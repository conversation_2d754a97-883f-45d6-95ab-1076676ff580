<!DOCTYPE html>
<html lang="en" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Australian Pay Calculator | Salary, Tax & Super Calculator</title>
    <meta name="description" content="Calculate your take-home pay and understand your tax obligations with our free Australian pay calculator. Features include instant tax calculations, superannuation calculator, and HELP/HECS debt repayments.">
    <link rel="stylesheet" href="styles.css">
</head>
<body class="min-h-screen bg-background font-sans antialiased">
    <!-- Promotion Banner -->
    <div class="bg-green-100/90 border-b border-green-200 text-green-600 dark:bg-green-900/20 dark:border-green-800 dark:text-green-400">
        <div class="container flex h-14 items-center justify-between px-4">
            <div class="flex items-center gap-x-2">
                <span class="text-sm">
                    <a class="font-semibold text-green-600 dark:text-green-400 hover:underline" href="https://paycal.com.au/ev-ice-novated-lease-calculator/">EV & ICE Novated Lease Calculator</a>
                    <span class="hidden sm:inline"> - Compare EV vs ICE savings with FBT exemptions!</span>
                </span>
            </div>
            <div class="flex items-center gap-x-1 sm:gap-x-2">
                <div class="hidden sm:flex items-center gap-x-1 mr-2">
                    <button class="w-2 h-2 rounded-full transition-colors bg-gray-300 dark:bg-gray-600" aria-label="Show promotion 1"></button>
                    <button class="w-2 h-2 rounded-full transition-colors bg-green-600 dark:text-green-400" aria-label="Show promotion 2"></button>
                </div>
                <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-8 w-8 sm:hidden hover:bg-green-200/70 dark:hover:bg-green-800/30">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                        <path d="m15 18-6-6 6-6"></path>
                    </svg>
                    <span class="sr-only">Previous promotion</span>
                </button>
                <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-8 w-8 sm:hidden hover:bg-green-200/70 dark:hover:bg-green-800/30">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                        <path d="m9 18 6-6-6-6"></path>
                    </svg>
                    <span class="sr-only">Next promotion</span>
                </button>
                <a class="items-center justify-center text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-9 rounded-md px-3 hidden sm:inline-flex border-green-300 dark:border-green-700 hover:bg-green-200/70 dark:hover:bg-green-800/30" href="https://paycal.com.au/ev-ice-novated-lease-calculator/">Try Now</a>
                <a class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-8 w-8 sm:hidden border-green-300 dark:border-green-700 hover:bg-green-200/70 dark:hover:bg-green-800/30" href="https://paycal.com.au/ev-ice-novated-lease-calculator/">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                        <path d="M5 12h14"></path>
                        <path d="m12 5 7 7-7 7"></path>
                    </svg>
                    <span class="sr-only">Try EV & ICE Novated Lease Calculator</span>
                </a>
                <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-8 w-8 hover:bg-green-200/70 dark:hover:bg-green-800/30">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                        <path d="M18 6 6 18"></path>
                        <path d="m6 6 12 12"></path>
                    </svg>
                    <span class="sr-only">Dismiss</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Header -->
    <header class="supports-backdrop-blur:bg-background/60 sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur">
        <div class="container flex h-14 items-center">
            <button class="inline-flex items-center justify-center rounded-md font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-10 py-2 mr-2 px-0 text-base hover:bg-transparent focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="radix-:Rkqla:" data-state="closed">
                <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5">
                    <path d="M8 2H13.5C13.7761 2 14 2.22386 14 2.5V12.5C14 12.7761 13.7761 13 13.5 13H8V2ZM7 2H1.5C1.22386 2 1 2.22386 1 2.5V12.5C1 12.7761 1.22386 13 1.5 13H7V2ZM0 2.5C0 1.67157 0.671573 1 1.5 1H13.5C14.3284 1 15 1.67157 15 2.5V12.5C15 13.3284 14.3284 14 13.5 14H1.5C0.671573 14 0 13.3284 0 12.5V2.5Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path>
                </svg>
                <span class="sr-only">Toggle Menu</span>
            </button>
            <div class="mr-4 hidden md:flex">
                <nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex max-w-max flex-1 items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256" class="h-6 w-6 dark:text-white" aria-hidden="true">
                        <path d="M0 0h256v256H0z" fill="none"></path>
                        <rect width="256" height="256" fill="none" stroke="currentColor" stroke-width="16"></rect>
                        <rect x="32" y="32" width="192" height="64" fill="currentColor"></rect>
                        <circle cx="64" cy="128" r="16" fill="currentColor"></circle>
                        <circle cx="128" cy="128" r="16" fill="currentColor"></circle>
                        <circle cx="192" cy="128" r="16" fill="currentColor"></circle>
                        <circle cx="64" cy="176" r="16" fill="currentColor"></circle>
                        <circle cx="128" cy="176" r="16" fill="currentColor"></circle>
                        <circle cx="192" cy="176" r="16" fill="currentColor"></circle>
                        <circle cx="64" cy="224" r="16" fill="currentColor"></circle>
                        <circle cx="128" cy="224" r="16" fill="currentColor"></circle>
                        <circle cx="192" cy="224" r="16" fill="currentColor"></circle>
                    </svg>
                    <div style="position:relative">
                        <ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr">
                            <li><a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="https://paycal.com.au/" data-radix-collection-item="">Paycal.com.au</a></li>
                            <li><button id="radix-:R14qla:-trigger-radix-:R574qla:" data-state="closed" aria-expanded="false" aria-controls="radix-:R14qla:-content-radix-:R574qla:" class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger" data-radix-collection-item="">Pay Calculators<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li>
                            <li><button id="radix-:R14qla:-trigger-radix-:R774qla:" data-state="closed" aria-expanded="false" aria-controls="radix-:R14qla:-content-radix-:R774qla:" class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger" data-radix-collection-item="">Home & Property Calculators<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li>
                            <li><button id="radix-:R14qla:-trigger-radix-:R974qla:" data-state="closed" aria-expanded="false" aria-controls="radix-:R14qla:-content-radix-:R974qla:" class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger" data-radix-collection-item="">Tax Calculators<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li>
                            <li><button id="radix-:R14qla:-trigger-radix-:Rb74qla:" data-state="closed" aria-expanded="false" aria-controls="radix-:R14qla:-content-radix-:Rb74qla:" class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger" data-radix-collection-item="">Financial Tools<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li>
                            <li><button id="radix-:R14qla:-trigger-radix-:Rd74qla:" data-state="closed" aria-expanded="false" aria-controls="radix-:R14qla:-content-radix-:Rd74qla:" class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger" data-radix-collection-item="">Vehicle Calculators<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li>
                            <li><button id="radix-:R14qla:-trigger-radix-:Rf74qla:" data-state="closed" aria-expanded="false" aria-controls="radix-:R14qla:-content-radix-:Rf74qla:" class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger" data-radix-collection-item="">Holidays Calculators<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li>
                            <li><a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="https://paycal.com.au/blogs/" data-radix-collection-item="">Blogs</a></li>
                            <li><a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="https://paycal.com.au/recommended/" data-radix-collection-item="">Recommended</a></li>
                        </ul>
                    </div>
                    <div class="absolute left-0 top-full flex justify-center">
                        <!-- Pay Calculators Dropdown -->
                        <div id="radix-:R14qla:-content-radix-:R574qla:" class="dropdown-content hidden absolute top-full left-0 mt-2 w-64 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg z-50">
                            <div class="p-4">
                                <h3 class="font-semibold text-sm mb-3">Pay Calculators</h3>
                                <div class="space-y-2">
                                    <a href="https://paycal.com.au/" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-primary">Australian Pay Calculator</a>
                                    <a href="https://paycal.com.au/contractor-pay-calculator/" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-primary">Contractor Pay Calculator</a>
                                    <a href="https://paycal.com.au/salary-rates/" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-primary">Salary Rate Benchmarking</a>
                                    <a href="https://paycal.com.au/contract-rates/" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-primary">Contract Rate Benchmarking</a>
                                </div>
                            </div>
                        </div>

                        <!-- Home & Property Calculators Dropdown -->
                        <div id="radix-:R14qla:-content-radix-:R774qla:" class="dropdown-content hidden absolute top-full left-0 mt-2 w-64 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg z-50">
                            <div class="p-4">
                                <h3 class="font-semibold text-sm mb-3">Home & Property Calculators</h3>
                                <div class="space-y-2">
                                    <a href="https://paycal.com.au/mortgage-calculator/" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-primary">Mortgage Calculator</a>
                                    <a href="https://paycal.com.au/home-loan-comparison/" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-primary">Home Loan Comparison</a>
                                    <a href="https://paycal.com.au/rate-cut-calculator/" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-primary">Rate Cut Calculator</a>
                                    <a href="https://paycal.com.au/mortgage-payoff-calculator/" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-primary">Mortgage Payoff Calculator</a>
                                </div>
                            </div>
                        </div>

                        <!-- Tax Calculators Dropdown -->
                        <div id="radix-:R14qla:-content-radix-:R974qla:" class="dropdown-content hidden absolute top-full left-0 mt-2 w-64 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg z-50">
                            <div class="p-4">
                                <h3 class="font-semibold text-sm mb-3">Tax Calculators</h3>
                                <div class="space-y-2">
                                    <a href="https://paycal.com.au/tax-cut-calculator/" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-primary">Tax Cut Calculator</a>
                                    <a href="https://paycal.com.au/tax-calendar/" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-primary">Tax Calendar</a>
                                    <a href="https://paycal.com.au/gst-calculator/" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-primary">GST Calculator</a>
                                </div>
                            </div>
                        </div>

                        <!-- Financial Tools Dropdown -->
                        <div id="radix-:R14qla:-content-radix-:Rb74qla:" class="dropdown-content hidden absolute top-full left-0 mt-2 w-64 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg z-50">
                            <div class="p-4">
                                <h3 class="font-semibold text-sm mb-3">Financial Tools</h3>
                                <div class="space-y-2">
                                    <a href="https://paycal.com.au/investment-calculator/" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-primary">Investment Calculator</a>
                                    <a href="https://paycal.com.au/loan-calculator/" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-primary">Loan Calculator</a>
                                    <a href="https://paycal.com.au/savings-calculator/" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-primary">Savings Calculator</a>
                                </div>
                            </div>
                        </div>

                        <!-- Vehicle Calculators Dropdown -->
                        <div id="radix-:R14qla:-content-radix-:Rd74qla:" class="dropdown-content hidden absolute top-full left-0 mt-2 w-64 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg z-50">
                            <div class="p-4">
                                <h3 class="font-semibold text-sm mb-3">Vehicle Calculators</h3>
                                <div class="space-y-2">
                                    <a href="https://paycal.com.au/ev-ice-novated-lease-calculator/" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-primary">EV & ICE Novated Lease Calculator</a>
                                    <a href="https://paycal.com.au/car-loan-calculator/" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-primary">Car Loan Calculator</a>
                                    <a href="https://paycal.com.au/fuel-cost-calculator/" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-primary">Fuel Cost Calculator</a>
                                </div>
                            </div>
                        </div>

                        <!-- Holidays Calculators Dropdown -->
                        <div id="radix-:R14qla:-content-radix-:Rf74qla:" class="dropdown-content hidden absolute top-full left-0 mt-2 w-64 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg z-50">
                            <div class="p-4">
                                <h3 class="font-semibold text-sm mb-3">Holidays Calculators</h3>
                                <div class="space-y-2">
                                    <a href="https://paycal.com.au/annual-leave-calculator/" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-primary">Annual Leave Calculator</a>
                                    <a href="https://paycal.com.au/long-service-leave-calculator/" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-primary">Long Service Leave Calculator</a>
                                    <a href="https://paycal.com.au/sick-leave-calculator/" class="block text-sm text-gray-600 dark:text-gray-300 hover:text-primary">Sick Leave Calculator</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </nav>
            </div>
            <div class="container flex h-16 items-center space-x-4 sm:justify-between sm:space-x-0">
                <div class="flex flex-1 items-center justify-end space-x-4">
                    <nav class="flex items-center space-x-1">
                        <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 w-10">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-[1.5rem] w-[1.3rem] dark:hidden">
                                <circle cx="12" cy="12" r="4"></circle>
                                <path d="M12 2v2"></path>
                                <path d="M12 20v2"></path>
                                <path d="m4.93 4.93 1.41 1.41"></path>
                                <path d="m17.66 17.66 1.41 1.41"></path>
                                <path d="M2 12h2"></path>
                                <path d="M20 12h2"></path>
                                <path d="m6.34 17.66-1.41 1.41"></path>
                                <path d="m19.07 4.93-1.41 1.41"></path>
                            </svg>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="hidden h-5 w-5 dark:block dark:text-white">
                                <path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"></path>
                            </svg>
                            <span class="sr-only">Toggle theme</span>
                        </button>
                    </nav>
                </div>
            </div>
        </div>
    </header>

    <div class="flex-1">
        <main class="flex flex-col items-center justify-between">
            <div class="container relative">
                <div class="flex flex-col md:flex-row md:gap-8">
                    <div class="flex-1">
                        <section class="flex flex-col items-start gap-2 px-4 pt-8 md:pt-12 pb-8">
                            <a class="group inline-flex items-center rounded-lg text-sm font-medium transition-colors mb-6 hover:text-primary" href="https://paycal.com.au/contractor-pay-calculator/">
                                <span class="mr-2">💼</span>Looking for contractor rates? Try our Contractor Calculator
                                <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" class="ml-1 h-4 w-4 group-hover:translate-x-0.5 transition-transform">
                                    <path d="M8.14645 3.14645C8.34171 2.95118 8.65829 2.95118 8.85355 3.14645L12.8536 7.14645C13.0488 7.34171 13.0488 7.65829 12.8536 7.85355L8.85355 11.8536C8.65829 12.0488 8.34171 12.0488 8.14645 11.8536C7.95118 11.6583 7.95118 11.3417 8.14645 11.1464L11.2929 8H2.5C2.22386 8 2 7.77614 2 7.5C2 7.22386 2.22386 7 2.5 7H11.2929L8.14645 3.85355C7.95118 3.65829 7.95118 3.34171 8.14645 3.14645Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path>
                                </svg>
                            </a>
                            <h1 class="text-3xl font-bold leading-tight tracking-tighter md:text-5xl lg:leading-[1.1]">Australian Pay Calculator</h1>
                            <span class="max-w-[750px] text-lg text-muted-foreground sm:text-xl" data-br=":R6jb6qla:" data-brr="1" style="display:inline-block;vertical-align:top;text-decoration:inherit;text-wrap:balance">Calculate your take-home pay and understand your tax obligations</span>
                        </section>
                    </div>
                    <div class="mb-6 md:mb-0 md:w-1/3">
                        <div class="flex items-center justify-center h-full">
                            <a target="_blank" rel="noopener noreferrer" class="block no-underline w-full" href="https://ko-fi.com/paycal">
                                <div class="rounded-lg border bg-card text-card-foreground shadow-sm group relative overflow-hidden transition-all hover:shadow-lg hover:shadow-orange-100 border-orange-100 hover:border-orange-200">
                                    <div class="p-6">
                                        <div class="flex items-start gap-4">
                                            <div class="relative h-12 w-12 flex-none rounded-full bg-gradient-to-br from-orange-100 to-yellow-100 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                                <div class="relative">
                                                    <img alt="Ko-fi" loading="lazy" width="32" height="32" decoding="async" data-nimg="1" class="rounded-full" style="color:transparent" src="https://paycal.com.au/tip-kofi-alt.gif">
                                                </div>
                                            </div>
                                            <div class="space-y-2 pr-8 flex-1">
                                                <div class="space-y-1">
                                                    <h3 class="font-semibold leading-none tracking-tight text-gray-900 dark:text-gray-100">Thank you for using PayCal! 🙏</h3>
                                                    <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 hover:bg-secondary/80 text-xs bg-orange-50 text-orange-700 border-orange-200">Growing community of supporters</div>
                                                </div>
                                                <p class="text-sm text-muted-foreground leading-relaxed">Your calculation just saved you time and stress. A coffee helps us help more Australians like you.</p>
                                            </div>
                                        </div>
                                        <div class="mt-4 bg-gradient-to-r from-orange-50 to-yellow-50 p-3 rounded-lg border border-orange-100">
                                            <div class="flex items-center justify-between mb-2">
                                                <span class="text-xs font-medium text-orange-700">Monthly Goal</span>
                                                <span class="text-xs text-orange-600">In Progress</span>
                                            </div>
                                            <p class="text-xs text-orange-700">Keep servers running & add EV Novated Lease Calculator ✨</p>
                                        </div>
                                        <div class="mt-4 flex items-center justify-between">
                                            <button class="inline-flex items-center rounded-full bg-gradient-to-r from-orange-500 to-yellow-500 hover:from-orange-600 hover:to-yellow-600 px-4 py-2 text-sm font-semibold text-white transition-all hover:scale-105 active:scale-95 shadow-md hover:shadow-lg">
                                                <span class="mr-2">☕</span>Buy me a latte
                                                <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" class="ml-2 h-4 w-4 group-hover:animate-pulse">
                                                    <path d="M8.14645 3.14645C8.34171 2.95118 8.65829 2.95118 8.85355 3.14645L12.8536 7.14645C13.0488 7.34171 13.0488 7.65829 12.8536 7.85355L8.85355 11.8536C8.65829 12.0488 8.34171 12.0488 8.14645 11.8536C7.95118 11.6583 7.95118 11.3417 8.14645 11.1464L11.2929 8H2.5C2.22386 8 2 7.77614 2 7.5C2 7.22386 2.22386 7 2.5 7H11.2929L8.14645 3.85355C7.95118 3.65829 7.95118 3.34171 8.14645 3.14645Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path>
                                                </svg>
                                            </button>
                                            <div class="text-xs text-muted-foreground">💚 From $5 AUD</div>
                                        </div>
                                    </div>
                                    <div class="absolute inset-x-0 -bottom-px h-px bg-gradient-to-r from-transparent via-orange-300/50 to-transparent group-hover:via-orange-400/70 transition-all"></div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>

                <section>
                    <div class="flex flex-wrap justify-center items-center w-full gap-4 flex-grow overflow-hidden rounded-[0.5rem] p-4 sm:p-8 border bg-background shadow">
                        <div class="flex flex-col gap-4 w-full max-w-screen-xl">
                            <div role="alert" class="relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground bg-background text-foreground mb-6">
                                <h5 class="mb-1 font-medium leading-none tracking-tight">💡 Looking for market rates?</h5>
                                <div class="text-sm [&_p]:leading-relaxed">Check out our <a class="font-medium underline underline-offset-4" href="https://paycal.com.au/salary-rates/">Salary Rates</a> page to compare and share salary rates across different industries in Australia.</div>
                            </div>

                            <div class="flex flex-col md:flex-row gap-4">
                                <div class="md:w-1/3 flex flex-col gap-4">
                                    <div class="space-y-4">
                                        <div class="px-4 md:hidden">
                                            <h3 class="text-lg font-semibold">Your pay</h3>
                                            <p class="text-sm text-muted-foreground">Enter your income details</p>
                                        </div>

                                        <div class="md:hidden px-4">
                                            <form class="grid gap-6">
                                                <div class="flex flex-col space-y-1.5">
                                                    <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="salary">Salary</label>
                                                    <div class="flex border h-10 w-full rounded-md border-input bg-background">
                                                        <span class="flex items-center px-3 h-10">$</span>
                                                        <input type="number" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-l-none m-[-1]" id="salary" min="0" placeholder="Enter your salary" value="50000">
                                                    </div>
                                                </div>

                                                <div class="flex flex-col space-y-1.5">
                                                    <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="paycycle">Time Period</label>
                                                    <button type="button" role="combobox" aria-controls="radix-:R2ab5b6qla:" aria-expanded="false" aria-autocomplete="none" dir="ltr" data-state="closed" class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" id="paycycle" aria-label="Select pay cycle">
                                                        <span style="pointer-events:none">Annually</span>
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 opacity-50" aria-hidden="true">
                                                            <path d="m6 9 6 6 6-6"></path>
                                                        </svg>
                                                    </button>
                                                </div>

                                                <div class="flex flex-col space-y-1.5">
                                                    <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="superRate">Super Rate %</label>
                                                    <input type="number" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" id="superRate" placeholder="Enter super rate (e.g., 9.5 for 9.5%)" value="11.5">
                                                </div>

                                                <div class="flex flex-col space-y-1.5">
                                                    <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="fiscalYear">Tax Year</label>
                                                    <button type="button" role="combobox" aria-controls="radix-:R2ib5b6qla:" aria-expanded="false" aria-autocomplete="none" dir="ltr" data-state="closed" class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" id="fiscalYear" aria-label="Select fiscal year">
                                                        <span style="pointer-events:none">2024-2025</span>
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 opacity-50" aria-hidden="true">
                                                            <path d="m6 9 6 6 6-6"></path>
                                                        </svg>
                                                    </button>
                                                </div>
                                            </form>
                                        </div>

                                        <div class="hidden md:block">
                                            <div class="rounded-lg border bg-card text-card-foreground shadow-sm m-2 flex-1">
                                                <div class="flex flex-col space-y-1.5 p-6">
                                                    <h3 class="text-2xl font-semibold leading-none tracking-tight">Your pay</h3>
                                                    <p class="text-sm text-muted-foreground">Enter your income details</p>
                                                </div>
                                                <div class="p-6 pt-0">
                                                    <form class="grid gap-6">
                                                        <div class="flex flex-col space-y-1.5">
                                                            <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="salary-desktop">Salary</label>
                                                            <div class="flex border h-10 w-full rounded-md border-input bg-background">
                                                                <span class="flex items-center px-3 h-10">$</span>
                                                                <input type="number" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-l-none m-[-1]" id="salary-desktop" min="0" placeholder="Enter your salary" value="50000">
                                                            </div>
                                                        </div>

                                                        <div class="flex flex-col space-y-1.5">
                                                            <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="paycycle-desktop">Time Period</label>
                                                            <button type="button" role="combobox" aria-controls="radix-:R9bb5b6qla:" aria-expanded="false" aria-autocomplete="none" dir="ltr" data-state="closed" class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" id="paycycle-desktop" aria-label="Select pay cycle">
                                                                <span style="pointer-events:none">Annually</span>
                                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 opacity-50" aria-hidden="true">
                                                                    <path d="m6 9 6 6 6-6"></path>
                                                                </svg>
                                                            </button>
                                                        </div>

                                                        <div class="flex flex-col space-y-1.5">
                                                            <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="superRate-desktop">Super Rate %</label>
                                                            <input type="number" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" id="superRate-desktop" placeholder="Enter super rate (e.g., 9.5 for 9.5%)" value="11.5">
                                                        </div>

                                                        <div class="flex flex-col space-y-1.5">
                                                            <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="fiscalYear-desktop">Tax Year</label>
                                                            <button type="button" role="combobox" aria-controls="radix-:Rabb5b6qla:" aria-expanded="false" aria-autocomplete="none" dir="ltr" data-state="closed" class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" id="fiscalYear-desktop" aria-label="Select fiscal year">
                                                                <span style="pointer-events:none">2024-2025</span>
                                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 opacity-50" aria-hidden="true">
                                                                    <path d="m6 9 6 6 6-6"></path>
                                                                </svg>
                                                            </button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="space-y-4">
                                        <div class="px-4 md:hidden">
                                            <h3 class="text-lg font-semibold">Options</h3>
                                        </div>

                                        <div class="md:hidden px-4 grid gap-6">
                                            <div class="flex items-center justify-between space-x-2">
                                                <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex flex-col space-y-1" for="includesSuper">
                                                    <span>Includes Superannuation</span>
                                                </label>
                                                <button type="button" role="switch" aria-checked="false" data-state="unchecked" value="on" class="peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input" id="includesSuper" aria-label="Includes Superannuation">
                                                    <span data-state="unchecked" class="pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0"></span>
                                                </button>
                                            </div>

                                            <div class="flex items-center justify-between space-x-2">
                                                <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex flex-col space-y-1" for="noPrivateHospitalCover">
                                                    <span>No Private Hospital Cover</span>
                                                </label>
                                                <button type="button" role="switch" aria-checked="false" data-state="unchecked" value="on" class="peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input" id="noPrivateHospitalCover" aria-label="No Private Hospital Cover">
                                                    <span data-state="unchecked" class="pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0"></span>
                                                </button>
                                            </div>

                                            <div class="flex items-center justify-between space-x-2">
                                                <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex flex-col space-y-1" for="nonResident">
                                                    <span>Non-Resident</span>
                                                </label>
                                                <button type="button" role="switch" aria-checked="false" data-state="unchecked" value="on" class="peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input" id="nonResident" aria-label="Non-Resident">
                                                    <span data-state="unchecked" class="pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0"></span>
                                                </button>
                                            </div>

                                            <div class="flex items-center justify-between space-x-2">
                                                <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex flex-col space-y-1" for="workingHoliday">
                                                    <span>Working Holiday Visa</span>
                                                </label>
                                                <button type="button" role="switch" aria-checked="false" data-state="unchecked" value="on" class="peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input" id="workingHoliday" aria-label="Working Holiday Visa">
                                                    <span data-state="unchecked" class="pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0"></span>
                                                </button>
                                            </div>

                                            <div class="flex items-center justify-between space-x-2">
                                                <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex flex-col space-y-1" for="noTaxFree">
                                                    <span>No tax-free threshold</span>
                                                </label>
                                                <button type="button" role="switch" aria-checked="false" data-state="unchecked" value="on" class="peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input" id="noTaxFree" aria-label="No tax-free threshold">
                                                    <span data-state="unchecked" class="pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0"></span>
                                                </button>
                                            </div>

                                            <div class="flex items-center justify-between space-x-2">
                                                <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex flex-col space-y-1" for="studentLoan">
                                                    <span>Student Loan (HELP, VET, SSL, TSL, SFSS)</span>
                                                </label>
                                                <button type="button" role="switch" aria-checked="false" data-state="unchecked" value="on" class="peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input" id="studentLoan" aria-label="Student Loan (HELP, VET, SSL, TSL, SFSS)">
                                                    <span data-state="unchecked" class="pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0"></span>
                                                </button>
                                            </div>

                                            <div class="flex items-center justify-between space-x-2">
                                                <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex flex-col space-y-1" for="withholdTax">
                                                    <span>Withhold Tax Offsets</span>
                                                </label>
                                                <button type="button" role="switch" aria-checked="false" data-state="unchecked" value="on" class="peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input" id="withholdTax" aria-label="Withhold Tax Offsets">
                                                    <span data-state="unchecked" class="pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0"></span>
                                                </button>
                                            </div>
                                        </div>

                                        <div class="hidden md:block">
                                            <div class="rounded-lg border bg-card text-card-foreground shadow-sm m-2 flex-1">
                                                <div class="flex flex-col space-y-1.5 p-6">
                                                    <h3 class="text-2xl font-semibold leading-none tracking-tight">Options</h3>
                                                </div>
                                                <div class="p-6 pt-0 grid gap-6">
                                                    <div class="flex items-center justify-between space-x-2">
                                                        <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex flex-col space-y-1" for="includesSuper-desktop">
                                                            <span>Includes Superannuation</span>
                                                        </label>
                                                        <button type="button" role="switch" aria-checked="false" data-state="unchecked" value="on" class="peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input" id="includesSuper-desktop" aria-label="Includes Superannuation">
                                                            <span data-state="unchecked" class="pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0"></span>
                                                        </button>
                                                    </div>

                                                    <div class="flex items-center justify-between space-x-2">
                                                        <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex flex-col space-y-1" for="noPrivateHospitalCover-desktop">
                                                            <span>No Private Hospital Cover</span>
                                                        </label>
                                                        <button type="button" role="switch" aria-checked="false" data-state="unchecked" value="on" class="peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input" id="noPrivateHospitalCover-desktop" aria-label="No Private Hospital Cover">
                                                            <span data-state="unchecked" class="pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0"></span>
                                                        </button>
                                                    </div>

                                                    <div class="flex items-center justify-between space-x-2">
                                                        <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex flex-col space-y-1" for="nonResident-desktop">
                                                            <span>Non-Resident</span>
                                                        </label>
                                                        <button type="button" role="switch" aria-checked="false" data-state="unchecked" value="on" class="peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input" id="nonResident-desktop" aria-label="Non-Resident">
                                                            <span data-state="unchecked" class="pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0"></span>
                                                        </button>
                                                    </div>

                                                    <div class="flex items-center justify-between space-x-2">
                                                        <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex flex-col space-y-1" for="workingHoliday-desktop">
                                                            <span>Working Holiday Visa</span>
                                                        </label>
                                                        <button type="button" role="switch" aria-checked="false" data-state="unchecked" value="on" class="peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input" id="workingHoliday-desktop" aria-label="Working Holiday Visa">
                                                            <span data-state="unchecked" class="pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0"></span>
                                                        </button>
                                                    </div>

                                                    <div class="flex items-center justify-between space-x-2">
                                                        <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex flex-col space-y-1" for="noTaxFree-desktop">
                                                            <span>No tax-free threshold</span>
                                                        </label>
                                                        <button type="button" role="switch" aria-checked="false" data-state="unchecked" value="on" class="peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input" id="noTaxFree-desktop" aria-label="No tax-free threshold">
                                                            <span data-state="unchecked" class="pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0"></span>
                                                        </button>
                                                    </div>

                                                    <div class="flex items-center justify-between space-x-2">
                                                        <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex flex-col space-y-1" for="studentLoan-desktop">
                                                            <span>Student Loan (HELP, VET, SSL, TSL, SFSS)</span>
                                                        </label>
                                                        <button type="button" role="switch" aria-checked="false" data-state="unchecked" value="on" class="peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input" id="studentLoan-desktop" aria-label="Student Loan (HELP, VET, SSL, TSL, SFSS)">
                                                            <span data-state="unchecked" class="pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0"></span>
                                                        </button>
                                                    </div>

                                                    <div class="flex items-center justify-between space-x-2">
                                                        <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex flex-col space-y-1" for="withholdTax-desktop">
                                                            <span>Withhold Tax Offsets</span>
                                                        </label>
                                                        <button type="button" role="switch" aria-checked="false" data-state="unchecked" value="on" class="peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input" id="withholdTax-desktop" aria-label="Withhold Tax Offsets">
                                                            <span data-state="unchecked" class="pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0"></span>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="space-y-4 md:w-2/3">
                                        <div class="px-4 md:hidden">
                                            <h3 class="text-lg font-semibold">Summary</h3>
                                        </div>

                                        <div class="md:hidden px-4">
                                            <div class="mb-6 p-4 bg-primary/5 rounded-lg border border-primary/20 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                                                <div>
                                                    <h3 class="text-lg font-semibold mb-1">Submit Your Salary Anonymously</h3>
                                                    <p class="text-sm text-muted-foreground">Help others make informed decisions about their salaries</p>
                                                </div>
                                                <a class="w-full md:w-auto font-semibold whitespace-nowrap bg-primary text-primary-foreground hover:bg-primary/90 inline-flex items-center justify-center rounded-md text-sm ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 px-4 py-2" href="https://paycal.com.au/salary-rates/">Get paid, not played →</a>
                                            </div>

                                            <div class="w-full overflow-auto">
                                                <table class="w-full caption-bottom text-sm">
                                                    <thead class="[&_tr]:border-b">
                                                        <tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                                                            <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0">Component</th>
                                                            <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0">Weekly</th>
                                                            <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0">Fortnightly</th>
                                                            <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0">Monthly</th>
                                                            <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0">Annually</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody class="[&_tr:last-child]:border-0">
                                                        <tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted font-medium bg-muted/50">
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">Gross Income</td>
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$961.54</td>
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$1,923.08</td>
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$4,166.67</td>
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$50,000.00</td>
                                                        </tr>
                                                        <tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted font-medium bg-muted/50">
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">Superannuation</td>
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$110.58</td>
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$221.15</td>
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$479.17</td>
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$5,750.00</td>
                                                        </tr>
                                                        <tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted font-medium bg-muted/50">
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">Total Tax</td>
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$125.73</td>
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$251.46</td>
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$544.83</td>
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$6,538.00</td>
                                                        </tr>
                                                        <tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted border-l-2 border-l-muted">
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0 pl-6">── Income Tax</td>
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$111.31</td>
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$222.62</td>
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$482.33</td>
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$5,788.00</td>
                                                        </tr>
                                                        <tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted border-l-2 border-l-muted">
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0 pl-6">── Medicare Levy</td>
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$19.23</td>
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$38.46</td>
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$83.33</td>
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$1,000.00</td>
                                                        </tr>
                                                        <tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted border-l-2 border-l-muted">
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0 pl-6">── Medicare Levy Surcharge</td>
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$0.00</td>
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$0.00</td>
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$0.00</td>
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$0.00</td>
                                                        </tr>
                                                        <tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted border-l-2 border-l-muted">
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0 pl-6">── Tax Offsets</td>
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">-</td>
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">-</td>
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">-</td>
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">- $250.00</td>
                                                        </tr>
                                                        <tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted font-medium text-lg bg-primary/5">
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">Take Home Pay</td>
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$835.81</td>
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$1,671.62</td>
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$3,621.83</td>
                                                            <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$43,462.00</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>

                                            <div class="mt-8">
                                                <div class="flex justify-end items-center gap-4 mt-4">
                                                    <button class="justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2 flex items-center gap-2">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                                                            <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path>
                                                            <polyline points="14 2 14 8 20 8"></polyline>
                                                            <path d="M12 18v-6"></path>
                                                            <path d="m9 15 3 3 3-3"></path>
                                                        </svg>Export CSV
                                                    </button>
                                                    <div class="flex flex-wrap gap-2 w-full sm:inline-flex">
                                                        <button class="inline-flex items-center text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 flex-1 sm:flex-initial min-w-[120px] justify-center">
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 h-4 w-4">
                                                                <circle cx="18" cy="5" r="3"></circle>
                                                                <circle cx="6" cy="12" r="3"></circle>
                                                                <circle cx="18" cy="19" r="3"></circle>
                                                                <line x1="8.59" x2="15.42" y1="13.51" y2="17.49"></line>
                                                                <line x1="15.41" x2="8.59" y1="6.51" y2="10.49"></line>
                                                            </svg>Share
                                                        </button>
                                                        <button class="inline-flex items-center text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 flex-1 sm:flex-initial min-w-[120px] justify-center">
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 h-4 w-4">
                                                                <rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect>
                                                                <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path>
                                                            </svg>Copy
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>

                                            <p class="italic mt-4">*This salary and pay calculator shows estimates only</p>
                                        </div>

                                        <div class="hidden md:block">
                                            <div class="rounded-lg border bg-card text-card-foreground shadow-sm m-2 flex-1 flex flex-col">
                                                <div class="flex flex-col space-y-1.5 p-6">
                                                    <h3 class="text-2xl font-semibold leading-none tracking-tight">Summary</h3>
                                                </div>
                                                <div class="p-6 pt-0 flex-grow">
                                                    <div class="mb-6 p-4 bg-primary/5 rounded-lg border border-primary/20 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                                                        <div>
                                                            <h3 class="text-lg font-semibold mb-1">Submit Your Salary Anonymously</h3>
                                                            <p class="text-sm text-muted-foreground">Help others make informed decisions about their salaries</p>
                                                        </div>
                                                        <a class="w-full md:w-auto font-semibold whitespace-nowrap bg-primary text-primary-foreground hover:bg-primary/90 inline-flex items-center justify-center rounded-md text-sm ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 px-4 py-2" href="https://paycal.com.au/salary-rates/">Get paid, not played →</a>
                                                    </div>

                                                    <div class="w-full overflow-auto">
                                                        <table class="w-full caption-bottom text-sm">
                                                            <thead class="[&_tr]:border-b">
                                                                <tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                                                                    <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0">Component</th>
                                                                    <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0">Weekly</th>
                                                                    <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0">Fortnightly</th>
                                                                    <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0">Monthly</th>
                                                                    <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0">Annually</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody class="[&_tr:last-child]:border-0">
                                                                <tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted font-medium bg-muted/50">
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">Gross Income</td>
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$961.54</td>
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$1,923.08</td>
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$4,166.67</td>
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$50,000.00</td>
                                                                </tr>
                                                                <tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted font-medium bg-muted/50">
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">Superannuation</td>
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$110.58</td>
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$221.15</td>
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$479.17</td>
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$5,750.00</td>
                                                                </tr>
                                                                <tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted font-medium bg-muted/50">
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">Total Tax</td>
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$125.73</td>
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$251.46</td>
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$544.83</td>
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$6,538.00</td>
                                                                </tr>
                                                                <tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted border-l-2 border-l-muted">
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0 pl-6">── Income Tax</td>
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$111.31</td>
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$222.62</td>
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$482.33</td>
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$5,788.00</td>
                                                                </tr>
                                                                <tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted border-l-2 border-l-muted">
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0 pl-6">── Medicare Levy</td>
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$19.23</td>
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$38.46</td>
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$83.33</td>
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$1,000.00</td>
                                                                </tr>
                                                                <tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted border-l-2 border-l-muted">
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0 pl-6">── Medicare Levy Surcharge</td>
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$0.00</td>
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$0.00</td>
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$0.00</td>
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$0.00</td>
                                                                </tr>
                                                                <tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted border-l-2 border-l-muted">
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0 pl-6">── Tax Offsets</td>
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">-</td>
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">-</td>
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">-</td>
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">- $250.00</td>
                                                                </tr>
                                                                <tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted font-medium text-lg bg-primary/5">
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">Take Home Pay</td>
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$835.81</td>
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$1,671.62</td>
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$3,621.83</td>
                                                                    <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">$43,462.00</td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>

                                                    <div class="mt-8">
                                                        <div class="flex justify-end items-center gap-4 mt-4">
                                                            <button class="justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2 flex items-center gap-2">
                                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                                                                    <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path>
                                                                    <polyline points="14 2 14 8 20 8"></polyline>
                                                                    <path d="M12 18v-6"></path>
                                                                    <path d="m9 15 3 3 3-3"></path>
                                                                </svg>Export CSV
                                                            </button>
                                                            <div class="flex flex-wrap gap-2 w-full sm:inline-flex">
                                                                <button class="inline-flex items-center text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 flex-1 sm:flex-initial min-w-[120px] justify-center">
                                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 h-4 w-4">
                                                                        <circle cx="18" cy="5" r="3"></circle>
                                                                        <circle cx="6" cy="12" r="3"></circle>
                                                                        <circle cx="18" cy="19" r="3"></circle>
                                                                        <line x1="8.59" x2="15.42" y1="13.51" y2="17.49"></line>
                                                                        <line x1="15.41" x2="8.59" y1="6.51" y2="10.49"></line>
                                                                    </svg>Share
                                                                </button>
                                                                <button class="inline-flex items-center text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 flex-1 sm:flex-initial min-w-[120px] justify-center">
                                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 h-4 w-4">
                                                                        <rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect>
                                                                        <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path>
                                                                    </svg>Copy
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="flex items-center p-6 pt-0">
                                                    <p class="italic">*This salary and pay calculator shows estimates only</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="bg-primary/5 rounded-lg p-6 mb-8 border border-primary/20">
                                    <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
                                        <div class="flex-1">
                                            <h3 class="text-xl font-semibold mb-2">🌟 Help the Salary Community</h3>
                                            <p class="text-lg mb-4">Want to make better salary decisions? Join hundreds of professionals who are sharing their salaries anonymously. Your contribution helps others negotiate better compensation.</p>
                                            <div class="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                                                <a class="bg-primary text-primary-foreground hover:bg-primary/90 px-4 py-2 rounded-md whitespace-nowrap w-full sm:w-auto text-center font-semibold" href="https://paycal.com.au/salary-rates/?salary=50000&type=annually">Get paid, not played →</a>
                                                <span class="text-sm text-muted-foreground">It takes less than a minute!</span>
                                            </div>
                                        </div>
                                        <div class="hidden md:block ml-6 p-4 bg-background rounded-lg border">
                                            <div class="text-center">
                                                <div class="text-3xl font-bold mb-1">100+</div>
                                                <div class="text-sm text-muted-foreground">Salaries Shared</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <section class="mt-8 prose dark:prose-invert max-w-none">
                    <h2>Australian Pay Calculator</h2>
                    <p>Calculate your salary, wages, and take-home pay with our free Australian pay calculator. Features include:</p>
                    <ul>
                        <li>Instant tax calculations based on 2024-25 rates</li>
                        <li>Stage 3 tax cuts included</li>
                        <li>Superannuation calculator</li>
                        <li>HELP/HECS debt repayments</li>
                        <li>Medicare levy calculations</li>
                    </ul>
                    <div class="my-8 p-4 bg-muted rounded-lg">
                        <h3 class="mt-0">Are you a contractor?</h3>
                        <p>If you're working as a contractor, try our dedicated <a class="font-medium text-primary hover:underline" href="https://paycal.com.au/contractor-pay-calculator/">Contractor Pay Calculator</a> which includes:</p>
                        <ul>
                            <li>Hourly and daily rate calculations</li>
                            <li>GST considerations</li>
                            <li>Flexible superannuation options</li>
                            <li>Tax variations for contractors</li>
                        </ul>
                    </div>
                    <h3>How to Use the Pay Calculator</h3>
                    <p>Enter your salary details and instantly see your take-home pay, tax deductions, and superannuation contributions.</p>
                </section>
            </div>
        </main>
    </div>

    <footer class="border-t mt-24">
        <div class="container mx-auto px-6 py-12">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
                <div class="lg:col-span-2">
                    <a class="text-xl font-bold" href="https://paycal.com.au/">PayCal.com.au</a>
                    <p class="mt-4 text-sm text-muted-foreground">Australian calculators for tax, salary, and financial planning. Helping Aussies make informed financial decisions.</p>
                    <div class="mt-6">
                        <a href="mailto:<EMAIL>" class="text-sm text-muted-foreground hover:text-primary"><EMAIL></a>
                    </div>
                </div>
                <div>
                    <h3 class="text-sm font-semibold mb-4">Pay Calculators</h3>
                    <ul class="space-y-3">
                        <li><a class="text-sm text-muted-foreground hover:text-primary" href="https://paycal.com.au/">Australian Pay Calculator</a></li>
                        <li><a class="text-sm text-muted-foreground hover:text-primary" href="https://paycal.com.au/contractor-pay-calculator/">Contractor Pay Calculator</a></li>
                        <li><a class="text-sm text-muted-foreground hover:text-primary" href="https://paycal.com.au/contract-rates/">Contract Rate Benchmarking</a></li>
                        <li><a class="text-sm text-muted-foreground hover:text-primary" href="https://paycal.com.au/salary-rates/">Salary Rate Benchmarking</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-sm font-semibold mb-4">Property Calculators</h3>
                    <ul class="space-y-3">
                        <li><a class="text-sm text-muted-foreground hover:text-primary" href="https://paycal.com.au/mortgage-calculator/">Mortgage Calculator</a></li>
                        <li><a class="text-sm text-muted-foreground hover:text-primary" href="https://paycal.com.au/home-loan-comparison/">Home Loan Comparison</a></li>
                        <li><a class="text-sm text-muted-foreground hover:text-primary" href="https://paycal.com.au/rate-cut-calculator/">Rate Cut Calculator</a></li>
                        <li><a class="text-sm text-muted-foreground hover:text-primary" href="https://paycal.com.au/mortgage-payoff-calculator/">Mortgage Payoff Calculator</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-sm font-semibold mb-4">Tax Tools</h3>
                    <ul class="space-y-3">
                        <li><a class="text-sm text-muted-foreground hover:text-primary" href="https://paycal.com.au/tax-cut-calculator/">Tax Cut Calculator</a></li>
                        <li><a class="text-sm text-muted-foreground hover:text-primary" href="https://paycal.com.au/tax-calendar/">Tax Calendar</a></li>
                        <li><a class="text-sm text-muted-foreground hover:text-primary" href="https://paycal.com.au/gst-calculator/">GST Calculator</a></li>
                    </ul>
                </div>
            </div>
            <div class="mt-12 pt-8 border-t">
                <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                    <div class="flex items-center space-x-4">
                        <a class="text-sm text-muted-foreground hover:text-primary" href="https://paycal.com.au/privacy-policy/">Privacy Policy</a>
                        <span class="text-muted-foreground">•</span>
                        <a class="text-sm text-muted-foreground hover:text-primary" href="https://paycal.com.au/blogs/">Blog</a>
                        <span class="text-muted-foreground">•</span>
                        <a href="https://docs.google.com/forms/d/e/1FAIpQLSfrRSGpb8UsOLM_o_02sYU0RCOWZep8NDHW30frI6ccxu4OVw/viewform" target="_blank" rel="noopener noreferrer" class="text-sm text-muted-foreground hover:text-primary">Feedback</a>
                    </div>
                    <p class="text-sm text-muted-foreground">Made for Aussies ❤️ © 2025 PayCal</p>
                </div>
            </div>
        </div>
    </footer>

    <div class="fixed bottom-4 right-4 z-50">
        <button class="inline-flex items-center justify-center text-sm font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 bg-background/60 backdrop-blur-sm shadow-sm hover:shadow-md transition-all">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 h-4 w-4">
                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                <line x1="9" x2="15" y1="10" y2="10"></line>
                <line x1="12" x2="12" y1="7" y2="13"></line>
            </svg>Feedback
        </button>
    </div>

    <script src="script.js"></script>
</body>
</html>
