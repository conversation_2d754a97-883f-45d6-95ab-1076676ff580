<!DOCTYPE html>
<html lang="en" class="light" style="color-scheme: light;">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Financial Calculator Suite | Comprehensive Financial Tools</title>
    <meta name="description" content="Comprehensive financial calculator suite. Calculate salary, tax, mortgage, loan payments, investment returns, and more. Free tools to help you make informed financial decisions.">
    <meta name="keywords" content="financial calculator,salary calculator,tax calculator,mortgage calculator,loan calculator,investment calculator,financial planning tools,money calculator,budget calculator">
    <meta name="theme-color" media="(prefers-color-scheme: light)" content="white">
    <meta name="theme-color" media="(prefers-color-scheme: dark)" content="black">
    <meta name="creator" content="Financial Calculator Suite">
    <link rel="canonical" href="/">
    <meta property="og:title" content="Financial Calculator Suite | Comprehensive Financial Tools">
    <meta property="og:description" content="Comprehensive financial calculator suite. Calculate salary, tax, mortgage, loan payments, investment returns, and more. Free tools to help you make informed financial decisions.">
    <meta property="og:type" content="website">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Financial Calculator Suite | Comprehensive Financial Tools">
    <meta name="twitter:description" content="Comprehensive financial calculator suite. Calculate salary, tax, mortgage, loan payments, investment returns, and more. Free tools to help you make informed financial decisions.">
    <link rel="stylesheet" href="styles.css">
</head>
<body class="min-h-screen bg-background font-sans antialiased">
    <div class="relative flex min-h-screen flex-col">
        <!-- Promotion Banner -->
        <div class="bg-green-100/90 dark:bg-green-900/30 border-b border-green-200 dark:border-green-800/30 transition-colors duration-500">
            <div class="container mx-auto px-4 py-3">
                <div class="flex items-center justify-between gap-x-4">
                    <div class="flex items-center gap-x-2 flex-1">
                        <span class="hidden sm:inline">⚡ New!</span>
                        <span class="text-sm sm:text-base">Try our comprehensive <a class="font-semibold text-green-600 dark:text-green-400 hover:underline" href="#calculators">Financial Calculator Suite</a><span class="hidden sm:inline"> - All your financial calculations in one place!</span></span>
                    </div>
                    <div class="flex items-center gap-x-1 sm:gap-x-2">
                        <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-8 w-8 hover:bg-green-200/70 dark:hover:bg-green-800/30">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                                <path d="M18 6 6 18"></path>
                                <path d="m6 6 12 12"></path>
                            </svg>
                            <span class="sr-only">Dismiss</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Header -->
        <header class="supports-backdrop-blur:bg-background/60 sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur">
            <div class="container flex h-14 items-center">
                <div class="mr-4 hidden md:flex">
                    <nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex max-w-max flex-1 items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256" class="h-6 w-6 dark:text-white" aria-hidden="true">
                            <path d="M0 0h256v256H0z" fill="none"></path>
                            <rect width="256" height="256" fill="none" stroke="currentColor" stroke-width="16"></rect>
                            <rect x="32" y="32" width="192" height="64" fill="currentColor"></rect>
                            <circle cx="64" cy="128" r="16" fill="currentColor"></circle>
                            <circle cx="128" cy="128" r="16" fill="currentColor"></circle>
                            <circle cx="192" cy="128" r="16" fill="currentColor"></circle>
                            <circle cx="64" cy="176" r="16" fill="currentColor"></circle>
                            <circle cx="128" cy="176" r="16" fill="currentColor"></circle>
                            <circle cx="192" cy="176" r="16" fill="currentColor"></circle>
                            <circle cx="64" cy="224" r="16" fill="currentColor"></circle>
                            <circle cx="128" cy="224" r="16" fill="currentColor"></circle>
                            <circle cx="192" cy="224" r="16" fill="currentColor"></circle>
                        </svg>
                        <div style="position:relative">
                            <ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr">
                                <li><a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="#home">Financial Calculator Suite</a></li>
                                <li><a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="#calculators">Calculators</a></li>
                                <li><a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="#about">About</a></li>
                            </ul>
                        </div>
                    </nav>
                </div>
                <div class="container flex h-16 items-center space-x-4 sm:justify-between sm:space-x-0">
                    <div class="flex flex-1 items-center justify-end space-x-4">
                        <nav class="flex items-center space-x-1">
                            <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 w-10" onclick="toggleTheme()">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-[1.5rem] w-[1.3rem] dark:hidden">
                                    <circle cx="12" cy="12" r="4"></circle>
                                    <path d="M12 2v2"></path>
                                    <path d="M12 20v2"></path>
                                    <path d="m4.93 4.93 1.41 1.41"></path>
                                    <path d="m17.66 17.66 1.41 1.41"></path>
                                    <path d="M2 12h2"></path>
                                    <path d="M20 12h2"></path>
                                    <path d="m6.34 17.66-1.41 1.41"></path>
                                    <path d="m19.07 4.93-1.41 1.41"></path>
                                </svg>
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="hidden h-5 w-5 dark:block dark:text-white">
                                    <path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"></path>
                                </svg>
                                <span class="sr-only">Toggle theme</span>
                            </button>
                        </nav>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <div class="flex-1">
            <main class="flex flex-col items-center justify-between">
                <div class="container relative">
                    <div class="flex flex-col md:flex-row md:gap-8">
                        <div class="flex-1">
                            <section class="flex flex-col items-start gap-2 px-4 pt-8 md:pt-12 pb-8" id="home">
                                <h1 class="text-3xl font-bold leading-tight tracking-tighter md:text-5xl lg:leading-[1.1]">Financial Calculator Suite</h1>
                                <span class="max-w-[750px] text-lg text-muted-foreground sm:text-xl">
                                    Comprehensive financial tools to help you make informed decisions about your money, investments, and future
                                </span>
                            </section>
                        </div>
                    </div>

                    <!-- Calculator Section -->
                    <section id="calculators" class="px-4 py-8">
                        <div class="flex flex-wrap justify-center items-center w-full gap-4 flex-grow overflow-hidden rounded-[0.5rem] p-4 sm:p-8 border bg-background shadow">
                            <div class="flex flex-col md:flex-row gap-4 w-full max-w-screen-xl">
                                <!-- Calculator Input Panel -->
                                <div class="md:w-1/3 flex flex-col gap-4">
                                    <div class="rounded-lg border bg-card text-card-foreground shadow-sm m-2 flex-1">
                                        <div class="flex flex-col space-y-1.5 p-6">
                                            <h3 class="text-2xl font-semibold leading-none tracking-tight">Financial Calculator</h3>
                                            <p class="text-sm text-muted-foreground">Choose a calculator type and enter your details</p>
                                        </div>
                                        <div class="p-6 pt-0">
                                            <form class="grid gap-6" id="calculatorForm">
                                                <!-- Calculator Type Selection -->
                                                <div class="flex flex-col space-y-1.5">
                                                    <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="calculatorType">Calculator Type</label>
                                                    <select class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" id="calculatorType" onchange="switchCalculator()">
                                                        <option value="salary">Salary Calculator</option>
                                                        <option value="mortgage">Mortgage Calculator</option>
                                                        <option value="loan">Loan Calculator</option>
                                                        <option value="investment">Investment Calculator</option>
                                                        <option value="tax">Tax Calculator</option>
                                                    </select>
                                                </div>

                                                <!-- Salary Calculator Fields -->
                                                <div id="salaryFields" class="calculator-fields">
                                                    <div class="flex flex-col space-y-1.5">
                                                        <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="salary">Annual Salary</label>
                                                        <div class="flex border h-10 w-full rounded-md border-input bg-background">
                                                            <span class="flex items-center px-3 h-10">$</span>
                                                            <input type="number" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-l-none m-[-1]" id="salary" placeholder="Enter annual salary" value="50000">
                                                        </div>
                                                    </div>
                                                    <div class="flex flex-col space-y-1.5">
                                                        <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="payFrequency">Pay Frequency</label>
                                                        <select class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" id="payFrequency">
                                                            <option value="annually">Annually</option>
                                                            <option value="monthly">Monthly</option>
                                                            <option value="fortnightly">Fortnightly</option>
                                                            <option value="weekly">Weekly</option>
                                                        </select>
                                                    </div>
                                                    <div class="flex flex-col space-y-1.5">
                                                        <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="taxRate">Tax Rate (%)</label>
                                                        <input type="number" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" id="taxRate" placeholder="Enter tax rate" value="25" min="0" max="100">
                                                    </div>
                                                </div>

                                                <!-- Mortgage Calculator Fields -->
                                                <div id="mortgageFields" class="calculator-fields" style="display: none;">
                                                    <div class="flex flex-col space-y-1.5">
                                                        <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="loanAmount">Loan Amount</label>
                                                        <div class="flex border h-10 w-full rounded-md border-input bg-background">
                                                            <span class="flex items-center px-3 h-10">$</span>
                                                            <input type="number" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-l-none m-[-1]" id="loanAmount" placeholder="Enter loan amount" value="250000">
                                                        </div>
                                                    </div>
                                                    <div class="flex flex-col space-y-1.5">
                                                        <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="interestRate">Interest Rate (%)</label>
                                                        <input type="number" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" id="interestRate" placeholder="Enter interest rate" value="5.89" min="0" max="100" step="0.01">
                                                    </div>
                                                    <div class="flex flex-col space-y-1.5">
                                                        <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="loanTerm">Loan Term (years)</label>
                                                        <input type="number" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" id="loanTerm" placeholder="Enter loan term" value="30" min="1" max="50">
                                                    </div>
                                                </div>

                                                <!-- Loan Calculator Fields -->
                                                <div id="loanFields" class="calculator-fields" style="display: none;">
                                                    <div class="flex flex-col space-y-1.5">
                                                        <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="loanAmountLoan">Loan Amount</label>
                                                        <div class="flex border h-10 w-full rounded-md border-input bg-background">
                                                            <span class="flex items-center px-3 h-10">$</span>
                                                            <input type="number" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-l-none m-[-1]" id="loanAmountLoan" placeholder="Enter loan amount" value="10000">
                                                        </div>
                                                    </div>
                                                    <div class="flex flex-col space-y-1.5">
                                                        <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="interestRateLoan">Interest Rate (%)</label>
                                                        <input type="number" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" id="interestRateLoan" placeholder="Enter interest rate" value="8.5" min="0" max="100" step="0.01">
                                                    </div>
                                                    <div class="flex flex-col space-y-1.5">
                                                        <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="loanTermLoan">Loan Term (years)</label>
                                                        <input type="number" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" id="loanTermLoan" placeholder="Enter loan term" value="5" min="1" max="30">
                                                    </div>
                                                </div>

                                                <!-- Investment Calculator Fields -->
                                                <div id="investmentFields" class="calculator-fields" style="display: none;">
                                                    <div class="flex flex-col space-y-1.5">
                                                        <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="initialInvestment">Initial Investment</label>
                                                        <div class="flex border h-10 w-full rounded-md border-input bg-background">
                                                            <span class="flex items-center px-3 h-10">$</span>
                                                            <input type="number" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-l-none m-[-1]" id="initialInvestment" placeholder="Enter initial investment" value="10000">
                                                        </div>
                                                    </div>
                                                    <div class="flex flex-col space-y-1.5">
                                                        <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="monthlyContribution">Monthly Contribution</label>
                                                        <div class="flex border h-10 w-full rounded-md border-input bg-background">
                                                            <span class="flex items-center px-3 h-10">$</span>
                                                            <input type="number" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-l-none m-[-1]" id="monthlyContribution" placeholder="Enter monthly contribution" value="500">
                                                        </div>
                                                    </div>
                                                    <div class="flex flex-col space-y-1.5">
                                                        <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="annualReturn">Annual Return (%)</label>
                                                        <input type="number" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" id="annualReturn" placeholder="Enter annual return" value="7" min="0" max="100" step="0.1">
                                                    </div>
                                                    <div class="flex flex-col space-y-1.5">
                                                        <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="investmentYears">Investment Period (years)</label>
                                                        <input type="number" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" id="investmentYears" placeholder="Enter investment period" value="20" min="1" max="50">
                                                    </div>
                                                </div>

                                                <!-- Tax Calculator Fields -->
                                                <div id="taxFields" class="calculator-fields" style="display: none;">
                                                    <div class="flex flex-col space-y-1.5">
                                                        <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="taxableIncome">Taxable Income</label>
                                                        <div class="flex border h-10 w-full rounded-md border-input bg-background">
                                                            <span class="flex items-center px-3 h-10">$</span>
                                                            <input type="number" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-l-none m-[-1]" id="taxableIncome" placeholder="Enter taxable income" value="75000">
                                                        </div>
                                                    </div>
                                                    <div class="flex flex-col space-y-1.5">
                                                        <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="taxYear">Tax Year</label>
                                                        <select class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" id="taxYear">
                                                            <option value="2024-2025">2024-2025</option>
                                                            <option value="2023-2024">2023-2024</option>
                                                            <option value="2022-2023">2022-2023</option>
                                                        </select>
                                                    </div>
                                                    <div class="flex flex-col space-y-1.5">
                                                        <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="residencyStatus">Residency Status</label>
                                                        <select class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" id="residencyStatus">
                                                            <option value="resident">Australian Resident</option>
                                                            <option value="non-resident">Non-Resident</option>
                                                            <option value="working-holiday">Working Holiday Visa</option>
                                                        </select>
                                                    </div>
                                                </div>

                                                <!-- Calculate Button -->
                                                <button type="button" class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2" onclick="calculate()">
                                                    Calculate
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>

                                <!-- Results Panel -->
                                <div class="md:w-2/3 flex flex-col gap-4">
                                    <div class="rounded-lg border bg-card text-card-foreground shadow-sm m-2 flex-1">
                                        <div class="flex flex-col space-y-1.5 p-6">
                                            <h3 class="text-2xl font-semibold leading-none tracking-tight">Results</h3>
                                        </div>
                                        <div class="p-6 pt-0">
                                            <div id="results" class="w-full overflow-auto">
                                                <div class="text-center text-muted-foreground py-8">
                                                    Enter your details and click Calculate to see results
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Chart Panel -->
                                    <div class="rounded-lg border bg-card text-card-foreground shadow-sm m-2 flex-1">
                                        <div class="flex flex-col space-y-1.5 p-6">
                                            <h3 class="text-2xl font-semibold leading-none tracking-tight">Visual Analysis</h3>
                                        </div>
                                        <div class="p-6 pt-0">
                                            <div id="chartContainer" class="w-full h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                                                <canvas id="financialChart" width="400" height="200"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- Educational Content Section -->
                    <section id="about" class="px-4 py-8">
                        <div class="rounded-lg border bg-card text-card-foreground shadow-sm m-2 w-full flex-1 flex flex-col">
                            <div class="flex flex-col space-y-1.5 p-6">
                                <h3 class="text-2xl font-semibold leading-none tracking-tight">Understanding Financial Calculations</h3>
                            </div>
                            <div class="p-6 pt-0">
                                <div class="container mx-auto p-6">
                                    <div class="grid md:grid-cols-2 gap-6">
                                        <!-- Financial Planning Image -->
                                        <div class="space-y-4">
                                            <div class="bg-gradient-to-br from-blue-100 to-indigo-100 p-6 rounded-lg">
                                                <div class="w-full h-48 bg-gradient-to-r from-blue-200 to-indigo-200 rounded-lg flex items-center justify-center">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="text-blue-600">
                                                        <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/>
                                                    </svg>
                                                </div>
                                                <h4 class="text-lg font-semibold mt-4 text-blue-800">Smart Financial Planning</h4>
                                                <p class="text-blue-700 text-sm">Use our calculators to make informed decisions about your financial future.</p>
                                            </div>
                                        </div>

                                        <!-- Content -->
                                        <div class="space-y-4">
                                            <h4 class="text-xl font-semibold">Why Use Financial Calculators?</h4>
                                            <p class="text-lg mb-4">Financial calculators are essential tools for making informed decisions about your money. Whether you're planning for retirement, buying a home, or managing debt, these tools help you understand the long-term impact of your financial choices.</p>

                                            <h4 class="text-lg font-medium mt-4 mb-2">Key Benefits:</h4>
                                            <ul class="list-disc pl-5 mb-4 space-y-2">
                                                <li class="mb-2">Accurate calculations for complex financial scenarios</li>
                                                <li class="mb-2">Compare different options and strategies</li>
                                                <li class="mb-2">Plan for future financial goals</li>
                                                <li class="mb-2">Understand the impact of interest rates and time</li>
                                                <li class="mb-2">Make data-driven financial decisions</li>
                                            </ul>
                                        </div>
                                    </div>

                                    <!-- Calculator Types Grid -->
                                    <div class="mt-8 grid md:grid-cols-3 gap-4">
                                        <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                                            <div class="flex items-center mb-3">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-green-600 mr-2">
                                                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
                                                    <circle cx="9" cy="7" r="4"/>
                                                    <path d="m22 2-5 10-5-5 10-5z"/>
                                                </svg>
                                                <h5 class="font-semibold text-green-800">Salary Calculator</h5>
                                            </div>
                                            <p class="text-sm text-green-700">Calculate your take-home pay after taxes and understand your true earning potential.</p>
                                        </div>

                                        <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                                            <div class="flex items-center mb-3">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-blue-600 mr-2">
                                                    <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                                                    <polyline points="9,22 9,12 15,12 15,22"/>
                                                </svg>
                                                <h5 class="font-semibold text-blue-800">Mortgage Calculator</h5>
                                            </div>
                                            <p class="text-sm text-blue-700">Determine your monthly mortgage payments and total interest costs for home loans.</p>
                                        </div>

                                        <div class="bg-purple-50 p-4 rounded-lg border border-purple-200">
                                            <div class="flex items-center mb-3">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-purple-600 mr-2">
                                                    <line x1="12" y1="1" x2="12" y2="23"/>
                                                    <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/>
                                                </svg>
                                                <h5 class="font-semibold text-purple-800">Investment Calculator</h5>
                                            </div>
                                            <p class="text-sm text-purple-700">Project the growth of your investments with compound interest over time.</p>
                                        </div>
                                    </div>

                                    <!-- Financial Tips -->
                                    <div class="mt-8 bg-yellow-50 p-6 rounded-lg border border-yellow-200">
                                        <h4 class="text-lg font-semibold text-yellow-800 mb-3">💡 Financial Planning Tips</h4>
                                        <div class="grid md:grid-cols-2 gap-4 text-sm text-yellow-700">
                                            <div>
                                                <p class="mb-2"><strong>Start Early:</strong> The power of compound interest means starting early can significantly impact your financial future.</p>
                                                <p class="mb-2"><strong>Emergency Fund:</strong> Aim to save 3-6 months of expenses for unexpected situations.</p>
                                            </div>
                                            <div>
                                                <p class="mb-2"><strong>Diversify:</strong> Don't put all your eggs in one basket when it comes to investments.</p>
                                                <p class="mb-2"><strong>Regular Reviews:</strong> Review and adjust your financial plans regularly as your situation changes.</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
            </main>
        </div>
    </div>

    <script src="header-footer.js"></script>
    <script>
    // Mobile menu toggle
    function toggleMobileMenu() {
        const mobileMenu = document.getElementById('mobile-menu');
        mobileMenu.classList.toggle('hidden');
    }

    // Dismiss banner
    function dismissBanner() {
        const banner = document.querySelector('.bg-green-100\\/90');
        if (banner) {
            banner.style.display = 'none';
        }
    }
    </script>
</body>
</html>
