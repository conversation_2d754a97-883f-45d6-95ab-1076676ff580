<!DOCTYPE html>
<html lang="en" class="light" style="color-scheme: light;">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Australian Pay Calculator | Salary, Tax & Super Calculator</title>
    <meta name="description" content="Australia's most comprehensive financial calculator suite. Calculate salary, tax (including 2024 Stage 3 tax cuts), superannuation, GST, stamp duty, and contractor rates. Plus, access up-to-date school and public holiday calendars. Free tools to help you make informed financial decisions.">
    <link rel="author" href="https://paycal.com.au/">
    <meta name="author" content="PayCal Australia">
    <link rel="manifest" href="https://paycal.com.au/site.webmanifest">
    <meta name="keywords" content="pay calculator australia,australian tax calculator 2024,stage 3 tax cuts calculator,salary calculator australia,take home pay calculator,superannuation calculator,contractor rate calculator,gst calculator australia,stamp duty calculator,school holiday calendar 2024,public holidays 2024,australian tax rates 2024,payroll calculator australia,income tax estimator,wage calculator australia,financial planning tools,australian tax calculator,ato tax rates,australian super calculator,business tax tools">
    <meta name="theme-color" media="(prefers-color-scheme: light)" content="white">
    <meta name="theme-color" media="(prefers-color-scheme: dark)" content="black">
    <meta name="creator" content="PayCal Australia">
    <link rel="canonical" href="https://paycal.com.au/">
    <meta property="og:title" content="Australian Pay Calculator | Salary, Tax & Super Calculator">
    <meta property="og:description" content="Australia's most comprehensive financial calculator suite. Calculate salary, tax (including 2024 Stage 3 tax cuts), superannuation, GST, stamp duty, and contractor rates. Plus, access up-to-date school and public holiday calendars. Free tools to help you make informed financial decisions.">
    <meta property="og:url" content="https://paycal.com.au/">
    <meta property="og:site_name" content="Australian Pay Calculator | Salary, Tax & Super Calculator">
    <meta property="og:locale" content="en-AU">
    <meta property="og:image" content="https://paycal.com.au/og-image.png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:image:alt" content="Australian Pay Calculator | Salary, Tax & Super Calculator">
    <meta property="og:type" content="website">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:creator" content="@cht0001">
    <meta name="twitter:title" content="Australian Pay Calculator | Salary, Tax & Super Calculator">
    <meta name="twitter:description" content="Australia's most comprehensive financial calculator suite. Calculate salary, tax (including 2024 Stage 3 tax cuts), superannuation, GST, stamp duty, and contractor rates. Plus, access up-to-date school and public holiday calendars. Free tools to help you make informed financial decisions.">
    <meta name="twitter:image" content="https://paycal.com.au/og-image.png">
    <link rel="icon" href="https://paycal.com.au/favicon.ico" type="image/x-icon" sizes="48x48">
    <link rel="icon" href="https://paycal.com.au/favicon.ico" sizes="any">
    <link rel="apple-touch-icon" href="https://paycal.com.au/apple-touch-icon.png">
    <link rel="manifest" href="https://paycal.com.au/site.webmanifest">
    <link rel="stylesheet" href="styles.css">
</head>
<body class="min-h-screen bg-background font-sans antialiased __variable_e8ce0c" aria-hidden="false">
    <div class="relative flex min-h-screen flex-col">
        <!-- Promotion Banner -->
        <div class="bg-green-100/90 dark:bg-green-900/30 border-b border-green-200 dark:border-green-800/30 transition-colors duration-500">
            <div class="container mx-auto px-4 py-3">
                <div class="flex items-center justify-between gap-x-4">
                    <div class="flex items-center gap-x-2 flex-1">
                        <span class="hidden sm:inline">⚡ New!</span>
                        <span class="text-sm sm:text-base">Try our free <a class="font-semibold text-green-600 dark:text-green-400 hover:underline" href="https://paycal.com.au/ev-ice-novated-lease-calculator/">EV & ICE Novated Lease Calculator</a><span class="hidden sm:inline"> - Compare EV vs ICE savings with FBT exemptions!</span></span>
                    </div>
                    <div class="flex items-center gap-x-1 sm:gap-x-2">
                        <div class="hidden sm:flex items-center gap-x-1 mr-2">
                            <button class="w-2 h-2 rounded-full transition-colors bg-gray-300 dark:bg-gray-600" aria-label="Show promotion 1"></button>
                            <button class="w-2 h-2 rounded-full transition-colors bg-green-600 dark:text-green-400" aria-label="Show promotion 2"></button>
                        </div>
                        <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-8 w-8 sm:hidden hover:bg-green-200/70 dark:hover:bg-green-800/30">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                                <path d="m15 18-6-6 6-6"></path>
                            </svg>
                            <span class="sr-only">Previous promotion</span>
                        </button>
                        <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-8 w-8 sm:hidden hover:bg-green-200/70 dark:hover:bg-green-800/30">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                                <path d="m9 18 6-6-6-6"></path>
                            </svg>
                            <span class="sr-only">Next promotion</span>
                        </button>
                        <a class="items-center justify-center text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-9 rounded-md px-3 hidden sm:inline-flex border-green-300 dark:border-green-700 hover:bg-green-200/70 dark:hover:bg-green-800/30" href="https://paycal.com.au/ev-ice-novated-lease-calculator/">Try Now</a>
                        <a class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-8 w-8 sm:hidden border-green-300 dark:border-green-700 hover:bg-green-200/70 dark:hover:bg-green-800/30" href="https://paycal.com.au/ev-ice-novated-lease-calculator/">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                                <path d="M5 12h14"></path>
                                <path d="m12 5 7 7-7 7"></path>
                            </svg>
                            <span class="sr-only">Try EV & ICE Novated Lease Calculator</span>
                        </a>
                        <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-8 w-8 hover:bg-green-200/70 dark:hover:bg-green-800/30" onclick="dismissBanner()">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                                <path d="M18 6 6 18"></path>
                                <path d="m6 6 12 12"></path>
                            </svg>
                            <span class="sr-only">Dismiss</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Header -->
        <header class="supports-backdrop-blur:bg-background/60 sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur">
            <div class="container flex h-14 items-center">
                <div class="mr-4 hidden md:flex">
                    <a class="mr-6 flex items-center space-x-2" href="/">
                        <span class="hidden font-bold sm:inline-block">PayCal</span>
                    </a>
                    <nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex max-w-max flex-1 items-center justify-center">
                        <div style="position:relative">
                            <ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr">
                                <li>
                                    <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" data-state="closed">
                                        Pay Calculators
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true">
                                            <path d="m6 9 6 6 6-6"></path>
                                        </svg>
                                    </button>
                                </li>
                                <li>
                                    <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" data-state="closed">
                                        Home & Property Calculators
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true">
                                            <path d="m6 9 6 6 6-6"></path>
                                        </svg>
                                    </button>
                                </li>
                                <li>
                                    <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" data-state="closed">
                                        Tax Calculators
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true">
                                            <path d="m6 9 6 6 6-6"></path>
                                        </svg>
                                    </button>
                                </li>
                                <li>
                                    <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" data-state="closed">
                                        Financial Tools
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true">
                                            <path d="m6 9 6 6 6-6"></path>
                                        </svg>
                                    </button>
                                </li>
                                <li>
                                    <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" data-state="closed">
                                        Vehicle Tools
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true">
                                            <path d="m6 9 6 6 6-6"></path>
                                        </svg>
                                    </button>
                                </li>
                            </ul>
                        </div>
                    </nav>
                </div>
                <button class="inline-flex items-center justify-center rounded-md font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10 md:hidden">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
                        <line x1="4" x2="20" y1="12" y2="12"></line>
                        <line x1="4" x2="20" y1="6" y2="6"></line>
                        <line x1="4" x2="20" y1="18" y2="18"></line>
                    </svg>
                    <span class="sr-only">Toggle Menu</span>
                </button>
                <div class="flex flex-1 items-center justify-between space-x-2 md:justify-end">
                    <div class="w-full flex-1 md:w-auto md:flex-none">
                        <button class="inline-flex items-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground px-4 py-2 relative h-8 w-full justify-start text-sm text-muted-foreground sm:pr-12 md:w-40 lg:w-64">
                            <span class="hidden lg:inline-flex">Search calculators...</span>
                            <span class="inline-flex lg:hidden">Search...</span>
                            <kbd class="pointer-events-none absolute right-[0.3rem] top-[0.3rem] hidden h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 sm:flex">
                                <span class="text-xs">⌘</span>K
                            </kbd>
                        </button>
                    </div>
                    <nav class="flex items-center">
                        <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 w-10">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0">
                                <circle cx="12" cy="12" r="4"></circle>
                                <path d="M12 2v2"></path>
                                <path d="M12 20v2"></path>
                                <path d="m4.93 4.93 1.41 1.41"></path>
                                <path d="m17.66 17.66 1.41 1.41"></path>
                                <path d="M2 12h2"></path>
                                <path d="M20 12h2"></path>
                                <path d="m6.34 17.66-1.41 1.41"></path>
                                <path d="m19.07 4.93-1.41 1.41"></path>
                            </svg>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100">
                                <path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"></path>
                            </svg>
                            <span class="sr-only">Toggle theme</span>
                        </button>
                    </nav>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <div class="flex-1">
            <main class="flex flex-col items-center justify-between">
                <div class="container relative">
                    <!-- Ko-fi Support Section -->
                    <div class="px-4 py-6">
                        <div class="bg-gradient-to-r from-orange-50 to-yellow-50 dark:from-orange-900/20 dark:to-yellow-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-6">
                            <div class="flex items-start space-x-4">
                                <div class="flex-shrink-0">
                                    <img src="https://paycal.com.au/tip-kofi-alt.gif" alt="Ko-fi" class="w-10 h-10">
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Thank you for using PayCal! 🙏</h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-300 mb-3">Growing community of supporters</p>
                                    <p class="text-sm text-gray-700 dark:text-gray-300 mb-4">Your calculation just saved you time and stress. A coffee helps us help more Australians like you.</p>
                                    <div class="flex flex-col sm:flex-row gap-2">
                                        <a href="https://ko-fi.com/paycal" target="_blank" class="inline-flex items-center px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white text-sm font-medium rounded-md transition-colors">
                                            ☕ Buy me a latte
                                        </a>
                                        <span class="text-sm text-gray-600 dark:text-gray-400 flex items-center">💚 From $5 AUD</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Header Section -->
                    <div class="flex flex-col md:flex-row md:gap-8">
                        <div class="flex-1">
                            <section class="flex flex-col items-start gap-2 px-4 pt-8 md:pt-12 pb-8">
                                <h1 class="text-3xl font-bold leading-tight tracking-tighter md:text-5xl lg:leading-[1.1] text-gray-900 dark:text-white">Australian Pay Calculator</h1>
                                <span class="max-w-[750px] text-lg text-muted-foreground sm:text-xl">
                                    Calculate your take-home pay and understand your tax obligations
                                </span>
                            </section>
                        </div>
                    </div>

                    <!-- Salary Rates Promotion -->
                    <div class="px-4 py-6">
                        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
                            <div class="flex items-center space-x-3 mb-3">
                                <span class="text-2xl">💡</span>
                                <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100">Looking for market rates?</h3>
                            </div>
                            <p class="text-blue-800 dark:text-blue-200 mb-4">Check out our <a href="https://paycal.com.au/salary-rates/" class="font-semibold underline hover:no-underline">Salary Rates</a> page to compare and share salary rates across different industries in Australia.</p>
                        </div>
                    </div>

                    <!-- Calculator Section -->
                    <section class="px-4 py-8">
                        <div class="flex flex-col lg:flex-row gap-8 max-w-7xl mx-auto">
                            <!-- Input Panel -->
                            <div class="lg:w-1/2">
                                <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                                    <!-- Desktop Input -->
                                    <div class="hidden md:block">
                                        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                                            <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Your pay</h3>
                                            <p class="text-sm text-gray-600 dark:text-gray-400">Enter your income details</p>
                                        </div>
                                        <div class="p-6 space-y-6">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Salary</label>
                                                <div class="relative">
                                                    <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                                                    <input type="number" id="salary" class="w-full pl-8 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500 dark:bg-gray-700 dark:text-white" placeholder="50000" value="50000">
                                                </div>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Time Period</label>
                                                <select id="timePeriod" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500 dark:bg-gray-700 dark:text-white">
                                                    <option value="annually">Annually</option>
                                                    <option value="monthly">Monthly</option>
                                                    <option value="fortnightly">Fortnightly</option>
                                                    <option value="weekly">Weekly</option>
                                                </select>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Super Rate %</label>
                                                <input type="number" id="superRate" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500 dark:bg-gray-700 dark:text-white" value="11.5" step="0.1" min="0" max="100">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Tax Year</label>
                                                <select id="taxYear" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500 dark:bg-gray-700 dark:text-white">
                                                    <option value="2024-25">2024-25</option>
                                                    <option value="2023-24">2023-24</option>
                                                    <option value="2022-23">2022-23</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Mobile Input -->
                                    <div class="md:hidden">
                                        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Your pay</h3>
                                            <p class="text-sm text-gray-600 dark:text-gray-400">Enter your income details</p>
                                        </div>
                                        <div class="p-4 space-y-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Salary</label>
                                                <div class="relative">
                                                    <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                                                    <input type="number" id="salaryMobile" class="w-full pl-8 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500 dark:bg-gray-700 dark:text-white" placeholder="50000" value="50000">
                                                </div>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Time Period</label>
                                                <select id="timePeriodMobile" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500 dark:bg-gray-700 dark:text-white">
                                                    <option value="annually">Annually</option>
                                                    <option value="monthly">Monthly</option>
                                                    <option value="fortnightly">Fortnightly</option>
                                                    <option value="weekly">Weekly</option>
                                                </select>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Super Rate %</label>
                                                <input type="number" id="superRateMobile" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500 dark:bg-gray-700 dark:text-white" value="11.5" step="0.1" min="0" max="100">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Tax Year</label>
                                                <select id="taxYearMobile" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500 dark:bg-gray-700 dark:text-white">
                                                    <option value="2024-25">2024-25</option>
                                                    <option value="2023-24">2023-24</option>
                                                    <option value="2022-23">2022-23</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Options Section -->
                                    <div class="border-t border-gray-200 dark:border-gray-700">
                                        <!-- Desktop Options -->
                                        <div class="hidden md:block">
                                            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                                                <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Options</h3>
                                            </div>
                                            <div class="p-6 space-y-4">
                                                <label class="flex items-center space-x-3">
                                                    <input type="checkbox" id="includesSuper" class="w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500">
                                                    <span class="text-sm text-gray-700 dark:text-gray-300">Includes Superannuation</span>
                                                </label>
                                                <label class="flex items-center space-x-3">
                                                    <input type="checkbox" id="noPrivateHealth" class="w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500">
                                                    <span class="text-sm text-gray-700 dark:text-gray-300">No Private Hospital Cover</span>
                                                </label>
                                                <label class="flex items-center space-x-3">
                                                    <input type="checkbox" id="nonResident" class="w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500">
                                                    <span class="text-sm text-gray-700 dark:text-gray-300">Non-Resident</span>
                                                </label>
                                                <label class="flex items-center space-x-3">
                                                    <input type="checkbox" id="workingHoliday" class="w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500">
                                                    <span class="text-sm text-gray-700 dark:text-gray-300">Working Holiday Visa</span>
                                                </label>
                                                <label class="flex items-center space-x-3">
                                                    <input type="checkbox" id="noTaxFreeThreshold" class="w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500">
                                                    <span class="text-sm text-gray-700 dark:text-gray-300">No tax-free threshold</span>
                                                </label>
                                                <label class="flex items-center space-x-3">
                                                    <input type="checkbox" id="studentLoan" class="w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500">
                                                    <span class="text-sm text-gray-700 dark:text-gray-300">Student Loan (HELP, VET, SSL, TSL, SFSS)</span>
                                                </label>
                                                <label class="flex items-center space-x-3">
                                                    <input type="checkbox" id="withholdTaxOffsets" class="w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500">
                                                    <span class="text-sm text-gray-700 dark:text-gray-300">Withhold Tax Offsets</span>
                                                </label>
                                            </div>
                                        </div>

                                        <!-- Mobile Options -->
                                        <div class="md:hidden">
                                            <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Options</h3>
                                            </div>
                                            <div class="p-4 space-y-3">
                                                <label class="flex items-center space-x-3">
                                                    <input type="checkbox" id="includesSuperMobile" class="w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500">
                                                    <span class="text-sm text-gray-700 dark:text-gray-300">Includes Superannuation</span>
                                                </label>
                                                <label class="flex items-center space-x-3">
                                                    <input type="checkbox" id="noPrivateHealthMobile" class="w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500">
                                                    <span class="text-sm text-gray-700 dark:text-gray-300">No Private Hospital Cover</span>
                                                </label>
                                                <label class="flex items-center space-x-3">
                                                    <input type="checkbox" id="nonResidentMobile" class="w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500">
                                                    <span class="text-sm text-gray-700 dark:text-gray-300">Non-Resident</span>
                                                </label>
                                                <label class="flex items-center space-x-3">
                                                    <input type="checkbox" id="workingHolidayMobile" class="w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500">
                                                    <span class="text-sm text-gray-700 dark:text-gray-300">Working Holiday Visa</span>
                                                </label>
                                                <label class="flex items-center space-x-3">
                                                    <input type="checkbox" id="noTaxFreeThresholdMobile" class="w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500">
                                                    <span class="text-sm text-gray-700 dark:text-gray-300">No tax-free threshold</span>
                                                </label>
                                                <label class="flex items-center space-x-3">
                                                    <input type="checkbox" id="studentLoanMobile" class="w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500">
                                                    <span class="text-sm text-gray-700 dark:text-gray-300">Student Loan (HELP, VET, SSL, TSL, SFSS)</span>
                                                </label>
                                                <label class="flex items-center space-x-3">
                                                    <input type="checkbox" id="withholdTaxOffsetsMobile" class="w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500">
                                                    <span class="text-sm text-gray-700 dark:text-gray-300">Withhold Tax Offsets</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Results Panel -->
                            <div class="lg:w-1/2">
                                <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                                    <!-- Summary Section -->
                                    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Summary</h3>
                                    </div>
                                    <div class="p-6">
                                        <div class="overflow-x-auto">
                                            <table class="w-full text-sm">
                                                <thead>
                                                    <tr class="border-b border-gray-200 dark:border-gray-700">
                                                        <th class="text-left py-2 text-gray-700 dark:text-gray-300">Component</th>
                                                        <th class="text-right py-2 text-gray-700 dark:text-gray-300">Weekly</th>
                                                        <th class="text-right py-2 text-gray-700 dark:text-gray-300">Fortnightly</th>
                                                        <th class="text-right py-2 text-gray-700 dark:text-gray-300">Monthly</th>
                                                        <th class="text-right py-2 text-gray-700 dark:text-gray-300">Annually</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="resultsTable" class="divide-y divide-gray-200 dark:divide-gray-700">
                                                    <tr>
                                                        <td class="py-2 text-gray-900 dark:text-white font-medium">Gross Income</td>
                                                        <td class="text-right py-2 text-gray-700 dark:text-gray-300" id="grossWeekly">$962</td>
                                                        <td class="text-right py-2 text-gray-700 dark:text-gray-300" id="grossFortnightly">$1,923</td>
                                                        <td class="text-right py-2 text-gray-700 dark:text-gray-300" id="grossMonthly">$4,167</td>
                                                        <td class="text-right py-2 text-gray-700 dark:text-gray-300" id="grossAnnually">$50,000</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="py-2 text-gray-900 dark:text-white font-medium">Superannuation</td>
                                                        <td class="text-right py-2 text-gray-700 dark:text-gray-300" id="superWeekly">$111</td>
                                                        <td class="text-right py-2 text-gray-700 dark:text-gray-300" id="superFortnightly">$221</td>
                                                        <td class="text-right py-2 text-gray-700 dark:text-gray-300" id="superMonthly">$479</td>
                                                        <td class="text-right py-2 text-gray-700 dark:text-gray-300" id="superAnnually">$5,750</td>
                                                    </tr>
                                                    <tr>
                                                        <td class="py-2 text-gray-900 dark:text-white font-medium">Total Tax</td>
                                                        <td class="text-right py-2 text-gray-700 dark:text-gray-300" id="totalTaxWeekly">$154</td>
                                                        <td class="text-right py-2 text-gray-700 dark:text-gray-300" id="totalTaxFortnightly">$308</td>
                                                        <td class="text-right py-2 text-gray-700 dark:text-gray-300" id="totalTaxMonthly">$667</td>
                                                        <td class="text-right py-2 text-gray-700 dark:text-gray-300" id="totalTaxAnnually">$8,000</td>
                                                    </tr>
                                                    <tr class="border-t-2 border-gray-300 dark:border-gray-600">
                                                        <td class="py-2 text-gray-900 dark:text-white font-bold">Take Home Pay</td>
                                                        <td class="text-right py-2 text-gray-900 dark:text-white font-bold" id="takeHomeWeekly">$808</td>
                                                        <td class="text-right py-2 text-gray-900 dark:text-white font-bold" id="takeHomeFortnightly">$1,615</td>
                                                        <td class="text-right py-2 text-gray-900 dark:text-white font-bold" id="takeHomeMonthly">$3,500</td>
                                                        <td class="text-right py-2 text-gray-900 dark:text-white font-bold" id="takeHomeAnnually">$42,000</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-4">*This salary and pay calculator shows estimates only</p>
                                    </div>

                                    <!-- Salary Rates Promotion in Results -->
                                    <div class="border-t border-gray-200 dark:border-gray-700 p-6">
                                        <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                                            <div class="flex items-center space-x-2 mb-2">
                                                <span class="text-lg">🌟</span>
                                                <h4 class="font-semibold text-green-900 dark:text-green-100">Help the Salary Community</h4>
                                            </div>
                                            <p class="text-sm text-green-800 dark:text-green-200 mb-3">Want to make better salary decisions? Join hundreds of professionals who are sharing their salaries anonymously. Your contribution helps others negotiate better compensation.</p>
                                            <div class="flex items-center justify-between">
                                                <a href="https://paycal.com.au/salary-rates/?salary=50000&type=annually" class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-md transition-colors">
                                                    Get paid, not played →
                                                </a>
                                                <div class="text-right">
                                                    <div class="text-lg font-bold text-green-900 dark:text-green-100">100+</div>
                                                    <div class="text-xs text-green-700 dark:text-green-300">Salaries Shared</div>
                                                </div>
                                            </div>
                                            <p class="text-xs text-green-700 dark:text-green-300 mt-2">It takes less than a minute!</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- Information Section -->
                    <section class="px-4 py-8">
                        <div class="max-w-4xl mx-auto">
                            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">Australian Pay Calculator</h2>
                            <div class="prose prose-gray dark:prose-invert max-w-none">
                                <p class="text-lg mb-6">Calculate your salary, wages, and take-home pay with our free Australian pay calculator. Features include:</p>
                                <ul class="list-disc pl-6 mb-6 space-y-2">
                                    <li>Instant tax calculations based on 2024-25 rates</li>
                                    <li>Stage 3 tax cuts included</li>
                                    <li>Superannuation calculator</li>
                                    <li>HELP/HECS debt repayments</li>
                                    <li>Medicare levy calculations</li>
                                </ul>

                                <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Are you a contractor?</h3>
                                <p class="mb-4">If you're working as a contractor, try our dedicated <a href="https://paycal.com.au/contractor-pay-calculator/" class="text-orange-600 hover:text-orange-700 underline">Contractor Pay Calculator</a> which includes:</p>
                                <ul class="list-disc pl-6 mb-6 space-y-2">
                                    <li>Hourly and daily rate calculations</li>
                                    <li>GST considerations</li>
                                    <li>Flexible superannuation options</li>
                                    <li>Tax variations for contractors</li>
                                </ul>

                                <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">How to Use the Pay Calculator</h3>
                                <p class="mb-6">Enter your salary details and instantly see your take-home pay, tax deductions, and superannuation contributions.</p>
                            </div>
                        </div>
                    </section>
                </div>
            </main>
        </div>

        <!-- Footer -->
        <footer class="border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
            <div class="container mx-auto px-4 py-12">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
                    <!-- Brand Section -->
                    <div class="lg:col-span-1">
                        <a href="/" class="text-xl font-bold text-orange-600">PayCal.com.au</a>
                        <p class="mt-4 text-sm text-gray-600 dark:text-gray-400">Australian calculators for tax, salary, and financial planning. Helping Aussies make informed financial decisions.</p>
                        <p class="mt-4 text-sm text-gray-600 dark:text-gray-400">
                            <a href="mailto:<EMAIL>" class="hover:text-orange-600"><EMAIL></a>
                        </p>
                    </div>

                    <!-- Pay Calculators -->
                    <div>
                        <h3 class="text-sm font-semibold text-gray-900 dark:text-white uppercase tracking-wider mb-4">Pay Calculators</h3>
                        <ul class="space-y-2">
                            <li><a href="/" class="text-sm text-muted-foreground hover:text-primary">Australian Pay Calculator</a></li>
                            <li><a href="https://paycal.com.au/contractor-pay-calculator/" class="text-sm text-muted-foreground hover:text-primary">Contractor Pay Calculator</a></li>
                            <li><a href="https://paycal.com.au/contract-rates/" class="text-sm text-muted-foreground hover:text-primary">Contract Rate Benchmarking</a></li>
                            <li><a href="https://paycal.com.au/salary-rates/" class="text-sm text-muted-foreground hover:text-primary">Salary Rate Benchmarking</a></li>
                        </ul>
                    </div>

                    <!-- Property Calculators -->
                    <div>
                        <h3 class="text-sm font-semibold text-gray-900 dark:text-white uppercase tracking-wider mb-4">Property Calculators</h3>
                        <ul class="space-y-2">
                            <li><a href="https://paycal.com.au/mortgage-calculator/" class="text-sm text-muted-foreground hover:text-primary">Mortgage Calculator</a></li>
                            <li><a href="https://paycal.com.au/home-loan-comparison/" class="text-sm text-muted-foreground hover:text-primary">Home Loan Comparison</a></li>
                            <li><a href="https://paycal.com.au/rate-cut-calculator/" class="text-sm text-muted-foreground hover:text-primary">Rate Cut Calculator</a></li>
                            <li><a href="https://paycal.com.au/mortgage-payoff-calculator/" class="text-sm text-muted-foreground hover:text-primary">Mortgage Payoff Calculator</a></li>
                        </ul>
                    </div>

                    <!-- Tax Tools -->
                    <div>
                        <h3 class="text-sm font-semibold text-gray-900 dark:text-white uppercase tracking-wider mb-4">Tax Tools</h3>
                        <ul class="space-y-2">
                            <li><a href="https://paycal.com.au/tax-cut-calculator/" class="text-sm text-muted-foreground hover:text-primary">Tax Cut Calculator</a></li>
                            <li><a href="https://paycal.com.au/tax-calendar/" class="text-sm text-muted-foreground hover:text-primary">Tax Calendar</a></li>
                            <li><a href="https://paycal.com.au/gst-calculator/" class="text-sm text-muted-foreground hover:text-primary">GST Calculator</a></li>
                        </ul>
                    </div>

                    <!-- Financial Tools -->
                    <div>
                        <h3 class="text-sm font-semibold text-gray-900 dark:text-white uppercase tracking-wider mb-4">Financial Tools</h3>
                        <ul class="space-y-2">
                            <li><a href="https://paycal.com.au/invoice-generator/" class="text-sm text-muted-foreground hover:text-primary">Invoice Generator</a></li>
                            <li><a href="https://paycal.com.au/quote-generator/" class="text-sm text-muted-foreground hover:text-primary">Quote Generator</a></li>
                            <li><a href="https://paycal.com.au/payslip-generator/" class="text-sm text-muted-foreground hover:text-primary">Payslip Generator</a></li>
                        </ul>

                        <h3 class="text-sm font-semibold text-gray-900 dark:text-white uppercase tracking-wider mb-4 mt-6">Vehicle Tools</h3>
                        <ul class="space-y-2">
                            <li><a href="https://paycal.com.au/ev-ice-novated-lease-calculator/" class="text-sm text-muted-foreground hover:text-primary">EV & ICE Novated Lease</a></li>
                            <li><a href="https://paycal.com.au/vehicle-registration-calculator/" class="text-sm text-muted-foreground hover:text-primary">Vehicle Registration</a></li>
                            <li><a href="https://paycal.com.au/public-holidays-calculator/" class="text-sm text-muted-foreground hover:text-primary">Public Holidays</a></li>
                            <li><a href="https://paycal.com.au/school-holidays-calculator/" class="text-sm text-muted-foreground hover:text-primary">School Holidays</a></li>
                            <li><a href="https://paycal.com.au/recommended/" class="text-sm text-muted-foreground hover:text-primary">Recommended</a></li>
                        </ul>
                    </div>
                </div>

                <!-- Bottom Footer -->
                <div class="mt-12 pt-8 border-t border-gray-200 dark:border-gray-700">
                    <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                        <div class="flex items-center space-x-4">
                            <a href="https://paycal.com.au/privacy-policy/" class="text-sm text-muted-foreground hover:text-primary">Privacy Policy</a>
                            <span class="text-muted-foreground">•</span>
                            <a href="https://paycal.com.au/blogs/" class="text-sm text-muted-foreground hover:text-primary">Blog</a>
                            <span class="text-muted-foreground">•</span>
                            <a href="https://docs.google.com/forms/d/e/1FAIpQLSfrRSGpb8UsOLM_o_02sYU0RCOWZep8NDHW30frI6ccxu4OVw/viewform" target="_blank" rel="noopener noreferrer" class="text-sm text-muted-foreground hover:text-primary">Feedback</a>
                        </div>
                        <p class="text-sm text-muted-foreground">Made for Aussies ❤️ © 2025 PayCal</p>
                    </div>
                </div>
            </div>
        </footer>

        <!-- Feedback Button -->
        <div class="fixed bottom-4 right-4 z-50">
            <button class="inline-flex items-center justify-center text-sm font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 bg-background/60 backdrop-blur-sm shadow-sm hover:shadow-md transition-all">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 h-4 w-4">
                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                    <line x1="9" x2="15" y1="10" y2="10"></line>
                    <line x1="12" x2="12" y1="7" y2="13"></line>
                </svg>
                Feedback
            </button>
        </div>
    </div>

    <script>
    // Mobile menu toggle
    function toggleMobileMenu() {
        const mobileMenu = document.getElementById('mobile-menu');
        if (mobileMenu) {
            mobileMenu.classList.toggle('hidden');
        }
    }

    // Dismiss banner
    function dismissBanner() {
        const banner = document.querySelector('.bg-green-100\\/90');
        if (banner) {
            banner.style.display = 'none';
        }
    }

    // Theme toggle
    function toggleTheme() {
        const html = document.documentElement;
        const isDark = html.classList.contains('dark');

        if (isDark) {
            html.classList.remove('dark');
            html.style.colorScheme = 'light';
            localStorage.setItem('theme', 'light');
        } else {
            html.classList.add('dark');
            html.style.colorScheme = 'dark';
            localStorage.setItem('theme', 'dark');
        }
    }

    // Initialize theme
    function initTheme() {
        const savedTheme = localStorage.getItem('theme');
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

        if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
            document.documentElement.classList.add('dark');
            document.documentElement.style.colorScheme = 'dark';
        } else {
            document.documentElement.classList.remove('dark');
            document.documentElement.style.colorScheme = 'light';
        }
    }

    // Australian Tax Calculator
    function calculateAustralianTax() {
        // Get input values
        const salary = parseFloat(document.getElementById('salary')?.value || document.getElementById('salaryMobile')?.value || 50000);
        const timePeriod = document.getElementById('timePeriod')?.value || document.getElementById('timePeriodMobile')?.value || 'annually';
        const superRate = parseFloat(document.getElementById('superRate')?.value || document.getElementById('superRateMobile')?.value || 11.5);

        // Convert to annual salary
        let annualSalary = salary;
        switch(timePeriod) {
            case 'weekly':
                annualSalary = salary * 52;
                break;
            case 'fortnightly':
                annualSalary = salary * 26;
                break;
            case 'monthly':
                annualSalary = salary * 12;
                break;
        }

        // Calculate tax (2024-25 rates with Stage 3 tax cuts)
        let incomeTax = 0;
        if (annualSalary > 18200) {
            if (annualSalary <= 45000) {
                incomeTax = (annualSalary - 18200) * 0.16;
            } else if (annualSalary <= 135000) {
                incomeTax = (45000 - 18200) * 0.16 + (annualSalary - 45000) * 0.30;
            } else if (annualSalary <= 190000) {
                incomeTax = (45000 - 18200) * 0.16 + (135000 - 45000) * 0.30 + (annualSalary - 135000) * 0.37;
            } else {
                incomeTax = (45000 - 18200) * 0.16 + (135000 - 45000) * 0.30 + (190000 - 135000) * 0.37 + (annualSalary - 190000) * 0.45;
            }
        }

        // Medicare levy (2%)
        const medicareLevy = annualSalary > 23226 ? annualSalary * 0.02 : 0;

        // Total tax
        const totalTax = incomeTax + medicareLevy;

        // Superannuation
        const superannuation = annualSalary * (superRate / 100);

        // Take home pay
        const takeHomePay = annualSalary - totalTax;

        // Update results table
        updateResultsTable(annualSalary, superannuation, totalTax, takeHomePay);
    }

    function updateResultsTable(gross, super, tax, takeHome) {
        // Calculate periods
        const weekly = {
            gross: gross / 52,
            super: super / 52,
            tax: tax / 52,
            takeHome: takeHome / 52
        };

        const fortnightly = {
            gross: gross / 26,
            super: super / 26,
            tax: tax / 26,
            takeHome: takeHome / 26
        };

        const monthly = {
            gross: gross / 12,
            super: super / 12,
            tax: tax / 12,
            takeHome: takeHome / 12
        };

        // Format currency
        const formatCurrency = (amount) => {
            return new Intl.NumberFormat('en-AU', {
                style: 'currency',
                currency: 'AUD',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(amount);
        };

        // Update table cells
        document.getElementById('grossWeekly').textContent = formatCurrency(weekly.gross);
        document.getElementById('grossFortnightly').textContent = formatCurrency(fortnightly.gross);
        document.getElementById('grossMonthly').textContent = formatCurrency(monthly.gross);
        document.getElementById('grossAnnually').textContent = formatCurrency(gross);

        document.getElementById('superWeekly').textContent = formatCurrency(weekly.super);
        document.getElementById('superFortnightly').textContent = formatCurrency(fortnightly.super);
        document.getElementById('superMonthly').textContent = formatCurrency(monthly.super);
        document.getElementById('superAnnually').textContent = formatCurrency(super);

        document.getElementById('totalTaxWeekly').textContent = formatCurrency(weekly.tax);
        document.getElementById('totalTaxFortnightly').textContent = formatCurrency(fortnightly.tax);
        document.getElementById('totalTaxMonthly').textContent = formatCurrency(monthly.tax);
        document.getElementById('totalTaxAnnually').textContent = formatCurrency(tax);

        document.getElementById('takeHomeWeekly').textContent = formatCurrency(weekly.takeHome);
        document.getElementById('takeHomeFortnightly').textContent = formatCurrency(fortnightly.takeHome);
        document.getElementById('takeHomeMonthly').textContent = formatCurrency(monthly.takeHome);
        document.getElementById('takeHomeAnnually').textContent = formatCurrency(takeHome);
    }

    // Auto-calculate on input change
    function setupAutoCalculation() {
        const inputs = ['salary', 'salaryMobile', 'timePeriod', 'timePeriodMobile', 'superRate', 'superRateMobile'];
        inputs.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('input', calculateAustralianTax);
                element.addEventListener('change', calculateAustralianTax);
            }
        });
    }

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
        initTheme();
        setupAutoCalculation();
        calculateAustralianTax(); // Initial calculation
    });
    </script>
</body>
</html>
