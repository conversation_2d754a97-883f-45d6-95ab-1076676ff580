<!-- PayCal Australia Header Component -->
<div class="bg-orange-100/90 dark:bg-orange-900/30 border-b border-orange-200 dark:border-orange-800/30 transition-colors duration-500">
    <div class="container mx-auto px-4 py-3">
        <div class="flex items-center justify-between gap-x-4">
            <div class="flex items-center gap-x-2 flex-1">
                <span class="hidden sm:inline">📄 New!</span>
                <span class="text-sm sm:text-base">Try our free <a class="font-semibold text-orange-600 dark:text-orange-400 hover:underline" href="invoice-generator.html">Invoice Generator</a><span class="hidden sm:inline"> - Create professional invoices in minutes!</span></span>
            </div>
            <div class="flex items-center gap-x-1 sm:gap-x-2">
                <div class="hidden sm:flex items-center gap-x-1 mr-2">
                    <button class="w-2 h-2 rounded-full transition-colors bg-orange-600 dark:text-orange-400" aria-label="Show promotion 1"></button>
                    <button class="w-2 h-2 rounded-full transition-colors bg-muted-foreground/30 hover:bg-orange-600/50" aria-label="Show promotion 2"></button>
                    <button class="w-2 h-2 rounded-full transition-colors bg-muted-foreground/30 hover:bg-orange-600/50" aria-label="Show promotion 3"></button>
                </div>
                <a class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-9 rounded-md px-3 hidden sm:inline-flex border-orange-300 dark:border-orange-700 hover:bg-orange-200/70 dark:hover:bg-orange-800/30" href="invoice-generator.html">Try Now</a>
                <a class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-8 w-8 sm:hidden border-orange-300 dark:border-orange-700 hover:bg-orange-200/70 dark:hover:bg-orange-800/30" href="invoice-generator.html">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                        <path d="M5 12h14"></path>
                        <path d="m12 5 7 7-7 7"></path>
                    </svg>
                    <span class="sr-only">Try Invoice Generator</span>
                </a>
                <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-8 w-8 hover:bg-orange-200/70 dark:hover:bg-orange-800/30" onclick="dismissBanner()">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                        <path d="M18 6 6 18"></path>
                        <path d="m6 6 12 12"></path>
                    </svg>
                    <span class="sr-only">Dismiss</span>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- PayCal Australia Main Header -->
<header class="supports-backdrop-blur:bg-background/60 sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur">
    <div class="container flex h-14 items-center">
        <!-- Mobile Menu Button -->
        <button class="inline-flex items-center justify-center rounded-md font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-9 w-9 md:hidden" onclick="toggleMobileMenu()">
            <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none" class="h-5 w-5">
                <path d="M8 2H13.5C13.7761 2 14 2.22386 14 2.5V12.5C14 12.7761 13.7761 13 13.5 13H8V2ZM7 2H1.5C1.22386 2 1 2.22386 1 2.5V12.5C1 12.7761 1.22386 13 1.5 13H7V2ZM0 2.5C0 1.67157 0.671573 1 1.5 1H13.5C14.3284 1 15 1.67157 15 2.5V12.5C15 13.3284 14.3284 14 13.5 14H1.5C0.671573 14 0 13.3284 0 12.5V2.5Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path>
            </svg>
            <span class="sr-only">Toggle Menu</span>
        </button>

        <!-- Desktop Navigation -->
        <div class="mr-4 hidden md:flex">
            <nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex max-w-max flex-1 items-center justify-center">
                <!-- PayCal Logo -->
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256" class="h-6 w-6 dark:text-white" aria-hidden="true">
                    <path d="M0 0h256v256H0z" fill="none"></path>
                    <rect width="256" height="256" fill="none" stroke="currentColor" stroke-width="16"></rect>
                    <rect x="32" y="32" width="192" height="64" fill="currentColor"></rect>
                    <circle cx="64" cy="128" r="16" fill="currentColor"></circle>
                    <circle cx="128" cy="128" r="16" fill="currentColor"></circle>
                    <circle cx="192" cy="128" r="16" fill="currentColor"></circle>
                    <circle cx="64" cy="176" r="16" fill="currentColor"></circle>
                    <circle cx="128" cy="176" r="16" fill="currentColor"></circle>
                    <circle cx="192" cy="176" r="16" fill="currentColor"></circle>
                    <circle cx="64" cy="224" r="16" fill="currentColor"></circle>
                    <circle cx="128" cy="224" r="16" fill="currentColor"></circle>
                    <circle cx="192" cy="224" r="16" fill="currentColor"></circle>
                </svg>
                
                <div style="position:relative">
                    <ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr">
                        <li>
                            <a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="index.html">
                                Paycal.com.au
                            </a>
                        </li>
                        <li>
                            <div class="relative group">
                                <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                    Pay Calculators
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                        <path d="m6 9 6 6 6-6"></path>
                                    </svg>
                                </button>
                                <div class="absolute left-0 top-full mt-1 hidden group-hover:block bg-background border rounded-md shadow-lg min-w-[250px] z-50">
                                    <div class="p-2">
                                        <a href="index.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Australian Pay Calculator</a>
                                        <a href="#" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Contractor Pay Calculator</a>
                                        <a href="#" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Contract Rate Benchmarking</a>
                                        <a href="#" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Salary Rate Benchmarking</a>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="relative group">
                                <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                    Home & Property Calculators
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                        <path d="m6 9 6 6 6-6"></path>
                                    </svg>
                                </button>
                                <div class="absolute left-0 top-full mt-1 hidden group-hover:block bg-background border rounded-md shadow-lg min-w-[250px] z-50">
                                    <div class="p-2">
                                        <a href="mortgage-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Mortgage Calculator</a>
                                        <a href="#" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Home Loan Comparison</a>
                                        <a href="#" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Rate Cut Calculator</a>
                                        <a href="#" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Mortgage Payoff Calculator</a>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="relative group">
                                <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                    Tax Calculators
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                        <path d="m6 9 6 6 6-6"></path>
                                    </svg>
                                </button>
                                <div class="absolute left-0 top-full mt-1 hidden group-hover:block bg-background border rounded-md shadow-lg min-w-[200px] z-50">
                                    <div class="p-2">
                                        <a href="#" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Tax Cut Calculator</a>
                                        <a href="#" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Tax Calendar</a>
                                        <a href="#" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">GST Calculator</a>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="relative group">
                                <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                    Financial Tools
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                        <path d="m6 9 6 6 6-6"></path>
                                    </svg>
                                </button>
                                <div class="absolute left-0 top-full mt-1 hidden group-hover:block bg-background border rounded-md shadow-lg min-w-[200px] z-50">
                                    <div class="p-2">
                                        <a href="invoice-generator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Invoice Generator</a>
                                        <a href="#" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Quote Generator</a>
                                        <a href="#" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Payslip Generator</a>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="relative group">
                                <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                    Vehicle Calculators
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                        <path d="m6 9 6 6 6-6"></path>
                                    </svg>
                                </button>
                                <div class="absolute left-0 top-full mt-1 hidden group-hover:block bg-background border rounded-md shadow-lg min-w-[250px] z-50">
                                    <div class="p-2">
                                        <a href="#" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">EV & ICE Novated Lease</a>
                                        <a href="#" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Vehicle Registration</a>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="relative group">
                                <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                    Holidays Calculators
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                        <path d="m6 9 6 6 6-6"></path>
                                    </svg>
                                </button>
                                <div class="absolute left-0 top-full mt-1 hidden group-hover:block bg-background border rounded-md shadow-lg min-w-[200px] z-50">
                                    <div class="p-2">
                                        <a href="#" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Public Holidays</a>
                                        <a href="#" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">School Holidays</a>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="#recommended">
                                Recommended
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="absolute left-0 top-full flex justify-center"></div>
            </nav>
        </div>

        <!-- Right Side Actions -->
        <div class="container flex h-16 items-center space-x-4 sm:justify-between sm:space-x-0">
            <div class="flex flex-1 items-center justify-end space-x-4">
                <nav class="flex items-center space-x-1">
                    <!-- Theme Toggle -->
                    <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 w-10" onclick="toggleTheme()">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-[1.5rem] w-[1.3rem] dark:hidden">
                            <circle cx="12" cy="12" r="4"></circle>
                            <path d="M12 2v2"></path>
                            <path d="M12 20v2"></path>
                            <path d="m4.93 4.93 1.41 1.41"></path>
                            <path d="m17.66 17.66 1.41 1.41"></path>
                            <path d="M2 12h2"></path>
                            <path d="M20 12h2"></path>
                            <path d="m6.34 17.66-1.41 1.41"></path>
                            <path d="m19.07 4.93-1.41 1.41"></path>
                        </svg>
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="hidden h-5 w-5 dark:block dark:text-white">
                            <path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"></path>
                        </svg>
                        <span class="sr-only">Toggle theme</span>
                    </button>
                </nav>
            </div>
        </div>
    </div>

    <!-- Mobile Menu -->
    <div id="mobile-menu" class="hidden md:hidden border-t bg-background">
        <div class="container px-4 py-4">
            <nav class="space-y-2">
                <a href="index.html" class="block px-3 py-2 text-sm font-medium hover:bg-accent rounded-md">Paycal.com.au</a>
                <div class="space-y-1">
                    <div class="px-3 py-2 text-sm font-medium text-muted-foreground">Pay Calculators</div>
                    <a href="index.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Australian Pay Calculator</a>
                    <a href="#" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Contractor Pay Calculator</a>
                    <a href="#" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Contract Rate Benchmarking</a>
                    <a href="#" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Salary Rate Benchmarking</a>
                </div>
                <div class="space-y-1">
                    <div class="px-3 py-2 text-sm font-medium text-muted-foreground">Home & Property Calculators</div>
                    <a href="mortgage-calculator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Mortgage Calculator</a>
                    <a href="#" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Home Loan Comparison</a>
                    <a href="#" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Rate Cut Calculator</a>
                    <a href="#" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Mortgage Payoff Calculator</a>
                </div>
                <div class="space-y-1">
                    <div class="px-3 py-2 text-sm font-medium text-muted-foreground">Tax Calculators</div>
                    <a href="#" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Tax Cut Calculator</a>
                    <a href="#" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Tax Calendar</a>
                    <a href="#" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">GST Calculator</a>
                </div>
                <div class="space-y-1">
                    <div class="px-3 py-2 text-sm font-medium text-muted-foreground">Financial Tools</div>
                    <a href="invoice-generator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Invoice Generator</a>
                    <a href="#" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Quote Generator</a>
                    <a href="#" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Payslip Generator</a>
                </div>
                <div class="space-y-1">
                    <div class="px-3 py-2 text-sm font-medium text-muted-foreground">Vehicle Calculators</div>
                    <a href="#" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">EV & ICE Novated Lease</a>
                    <a href="#" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Vehicle Registration</a>
                </div>
                <div class="space-y-1">
                    <div class="px-3 py-2 text-sm font-medium text-muted-foreground">Holidays Calculators</div>
                    <a href="#" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Public Holidays</a>
                    <a href="#" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">School Holidays</a>
                </div>
                <a href="#recommended" class="block px-3 py-2 text-sm font-medium hover:bg-accent rounded-md">Recommended</a>
            </nav>
        </div>
    </div>
</header>

<!-- PayCal Australia Footer Component -->
<footer class="border-t mt-24">
    <div class="container mx-auto px-6 py-12">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
            <!-- Brand Section -->
            <div class="lg:col-span-2">
                <a class="text-xl font-bold" href="index.html">PayCal.com.au</a>
                <p class="mt-4 text-sm text-muted-foreground">
                    Australian calculators for tax, salary, and financial planning. Helping Aussies make informed financial decisions.
                </p>
                <div class="mt-6">
                    <a href="mailto:<EMAIL>" class="text-sm text-muted-foreground hover:text-primary">
                        <EMAIL>
                    </a>
                </div>
            </div>

            <!-- Pay Calculators -->
            <div>
                <h3 class="text-sm font-semibold mb-4">Pay Calculators</h3>
                <ul class="space-y-3">
                    <li><a class="text-sm text-muted-foreground hover:text-primary" href="index.html">Australian Pay Calculator</a></li>
                    <li><a class="text-sm text-muted-foreground hover:text-primary" href="#">Contractor Pay Calculator</a></li>
                    <li><a class="text-sm text-muted-foreground hover:text-primary" href="#">Contract Rate Benchmarking</a></li>
                    <li><a class="text-sm text-muted-foreground hover:text-primary" href="#">Salary Rate Benchmarking</a></li>
                </ul>
            </div>

            <!-- Property Calculators -->
            <div>
                <h3 class="text-sm font-semibold mb-4">Property Calculators</h3>
                <ul class="space-y-3">
                    <li><a class="text-sm text-muted-foreground hover:text-primary" href="mortgage-calculator.html">Mortgage Calculator</a></li>
                    <li><a class="text-sm text-muted-foreground hover:text-primary" href="#">Home Loan Comparison</a></li>
                    <li><a class="text-sm text-muted-foreground hover:text-primary" href="#">Rate Cut Calculator</a></li>
                    <li><a class="text-sm text-muted-foreground hover:text-primary" href="#">Mortgage Payoff Calculator</a></li>
                </ul>
            </div>

            <!-- Tax Tools -->
            <div>
                <h3 class="text-sm font-semibold mb-4">Tax Tools</h3>
                <ul class="space-y-3">
                    <li><a class="text-sm text-muted-foreground hover:text-primary" href="#">Tax Cut Calculator</a></li>
                    <li><a class="text-sm text-muted-foreground hover:text-primary" href="#">Tax Calendar</a></li>
                    <li><a class="text-sm text-muted-foreground hover:text-primary" href="#">GST Calculator</a></li>
                </ul>
            </div>
        </div>

        <!-- Second Row -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-8">
            <!-- Financial Tools -->
            <div>
                <h3 class="text-sm font-semibold mb-4">Financial Tools</h3>
                <ul class="space-y-3">
                    <li><a class="text-sm text-muted-foreground hover:text-primary" href="invoice-generator.html">Invoice Generator</a></li>
                    <li><a class="text-sm text-muted-foreground hover:text-primary" href="#">Quote Generator</a></li>
                    <li><a class="text-sm text-muted-foreground hover:text-primary" href="#">Payslip Generator</a></li>
                </ul>
            </div>

            <!-- Vehicle Tools -->
            <div>
                <h3 class="text-sm font-semibold mb-4">Vehicle Tools</h3>
                <ul class="space-y-3">
                    <li><a class="text-sm text-muted-foreground hover:text-primary" href="#">EV & ICE Novated Lease</a></li>
                    <li><a class="text-sm text-muted-foreground hover:text-primary" href="#">Vehicle Registration</a></li>
                    <li><a class="text-sm text-muted-foreground hover:text-primary" href="#">Public Holidays</a></li>
                    <li><a class="text-sm text-muted-foreground hover:text-primary" href="#">School Holidays</a></li>
                    <li><a class="text-sm text-muted-foreground hover:text-primary" href="#recommended">Recommended</a></li>
                </ul>
            </div>
        </div>

        <!-- Footer Bottom -->
        <div class="mt-12 pt-8 border-t">
            <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                <div class="flex items-center space-x-4">
                    <a class="text-sm text-muted-foreground hover:text-primary" href="#privacy">Privacy Policy</a>
                    <span class="text-muted-foreground">•</span>
                    <a class="text-sm text-muted-foreground hover:text-primary" href="#blog">Blog</a>
                    <span class="text-muted-foreground">•</span>
                    <a href="#feedback" target="_blank" rel="noopener noreferrer" class="text-sm text-muted-foreground hover:text-primary">Feedback</a>
                </div>
                <p class="text-sm text-muted-foreground">
                    Made for Aussies ❤️ © 2025 PayCal
                </p>
            </div>
        </div>
    </div>
</footer>

<!-- Scroll to Top Button -->
<div class="fixed bottom-4 right-4 z-50">
    <button class="inline-flex items-center justify-center text-sm font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 bg-background/60 backdrop-blur-sm shadow-sm hover:shadow-md transition-all duration-200" onclick="scrollToTop()">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
            <path d="m18 15-6-6-6 6"></path>
        </svg>
        <span class="sr-only">Scroll to top</span>
    </button>
</div>

<!-- JavaScript for PayCal Header/Footer functionality -->
<script>
// Mobile menu toggle
function toggleMobileMenu() {
    const mobileMenu = document.getElementById('mobile-menu');
    if (mobileMenu) {
        mobileMenu.classList.toggle('hidden');
    }
}

// Dismiss banner
function dismissBanner() {
    const banner = document.querySelector('.bg-orange-100\\/90');
    if (banner) {
        banner.style.display = 'none';
    }
}

// Theme toggle functionality
function toggleTheme() {
    const html = document.documentElement;
    const currentTheme = html.classList.contains('dark') ? 'dark' : 'light';

    if (currentTheme === 'light') {
        html.classList.remove('light');
        html.classList.add('dark');
        html.style.colorScheme = 'dark';
        localStorage.setItem('theme', 'dark');
    } else {
        html.classList.remove('dark');
        html.classList.add('light');
        html.style.colorScheme = 'light';
        localStorage.setItem('theme', 'light');
    }
}

// Scroll to top functionality
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

// Initialize theme from localStorage
function initializeTheme() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    const html = document.documentElement;

    html.classList.remove('light', 'dark');
    html.classList.add(savedTheme);
    html.style.colorScheme = savedTheme;
}

// Initialize all PayCal functionality
function initializePayCal() {
    initializeTheme();

    // Close mobile menu when clicking outside
    document.addEventListener('click', function(event) {
        const mobileMenu = document.getElementById('mobile-menu');
        const menuButton = document.querySelector('[onclick="toggleMobileMenu()"]');

        if (mobileMenu && !mobileMenu.classList.contains('hidden')) {
            if (!mobileMenu.contains(event.target) && !menuButton.contains(event.target)) {
                mobileMenu.classList.add('hidden');
            }
        }
    });

    // Keyboard navigation support
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const mobileMenu = document.getElementById('mobile-menu');
            if (mobileMenu && !mobileMenu.classList.contains('hidden')) {
                mobileMenu.classList.add('hidden');
            }
        }
    });

    // Show/hide scroll to top button
    window.addEventListener('scroll', function() {
        const scrollButton = document.querySelector('.fixed.bottom-4.right-4');
        if (scrollButton) {
            if (window.pageYOffset > 300) {
                scrollButton.style.opacity = '1';
                scrollButton.style.visibility = 'visible';
            } else {
                scrollButton.style.opacity = '0';
                scrollButton.style.visibility = 'hidden';
            }
        }
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initializePayCal);
</script>
