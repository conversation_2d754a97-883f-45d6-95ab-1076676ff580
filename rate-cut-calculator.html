<!DOCTYPE html>
<html lang="en" class="light" style="color-scheme: light;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rate Cut Calculator | Interest Rate Change Calculator | PayCal Australia</title>
    <meta name="description" content="Calculate how interest rate cuts affect your mortgage payments. See potential savings from RBA rate changes and plan your home loan strategy.">
    <meta name="keywords" content="rate cut calculator,interest rate calculator,mortgage rate change,rba rate cut,home loan calculator,mortgage savings,interest rate impact">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        border: "hsl(var(--border))",
                        input: "hsl(var(--input))",
                        ring: "hsl(var(--ring))",
                        background: "hsl(var(--background))",
                        foreground: "hsl(var(--foreground))",
                        primary: {
                            DEFAULT: "hsl(var(--primary))",
                            foreground: "hsl(var(--primary-foreground))",
                        },
                        secondary: {
                            DEFAULT: "hsl(var(--secondary))",
                            foreground: "hsl(var(--secondary-foreground))",
                        },
                        muted: {
                            DEFAULT: "hsl(var(--muted))",
                            foreground: "hsl(var(--muted-foreground))",
                        },
                    },
                }
            }
        }
    </script>
    <style>
        :root {
            --background: 0 0% 100%;
            --foreground: 222.2 84% 4.9%;
            --card: 0 0% 100%;
            --card-foreground: 222.2 84% 4.9%;
            --primary: 222.2 47.4% 11.2%;
            --primary-foreground: 210 40% 98%;
            --secondary: 210 40% 96%;
            --secondary-foreground: 222.2 84% 4.9%;
            --muted: 210 40% 96%;
            --muted-foreground: 215.4 16.3% 46.9%;
            --border: 214.3 31.8% 91.4%;
            --input: 214.3 31.8% 91.4%;
            --ring: 222.2 84% 4.9%;
            --radius: 0.5rem;
        }

        * {
            border-color: hsl(var(--border));
        }

        body {
            background-color: hsl(var(--background));
            color: hsl(var(--foreground));
        }

        .calculator-container {
            background: hsl(var(--card));
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
        }

        .input-group {
            margin-bottom: 1rem;
        }

        .input-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: hsl(var(--foreground));
        }

        .input-group input, .input-group select {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
            background: hsl(var(--background));
            color: hsl(var(--foreground));
        }

        .input-group input:focus, .input-group select:focus {
            outline: none;
            border-color: hsl(var(--ring));
            box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
        }

        .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid hsl(var(--border));
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .result-label {
            font-weight: 500;
            color: hsl(var(--foreground));
        }

        .result-value {
            font-weight: 600;
            color: hsl(var(--primary));
            font-size: 1.1rem;
        }

        .savings-highlight {
            background: #dcfce7;
            border: 2px solid #16a34a;
            border-radius: var(--radius);
            padding: 1.5rem;
            margin: 1rem 0;
            text-align: center;
        }

        .rate-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin: 1rem 0;
        }

        .rate-card {
            background: hsl(var(--muted));
            padding: 1rem;
            border-radius: var(--radius);
            text-align: center;
        }

        .rate-card.current {
            border: 2px solid #ef4444;
        }

        .rate-card.new {
            border: 2px solid #16a34a;
        }

        .chart-container {
            height: 200px;
            background: hsl(var(--muted));
            border-radius: var(--radius);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 1rem 0;
        }

        @media (max-width: 768px) {
            .calculator-container {
                padding: 1rem;
            }
            .rate-comparison {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>

<body class="min-h-screen bg-background font-sans antialiased">
    <div class="relative flex min-h-screen flex-col">
        <!-- PayCal Australia Header -->
        <div class="bg-orange-100/90 border-b border-orange-200 transition-colors duration-500">
            <div class="container mx-auto px-4 py-3">
                <div class="flex items-center justify-between gap-x-4">
                    <div class="flex items-center gap-x-2 flex-1">
                        <span class="hidden sm:inline">📉 Calculate!</span>
                        <span class="text-sm sm:text-base">See how <a class="font-semibold text-orange-600 hover:underline" href="#calculator">rate cuts</a><span class="hidden sm:inline"> affect your mortgage!</span></span>
                    </div>
                    <div class="flex items-center gap-x-1 sm:gap-x-2">
                        <a class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-9 rounded-md px-3 hidden sm:inline-flex border-orange-300 hover:bg-orange-200/70" href="#calculator">Calculate Savings</a>
                        <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-8 w-8 hover:bg-orange-200/70" onclick="dismissBanner()">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                                <path d="M18 6 6 18"></path>
                                <path d="m6 6 12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <main class="flex-1">
            <div class="container mx-auto px-4 py-8">
                <!-- Hero Section -->
                <div class="text-center mb-8">
                    <h1 class="text-4xl font-bold tracking-tight text-foreground mb-4">
                        Rate Cut Calculator
                    </h1>
                    <p class="text-xl text-muted-foreground max-w-2xl mx-auto">
                        Calculate how interest rate changes affect your mortgage payments. See potential savings from RBA rate cuts and plan your home loan strategy.
                    </p>
                </div>

                <!-- Calculator Section -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto" id="calculator">
                    <!-- Input Form -->
                    <div class="calculator-container p-6">
                        <h2 class="text-2xl font-semibold mb-4">Loan Details</h2>
                        
                        <form class="grid gap-4" id="rate-cut-form">
                            <!-- Current Loan Balance -->
                            <div class="input-group">
                                <label for="loanBalance">Current Loan Balance</label>
                                <div class="flex border h-10 w-full rounded-md border-input bg-background">
                                    <span class="flex items-center px-3 h-10">$</span>
                                    <input 
                                        type="number" 
                                        id="loanBalance"
                                        class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-l-none m-[-1]"
                                        min="0"
                                        placeholder="Enter current loan balance"
                                        value="500000"
                                        oninput="calculateRateCut()"
                                    />
                                </div>
                            </div>

                            <!-- Current Interest Rate -->
                            <div class="input-group">
                                <label for="currentRate">Current Interest Rate</label>
                                <div class="flex border h-10 w-full rounded-md border-input bg-background">
                                    <input 
                                        type="number" 
                                        id="currentRate"
                                        class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-r-none m-[-1]"
                                        min="0"
                                        max="20"
                                        step="0.01"
                                        placeholder="Current rate"
                                        value="6.50"
                                        oninput="calculateRateCut()"
                                    />
                                    <span class="flex items-center px-3 h-10">%</span>
                                </div>
                            </div>

                            <!-- New Interest Rate -->
                            <div class="input-group">
                                <label for="newRate">New Interest Rate (After Cut)</label>
                                <div class="flex border h-10 w-full rounded-md border-input bg-background">
                                    <input 
                                        type="number" 
                                        id="newRate"
                                        class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-r-none m-[-1]"
                                        min="0"
                                        max="20"
                                        step="0.01"
                                        placeholder="New rate"
                                        value="6.00"
                                        oninput="calculateRateCut()"
                                    />
                                    <span class="flex items-center px-3 h-10">%</span>
                                </div>
                            </div>

                            <!-- Remaining Loan Term -->
                            <div class="input-group">
                                <label for="loanTerm">Remaining Loan Term</label>
                                <div class="flex border h-10 w-full rounded-md border-input bg-background">
                                    <input 
                                        type="number" 
                                        id="loanTerm"
                                        class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-r-none m-[-1]"
                                        min="1"
                                        max="50"
                                        placeholder="Years remaining"
                                        value="25"
                                        oninput="calculateRateCut()"
                                    />
                                    <span class="flex items-center px-3 h-10">years</span>
                                </div>
                            </div>

                            <!-- Payment Frequency -->
                            <div class="input-group">
                                <label for="paymentFreq">Payment Frequency</label>
                                <select 
                                    id="paymentFreq"
                                    class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                    onchange="calculateRateCut()"
                                >
                                    <option value="monthly" selected>Monthly</option>
                                    <option value="fortnightly">Fortnightly</option>
                                    <option value="weekly">Weekly</option>
                                </select>
                            </div>

                            <!-- Quick Rate Cut Buttons -->
                            <div class="input-group">
                                <label>Quick Rate Cut Options</label>
                                <div class="grid grid-cols-3 gap-2 mt-2">
                                    <button type="button" class="px-3 py-2 text-sm border rounded-md hover:bg-muted" onclick="applyRateCut(0.25)">-0.25%</button>
                                    <button type="button" class="px-3 py-2 text-sm border rounded-md hover:bg-muted" onclick="applyRateCut(0.50)">-0.50%</button>
                                    <button type="button" class="px-3 py-2 text-sm border rounded-md hover:bg-muted" onclick="applyRateCut(1.00)">-1.00%</button>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Results Section -->
                    <div class="calculator-container p-6">
                        <h2 class="text-2xl font-semibold mb-4">Rate Cut Impact</h2>
                        
                        <!-- Rate Comparison -->
                        <div class="rate-comparison">
                            <div class="rate-card current">
                                <h4 class="font-semibold text-red-700">Current Rate</h4>
                                <div class="text-2xl font-bold text-red-700" id="display-current-rate">6.50%</div>
                            </div>
                            <div class="rate-card new">
                                <h4 class="font-semibold text-green-700">New Rate</h4>
                                <div class="text-2xl font-bold text-green-700" id="display-new-rate">6.00%</div>
                            </div>
                        </div>

                        <!-- Savings Highlight -->
                        <div class="savings-highlight">
                            <h3 class="text-lg font-semibold text-green-800 mb-2">Monthly Savings</h3>
                            <div class="text-3xl font-bold text-green-800" id="monthly-savings">$234</div>
                            <p class="text-sm text-green-700 mt-1">per month reduction in payments</p>
                        </div>

                        <div class="results-container" id="rate-cut-results">
                            <div class="result-item">
                                <span class="result-label">Current Payment</span>
                                <span class="result-value" id="current-payment">$3,234</span>
                            </div>
                            <div class="result-item">
                                <span class="result-label">New Payment</span>
                                <span class="result-value" id="new-payment">$3,000</span>
                            </div>
                            <div class="result-item">
                                <span class="result-label">Annual Savings</span>
                                <span class="result-value" id="annual-savings">$2,808</span>
                            </div>
                            <div class="result-item">
                                <span class="result-label">Total Interest Saved</span>
                                <span class="result-value" id="total-interest-saved">$58,500</span>
                            </div>
                            <div class="result-item">
                                <span class="result-label">Time Saved</span>
                                <span class="result-value" id="time-saved">2.3 years</span>
                            </div>
                        </div>

                        <!-- Chart Placeholder -->
                        <div class="chart-container">
                            <div class="text-center text-muted-foreground">
                                <div class="text-lg font-semibold mb-2">Payment Comparison Chart</div>
                                <div class="text-sm">Visual representation of payment savings over time</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Information Section -->
                <div class="mt-12 max-w-4xl mx-auto">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <!-- About Rate Cuts -->
                        <div class="calculator-container p-6">
                            <h3 class="text-xl font-semibold mb-4">About Interest Rate Cuts</h3>
                            <p class="text-muted-foreground mb-4">
                                The Reserve Bank of Australia (RBA) sets the official cash rate, which influences mortgage interest rates across all lenders.
                            </p>
                            <ul class="text-sm text-muted-foreground space-y-2">
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-green-600 rounded-full"></span>
                                    Rate cuts reduce monthly payments
                                </li>
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-blue-600 rounded-full"></span>
                                    Lower rates mean less interest over time
                                </li>
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-orange-600 rounded-full"></span>
                                    Can shorten loan term if payments maintained
                                </li>
                            </ul>
                        </div>

                        <!-- Rate Cut Strategies -->
                        <div class="calculator-container p-6">
                            <h3 class="text-xl font-semibold mb-4">Rate Cut Strategies</h3>
                            <p class="text-muted-foreground mb-4">
                                Make the most of interest rate cuts with these smart mortgage strategies.
                            </p>
                            <ul class="text-sm text-muted-foreground space-y-2">
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-green-600 rounded-full"></span>
                                    Keep payments the same to pay off faster
                                </li>
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-blue-600 rounded-full"></span>
                                    Use savings for offset account
                                </li>
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-orange-600 rounded-full"></span>
                                    Consider refinancing for better rates
                                </li>
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-purple-600 rounded-full"></span>
                                    Review loan features and benefits
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="border-t bg-muted/50">
            <div class="container mx-auto px-4 py-8">
                <div class="text-center">
                    <p class="text-sm text-muted-foreground">
                        © 2024 PayCal.com.au. Made for Aussies ❤️
                    </p>
                </div>
            </div>
        </footer>
    </div>

    <script>
        // Initialize calculator
        document.addEventListener('DOMContentLoaded', function() {
            calculateRateCut();
        });

        // Calculate rate cut impact
        function calculateRateCut() {
            const loanBalance = parseFloat(document.getElementById('loanBalance').value) || 500000;
            const currentRate = parseFloat(document.getElementById('currentRate').value) || 6.50;
            const newRate = parseFloat(document.getElementById('newRate').value) || 6.00;
            const loanTerm = parseFloat(document.getElementById('loanTerm').value) || 25;
            const paymentFreq = document.getElementById('paymentFreq').value;

            // Convert annual rates to period rates
            let periodsPerYear;
            switch (paymentFreq) {
                case 'weekly':
                    periodsPerYear = 52;
                    break;
                case 'fortnightly':
                    periodsPerYear = 26;
                    break;
                default:
                    periodsPerYear = 12;
            }

            const currentPeriodRate = (currentRate / 100) / periodsPerYear;
            const newPeriodRate = (newRate / 100) / periodsPerYear;
            const totalPeriods = loanTerm * periodsPerYear;

            // Calculate current payment
            const currentPayment = calculatePayment(loanBalance, currentPeriodRate, totalPeriods);

            // Calculate new payment
            const newPayment = calculatePayment(loanBalance, newPeriodRate, totalPeriods);

            // Calculate savings
            const periodSavings = currentPayment - newPayment;
            const monthlySavings = paymentFreq === 'monthly' ? periodSavings :
                                 paymentFreq === 'fortnightly' ? periodSavings * 26 / 12 :
                                 periodSavings * 52 / 12;
            const annualSavings = monthlySavings * 12;

            // Calculate total interest saved over loan term
            const currentTotalInterest = (currentPayment * totalPeriods) - loanBalance;
            const newTotalInterest = (newPayment * totalPeriods) - loanBalance;
            const totalInterestSaved = currentTotalInterest - newTotalInterest;

            // Calculate time saved if maintaining current payment
            const timeSaved = calculateTimeSaved(loanBalance, newPeriodRate, currentPayment, periodsPerYear);

            // Update display
            document.getElementById('display-current-rate').textContent = `${currentRate.toFixed(2)}%`;
            document.getElementById('display-new-rate').textContent = `${newRate.toFixed(2)}%`;
            document.getElementById('current-payment').textContent = formatCurrency(currentPayment);
            document.getElementById('new-payment').textContent = formatCurrency(newPayment);
            document.getElementById('monthly-savings').textContent = formatCurrency(monthlySavings);
            document.getElementById('annual-savings').textContent = formatCurrency(annualSavings);
            document.getElementById('total-interest-saved').textContent = formatCurrency(totalInterestSaved);
            document.getElementById('time-saved').textContent = `${timeSaved.toFixed(1)} years`;
        }

        // Calculate loan payment using standard formula
        function calculatePayment(principal, rate, periods) {
            if (rate === 0) return principal / periods;
            return principal * (rate * Math.pow(1 + rate, periods)) / (Math.pow(1 + rate, periods) - 1);
        }

        // Calculate time saved if maintaining current payment
        function calculateTimeSaved(balance, newRate, currentPayment, periodsPerYear) {
            if (newRate === 0) return 0;

            // Calculate new loan term with current payment amount
            const newPeriods = -Math.log(1 - (balance * newRate) / currentPayment) / Math.log(1 + newRate);
            const originalPeriods = parseFloat(document.getElementById('loanTerm').value) * periodsPerYear;

            return Math.max(0, (originalPeriods - newPeriods) / periodsPerYear);
        }

        // Apply quick rate cut
        function applyRateCut(cutAmount) {
            const currentRate = parseFloat(document.getElementById('currentRate').value) || 6.50;
            const newRate = Math.max(0, currentRate - cutAmount);
            document.getElementById('newRate').value = newRate.toFixed(2);
            calculateRateCut();
        }

        // Format currency
        function formatCurrency(amount) {
            return new Intl.NumberFormat('en-AU', {
                style: 'currency',
                currency: 'AUD',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(amount);
        }

        // Dismiss banner
        function dismissBanner() {
            const banner = document.querySelector('.bg-orange-100\\/90');
            if (banner) banner.style.display = 'none';
        }
    </script>
</body>
</html>
