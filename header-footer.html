<!-- PayCal Australia Header Component -->
<div class="bg-orange-100/90 dark:bg-orange-900/30 border-b border-orange-200 dark:border-orange-800/30 transition-colors duration-500">
    <div class="container mx-auto px-4 py-3">
        <div class="flex items-center justify-between gap-x-4">
            <div class="flex items-center gap-x-2 flex-1">
                <span class="hidden sm:inline">📄 New!</span>
                <span class="text-sm sm:text-base">Try our free <a class="font-semibold text-orange-600 dark:text-orange-400 hover:underline" href="invoice-generator.html">Invoice Generator</a><span class="hidden sm:inline"> - Create professional invoices in minutes!</span></span>
            </div>
            <div class="flex items-center gap-x-1 sm:gap-x-2">
                <div class="hidden sm:flex items-center gap-x-1 mr-2">
                    <button class="w-2 h-2 rounded-full transition-colors bg-orange-600 dark:text-orange-400" aria-label="Show promotion 1"></button>
                    <button class="w-2 h-2 rounded-full transition-colors bg-muted-foreground/30 hover:bg-orange-600/50" aria-label="Show promotion 2"></button>
                    <button class="w-2 h-2 rounded-full transition-colors bg-muted-foreground/30 hover:bg-orange-600/50" aria-label="Show promotion 3"></button>
                </div>
                <a class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-9 rounded-md px-3 hidden sm:inline-flex border-orange-300 dark:border-orange-700 hover:bg-orange-200/70 dark:hover:bg-orange-800/30" href="invoice-generator.html">Try Now</a>
                <a class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-8 w-8 sm:hidden border-orange-300 dark:border-orange-700 hover:bg-orange-200/70 dark:hover:bg-orange-800/30" href="invoice-generator.html">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                        <path d="M5 12h14"></path>
                        <path d="m12 5 7 7-7 7"></path>
                    </svg>
                    <span class="sr-only">Try Invoice Generator</span>
                </a>
                <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-8 w-8 hover:bg-orange-200/70 dark:hover:bg-orange-800/30" onclick="dismissBanner()">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                        <path d="M18 6 6 18"></path>
                        <path d="m6 6 12 12"></path>
                    </svg>
                    <span class="sr-only">Dismiss</span>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Main Header -->
<header class="supports-backdrop-blur:bg-background/60 sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur">
    <div class="container flex h-14 items-center">
        <!-- Mobile Menu Button -->
        <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-9 w-9 md:hidden" onclick="toggleMobileMenu()">
            <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none" class="h-5 w-5">
                <path d="M1.5 3C1.22386 3 1 3.22386 1 3.5C1 3.77614 1.22386 4 1.5 4H13.5C13.7761 4 14 3.77614 14 3.5C14 3.22386 13.7761 3 13.5 3H1.5ZM1 7.5C1 7.22386 1.22386 7 1.5 7H13.5C13.7761 7 14 7.22386 14 7.5C14 7.77614 13.7761 8 13.5 8H1.5C1.22386 8 1 7.77614 1 7.5ZM1 11.5C1 11.2239 1.22386 11 1.5 11H13.5C13.7761 11 14 11.2239 14 11.5C14 11.7761 13.7761 12 13.5 12H1.5C1.22386 12 1 11.7761 1 11.5Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path>
            </svg>
            <span class="sr-only">Toggle Menu</span>
        </button>

        <!-- Desktop Navigation -->
        <div class="mr-4 hidden md:flex">
            <nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex max-w-max flex-1 items-center justify-center">
                <!-- Logo -->
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256" class="h-6 w-6 dark:text-white mr-4" aria-hidden="true">
                    <path d="M0 0h256v256H0z" fill="none"></path>
                    <rect width="256" height="256" fill="none" stroke="currentColor" stroke-width="16"></rect>
                    <rect x="32" y="32" width="192" height="64" fill="currentColor"></rect>
                    <circle cx="64" cy="128" r="16" fill="currentColor"></circle>
                    <circle cx="128" cy="128" r="16" fill="currentColor"></circle>
                    <circle cx="192" cy="128" r="16" fill="currentColor"></circle>
                    <circle cx="64" cy="176" r="16" fill="currentColor"></circle>
                    <circle cx="128" cy="176" r="16" fill="currentColor"></circle>
                    <circle cx="192" cy="176" r="16" fill="currentColor"></circle>
                    <circle cx="64" cy="224" r="16" fill="currentColor"></circle>
                    <circle cx="128" cy="224" r="16" fill="currentColor"></circle>
                    <circle cx="192" cy="224" r="16" fill="currentColor"></circle>
                </svg>
                
                <div style="position:relative">
                    <ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr">
                        <li>
                            <a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="index.html">
                                Calculator Suite
                            </a>
                        </li>
                        <li>
                            <div class="relative group">
                                <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                    Health Calculators
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                        <path d="m6 9 6 6 6-6"></path>
                                    </svg>
                                </button>
                                <div class="absolute left-0 top-full mt-1 hidden group-hover:block bg-background border rounded-md shadow-lg min-w-[200px] z-50">
                                    <div class="p-2">
                                        <a href="bmi-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">BMI Calculator</a>
                                        <a href="#" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Body Fat Calculator</a>
                                        <a href="#" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Calorie Calculator</a>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="relative group">
                                <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                    Financial Calculators
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                        <path d="m6 9 6 6 6-6"></path>
                                    </svg>
                                </button>
                                <div class="absolute left-0 top-full mt-1 hidden group-hover:block bg-background border rounded-md shadow-lg min-w-[200px] z-50">
                                    <div class="p-2">
                                        <a href="loan-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Loan Calculator</a>
                                        <a href="investment-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Investment Calculator</a>
                                        <a href="retirement-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Retirement Calculator</a>
                                        <a href="#" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Mortgage Calculator</a>
                                        <a href="#" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Savings Calculator</a>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="#about">
                                About
                            </a>
                        </li>
                        <li>
                            <a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="#contact">
                                Contact
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
        </div>

        <!-- Right Side Actions -->
        <div class="container flex h-16 items-center space-x-4 sm:justify-between sm:space-x-0">
            <div class="flex flex-1 items-center justify-end space-x-4">
                <nav class="flex items-center space-x-1">
                    <!-- Theme Toggle -->
                    <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 w-10" onclick="toggleTheme()">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-[1.5rem] w-[1.3rem] dark:hidden">
                            <circle cx="12" cy="12" r="4"></circle>
                            <path d="M12 2v2"></path>
                            <path d="M12 20v2"></path>
                            <path d="m4.93 4.93 1.41 1.41"></path>
                            <path d="m17.66 17.66 1.41 1.41"></path>
                            <path d="M2 12h2"></path>
                            <path d="M20 12h2"></path>
                            <path d="m6.34 17.66-1.41 1.41"></path>
                            <path d="m19.07 4.93-1.41 1.41"></path>
                        </svg>
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="hidden h-5 w-5 dark:block dark:text-white">
                            <path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"></path>
                        </svg>
                        <span class="sr-only">Toggle theme</span>
                    </button>
                </nav>
            </div>
        </div>
    </div>

    <!-- Mobile Menu -->
    <div id="mobile-menu" class="hidden md:hidden border-t bg-background">
        <div class="container px-4 py-4">
            <nav class="space-y-2">
                <a href="index.html" class="block px-3 py-2 text-sm font-medium hover:bg-accent rounded-md">Calculator Suite</a>
                <div class="space-y-1">
                    <div class="px-3 py-2 text-sm font-medium text-muted-foreground">Health Calculators</div>
                    <a href="bmi-calculator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">BMI Calculator</a>
                    <a href="#" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Body Fat Calculator</a>
                    <a href="#" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Calorie Calculator</a>
                </div>
                <div class="space-y-1">
                    <div class="px-3 py-2 text-sm font-medium text-muted-foreground">Financial Calculators</div>
                    <a href="loan-calculator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Loan Calculator</a>
                    <a href="investment-calculator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Investment Calculator</a>
                    <a href="retirement-calculator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Retirement Calculator</a>
                    <a href="#" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Mortgage Calculator</a>
                    <a href="#" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Savings Calculator</a>
                </div>
                <a href="#about" class="block px-3 py-2 text-sm font-medium hover:bg-accent rounded-md">About</a>
                <a href="#contact" class="block px-3 py-2 text-sm font-medium hover:bg-accent rounded-md">Contact</a>
            </nav>
        </div>
    </div>
</header>

<!-- PayCal Footer Component -->
<footer class="border-t mt-24">
    <div class="container mx-auto px-6 py-12">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
            <!-- Brand Section -->
            <div class="lg:col-span-2">
                <a class="text-xl font-bold" href="index.html">Calculator Suite</a>
                <p class="mt-4 text-sm text-muted-foreground">
                    Professional calculators for health, finance, and planning. Helping you make informed decisions with accurate calculations and educational content.
                </p>
                <div class="mt-6">
                    <a href="mailto:<EMAIL>" class="text-sm text-muted-foreground hover:text-primary">
                        <EMAIL>
                    </a>
                </div>
            </div>

            <!-- Health Calculators -->
            <div>
                <h3 class="text-sm font-semibold mb-4">Health Calculators</h3>
                <ul class="space-y-3">
                    <li><a class="text-sm text-muted-foreground hover:text-primary" href="bmi-calculator.html">BMI Calculator</a></li>
                    <li><a class="text-sm text-muted-foreground hover:text-primary" href="#">Body Fat Calculator</a></li>
                    <li><a class="text-sm text-muted-foreground hover:text-primary" href="#">Calorie Calculator</a></li>
                    <li><a class="text-sm text-muted-foreground hover:text-primary" href="#">Heart Rate Calculator</a></li>
                </ul>
            </div>

            <!-- Financial Calculators -->
            <div>
                <h3 class="text-sm font-semibold mb-4">Financial Calculators</h3>
                <ul class="space-y-3">
                    <li><a class="text-sm text-muted-foreground hover:text-primary" href="loan-calculator.html">Loan Calculator</a></li>
                    <li><a class="text-sm text-muted-foreground hover:text-primary" href="investment-calculator.html">Investment Calculator</a></li>
                    <li><a class="text-sm text-muted-foreground hover:text-primary" href="retirement-calculator.html">Retirement Calculator</a></li>
                    <li><a class="text-sm text-muted-foreground hover:text-primary" href="#">Mortgage Calculator</a></li>
                </ul>
            </div>

            <!-- Resources -->
            <div>
                <h3 class="text-sm font-semibold mb-4">Resources</h3>
                <ul class="space-y-3">
                    <li><a class="text-sm text-muted-foreground hover:text-primary" href="#about">About Us</a></li>
                    <li><a class="text-sm text-muted-foreground hover:text-primary" href="#contact">Contact</a></li>
                    <li><a class="text-sm text-muted-foreground hover:text-primary" href="#privacy">Privacy Policy</a></li>
                    <li><a class="text-sm text-muted-foreground hover:text-primary" href="#terms">Terms of Service</a></li>
                </ul>
            </div>
        </div>

        <!-- Footer Bottom -->
        <div class="mt-12 pt-8 border-t">
            <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                <div class="flex items-center space-x-4">
                    <a class="text-sm text-muted-foreground hover:text-primary" href="#privacy">Privacy Policy</a>
                    <span class="text-muted-foreground">•</span>
                    <a class="text-sm text-muted-foreground hover:text-primary" href="#terms">Terms</a>
                    <span class="text-muted-foreground">•</span>
                    <a class="text-sm text-muted-foreground hover:text-primary" href="#contact">Contact</a>
                </div>
                <p class="text-sm text-muted-foreground">
                    Made with ❤️ © 2025 Calculator Suite
                </p>
            </div>
        </div>
    </div>
</footer>

<!-- JavaScript for Header/Footer functionality -->
<script>
// Mobile menu toggle
function toggleMobileMenu() {
    const mobileMenu = document.getElementById('mobile-menu');
    mobileMenu.classList.toggle('hidden');
}

// Dismiss banner
function dismissBanner() {
    const banner = document.querySelector('.bg-green-100\\/90');
    if (banner) {
        banner.style.display = 'none';
    }
}

// Theme toggle functionality
function toggleTheme() {
    const html = document.documentElement;
    const currentTheme = html.classList.contains('dark') ? 'dark' : 'light';
    
    if (currentTheme === 'light') {
        html.classList.remove('light');
        html.classList.add('dark');
        html.style.colorScheme = 'dark';
        localStorage.setItem('theme', 'dark');
    } else {
        html.classList.remove('dark');
        html.classList.add('light');
        html.style.colorScheme = 'light';
        localStorage.setItem('theme', 'light');
    }
}

// Initialize theme from localStorage
function initializeTheme() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    const html = document.documentElement;
    
    html.classList.remove('light', 'dark');
    html.classList.add(savedTheme);
    html.style.colorScheme = savedTheme;
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    initializeTheme();
});
</script>
