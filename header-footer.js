// Universal Header/Footer JavaScript for Calculator Suite

// Mobile menu toggle
function toggleMobileMenu() {
    const mobileMenu = document.getElementById('mobile-menu');
    if (mobileMenu) {
        mobileMenu.classList.toggle('hidden');
    }
}

// Dismiss banner
function dismissBanner() {
    const banners = [
        '.bg-green-100\\/90',
        '.bg-blue-100\\/90', 
        '.bg-purple-100\\/90',
        '.bg-orange-100\\/90'
    ];
    
    banners.forEach(selector => {
        const banner = document.querySelector(selector);
        if (banner) {
            banner.style.display = 'none';
        }
    });
}

// Theme toggle functionality
function toggleTheme() {
    const html = document.documentElement;
    const currentTheme = html.classList.contains('dark') ? 'dark' : 'light';
    
    if (currentTheme === 'light') {
        html.classList.remove('light');
        html.classList.add('dark');
        html.style.colorScheme = 'dark';
        localStorage.setItem('theme', 'dark');
    } else {
        html.classList.remove('dark');
        html.classList.add('light');
        html.style.colorScheme = 'light';
        localStorage.setItem('theme', 'light');
    }
}

// Initialize theme from localStorage
function initializeTheme() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    const html = document.documentElement;
    
    html.classList.remove('light', 'dark');
    html.classList.add(savedTheme);
    html.style.colorScheme = savedTheme;
}

// Smooth scrolling for anchor links
function initializeSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Close mobile menu when clicking outside
function initializeMobileMenuClose() {
    document.addEventListener('click', function(event) {
        const mobileMenu = document.getElementById('mobile-menu');
        const menuButton = document.querySelector('[onclick="toggleMobileMenu()"]');
        
        if (mobileMenu && !mobileMenu.classList.contains('hidden')) {
            if (!mobileMenu.contains(event.target) && !menuButton.contains(event.target)) {
                mobileMenu.classList.add('hidden');
            }
        }
    });
}

// Initialize dropdown menus for desktop
function initializeDropdownMenus() {
    const dropdowns = document.querySelectorAll('.group');
    
    dropdowns.forEach(dropdown => {
        const trigger = dropdown.querySelector('.submenu-trigger');
        const menu = dropdown.querySelector('.absolute');
        
        if (trigger && menu) {
            let timeoutId;
            
            dropdown.addEventListener('mouseenter', () => {
                clearTimeout(timeoutId);
                menu.classList.remove('hidden');
            });
            
            dropdown.addEventListener('mouseleave', () => {
                timeoutId = setTimeout(() => {
                    menu.classList.add('hidden');
                }, 150);
            });
        }
    });
}

// Add active state to current page navigation
function setActiveNavigation() {
    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
    const navLinks = document.querySelectorAll('nav a[href]');
    
    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href === currentPage || (currentPage === '' && href === 'index.html')) {
            link.classList.add('bg-accent', 'text-accent-foreground');
        }
    });
}

// Initialize all header/footer functionality
function initializeHeaderFooter() {
    initializeTheme();
    initializeSmoothScrolling();
    initializeMobileMenuClose();
    initializeDropdownMenus();
    setActiveNavigation();
    
    // Add keyboard navigation support
    document.addEventListener('keydown', function(e) {
        // Escape key closes mobile menu
        if (e.key === 'Escape') {
            const mobileMenu = document.getElementById('mobile-menu');
            if (mobileMenu && !mobileMenu.classList.contains('hidden')) {
                mobileMenu.classList.add('hidden');
            }
        }
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initializeHeaderFooter);

// Export functions for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        toggleMobileMenu,
        dismissBanner,
        toggleTheme,
        initializeTheme,
        initializeHeaderFooter
    };
}
