<!DOCTYPE html>
<html lang="en" class="light" style="color-scheme: light;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GST Calculator | Australian GST Calculator | PayCal Australia</title>
    <meta name="description" content="Free Australian GST calculator. Calculate GST inclusive and exclusive prices, add or remove 10% GST from any amount. Perfect for businesses, contractors, and consumers.">
    <meta name="keywords" content="gst calculator,australian gst calculator,gst inclusive calculator,gst exclusive calculator,10% gst calculator,goods and services tax calculator,add gst calculator,remove gst calculator">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        border: "hsl(var(--border))",
                        input: "hsl(var(--input))",
                        ring: "hsl(var(--ring))",
                        background: "hsl(var(--background))",
                        foreground: "hsl(var(--foreground))",
                        primary: {
                            DEFAULT: "hsl(var(--primary))",
                            foreground: "hsl(var(--primary-foreground))",
                        },
                        secondary: {
                            DEFAULT: "hsl(var(--secondary))",
                            foreground: "hsl(var(--secondary-foreground))",
                        },
                        destructive: {
                            DEFAULT: "hsl(var(--destructive))",
                            foreground: "hsl(var(--destructive-foreground))",
                        },
                        muted: {
                            DEFAULT: "hsl(var(--muted))",
                            foreground: "hsl(var(--muted-foreground))",
                        },
                        accent: {
                            DEFAULT: "hsl(var(--accent))",
                            foreground: "hsl(var(--accent-foreground))",
                        },
                        popover: {
                            DEFAULT: "hsl(var(--popover))",
                            foreground: "hsl(var(--popover-foreground))",
                        },
                        card: {
                            DEFAULT: "hsl(var(--card))",
                            foreground: "hsl(var(--card-foreground))",
                        },
                    },
                }
            }
        }
    </script>
    <style>
        :root {
            --background: 0 0% 100%;
            --foreground: 222.2 84% 4.9%;
            --card: 0 0% 100%;
            --card-foreground: 222.2 84% 4.9%;
            --popover: 0 0% 100%;
            --popover-foreground: 222.2 84% 4.9%;
            --primary: 222.2 47.4% 11.2%;
            --primary-foreground: 210 40% 98%;
            --secondary: 210 40% 96%;
            --secondary-foreground: 222.2 84% 4.9%;
            --muted: 210 40% 96%;
            --muted-foreground: 215.4 16.3% 46.9%;
            --accent: 210 40% 96%;
            --accent-foreground: 222.2 84% 4.9%;
            --destructive: 0 84.2% 60.2%;
            --destructive-foreground: 210 40% 98%;
            --border: 214.3 31.8% 91.4%;
            --input: 214.3 31.8% 91.4%;
            --ring: 222.2 84% 4.9%;
            --radius: 0.5rem;
        }

        .dark {
            --background: 222.2 84% 4.9%;
            --foreground: 210 40% 98%;
            --card: 222.2 84% 4.9%;
            --card-foreground: 210 40% 98%;
            --popover: 222.2 84% 4.9%;
            --popover-foreground: 210 40% 98%;
            --primary: 210 40% 98%;
            --primary-foreground: 222.2 47.4% 11.2%;
            --secondary: 217.2 32.6% 17.5%;
            --secondary-foreground: 210 40% 98%;
            --muted: 217.2 32.6% 17.5%;
            --muted-foreground: 215 20.2% 65.1%;
            --accent: 217.2 32.6% 17.5%;
            --accent-foreground: 210 40% 98%;
            --destructive: 0 62.8% 30.6%;
            --destructive-foreground: 210 40% 98%;
            --border: 217.2 32.6% 17.5%;
            --input: 217.2 32.6% 17.5%;
            --ring: 212.7 26.8% 83.9%;
        }

        * {
            border-color: hsl(var(--border));
        }

        body {
            background-color: hsl(var(--background));
            color: hsl(var(--foreground));
        }

        .calculator-container {
            background: hsl(var(--card));
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
        }

        .input-group {
            margin-bottom: 1rem;
        }

        .input-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: hsl(var(--foreground));
        }

        .input-group input, .input-group select {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
            background: hsl(var(--background));
            color: hsl(var(--foreground));
        }

        .input-group input:focus, .input-group select:focus {
            outline: none;
            border-color: hsl(var(--ring));
            box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
        }

        .btn-primary {
            background: hsl(var(--primary));
            color: hsl(var(--primary-foreground));
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--radius);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary:hover {
            background: hsl(var(--primary) / 0.9);
        }

        .btn-secondary {
            background: hsl(var(--secondary));
            color: hsl(var(--secondary-foreground));
            padding: 0.75rem 1.5rem;
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            margin-right: 0.5rem;
        }

        .btn-secondary:hover {
            background: hsl(var(--accent));
        }

        .btn-secondary.active {
            background: hsl(var(--primary));
            color: hsl(var(--primary-foreground));
        }

        .results-container {
            background: hsl(var(--muted));
            padding: 1.5rem;
            border-radius: var(--radius);
            margin-top: 1rem;
        }

        .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid hsl(var(--border));
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .result-label {
            font-weight: 500;
            color: hsl(var(--foreground));
        }

        .result-value {
            font-weight: 600;
            color: hsl(var(--primary));
            font-size: 1.1rem;
        }

        .gst-breakdown {
            background: #f0fdf4;
            border: 1px solid #16a34a;
            border-radius: var(--radius);
            padding: 1rem;
            margin: 1rem 0;
        }

        .gst-breakdown h4 {
            color: #166534;
            margin: 0 0 0.5rem 0;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .calculator-container {
                padding: 1rem;
            }
            .btn-secondary {
                margin-bottom: 0.5rem;
                margin-right: 0;
                width: 100%;
            }
        }
    </style>
</head>

<body class="min-h-screen bg-background font-sans antialiased">
    <div class="relative flex min-h-screen flex-col">
        <!-- PayCal Australia Header Component -->
        <div class="bg-orange-100/90 dark:bg-orange-900/30 border-b border-orange-200 dark:border-orange-800/30 transition-colors duration-500">
            <div class="container mx-auto px-4 py-3">
                <div class="flex items-center justify-between gap-x-4">
                    <div class="flex items-center gap-x-2 flex-1">
                        <span class="hidden sm:inline">💰 New!</span>
                        <span class="text-sm sm:text-base">Calculate <a class="font-semibold text-orange-600 dark:text-orange-400 hover:underline" href="#calculator">GST instantly</a><span class="hidden sm:inline"> - Add or remove 10% GST!</span></span>
                    </div>
                    <div class="flex items-center gap-x-1 sm:gap-x-2">
                        <div class="hidden sm:flex items-center gap-x-1 mr-2">
                            <button class="w-2 h-2 rounded-full transition-colors bg-orange-600 dark:text-orange-400" aria-label="Show promotion 1"></button>
                            <button class="w-2 h-2 rounded-full transition-colors bg-muted-foreground/30 hover:bg-orange-600/50" aria-label="Show promotion 2"></button>
                            <button class="w-2 h-2 rounded-full transition-colors bg-muted-foreground/30 hover:bg-orange-600/50" aria-label="Show promotion 3"></button>
                        </div>
                        <a class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-9 rounded-md px-3 hidden sm:inline-flex border-orange-300 dark:border-orange-700 hover:bg-orange-200/70 dark:hover:bg-orange-800/30" href="#calculator">Calculate GST</a>
                        <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-8 w-8 hover:bg-orange-200/70 dark:hover:bg-orange-800/30" onclick="dismissBanner()">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                                <path d="M18 6 6 18"></path>
                                <path d="m6 6 12 12"></path>
                            </svg>
                            <span class="sr-only">Dismiss</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <main class="flex-1">
            <div class="container mx-auto px-4 py-8">
                <!-- Hero Section -->
                <div class="text-center mb-8">
                    <h1 class="text-4xl font-bold tracking-tight text-foreground mb-4">
                        GST Calculator
                    </h1>
                    <p class="text-xl text-muted-foreground max-w-2xl mx-auto">
                        Calculate GST inclusive and exclusive prices. Add or remove 10% GST from any amount with our free Australian GST calculator.
                    </p>
                </div>

                <!-- Calculator Section -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-4xl mx-auto" id="calculator">
                    <!-- Input Form -->
                    <div class="calculator-container p-6">
                        <h2 class="text-2xl font-semibold mb-4">GST Calculation</h2>

                        <!-- Calculation Type -->
                        <div class="mb-6">
                            <label class="text-sm font-medium mb-3 block">Calculation Type</label>
                            <div class="flex gap-2">
                                <button
                                    type="button"
                                    id="add-gst-btn"
                                    class="btn-secondary active flex-1"
                                    onclick="setCalculationType('add')"
                                >
                                    Add GST
                                </button>
                                <button
                                    type="button"
                                    id="remove-gst-btn"
                                    class="btn-secondary flex-1"
                                    onclick="setCalculationType('remove')"
                                >
                                    Remove GST
                                </button>
                            </div>
                        </div>

                        <!-- Amount Input -->
                        <div class="input-group">
                            <label for="amount" id="amount-label">Amount (GST Exclusive)</label>
                            <div class="flex border h-10 w-full rounded-md border-input bg-background">
                                <span class="flex items-center px-3 h-10">$</span>
                                <input
                                    type="number"
                                    id="amount"
                                    class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-l-none m-[-1]"
                                    min="0"
                                    step="0.01"
                                    placeholder="Enter amount"
                                    value="100"
                                    oninput="calculateGST()"
                                />
                            </div>
                        </div>

                        <!-- GST Rate -->
                        <div class="input-group">
                            <label for="gstRate">GST Rate (%)</label>
                            <input
                                type="number"
                                id="gstRate"
                                class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                value="10"
                                step="0.1"
                                min="0"
                                max="100"
                                oninput="calculateGST()"
                            />
                        </div>

                        <!-- Quick Amount Buttons -->
                        <div class="mt-6">
                            <label class="text-sm font-medium mb-3 block">Quick Amounts</label>
                            <div class="grid grid-cols-3 gap-2">
                                <button type="button" class="btn-secondary" onclick="setAmount(100)">$100</button>
                                <button type="button" class="btn-secondary" onclick="setAmount(500)">$500</button>
                                <button type="button" class="btn-secondary" onclick="setAmount(1000)">$1,000</button>
                                <button type="button" class="btn-secondary" onclick="setAmount(5000)">$5,000</button>
                                <button type="button" class="btn-secondary" onclick="setAmount(10000)">$10,000</button>
                                <button type="button" class="btn-secondary" onclick="setAmount(50000)">$50,000</button>
                            </div>
                        </div>
                    </div>

                    <!-- Results Section -->
                    <div class="calculator-container p-6">
                        <h2 class="text-2xl font-semibold mb-4">GST Breakdown</h2>

                        <div class="results-container" id="gst-results">
                            <div class="result-item">
                                <span class="result-label">Amount (Ex GST)</span>
                                <span class="result-value" id="amount-ex-gst">$100.00</span>
                            </div>
                            <div class="result-item">
                                <span class="result-label">GST Amount</span>
                                <span class="result-value positive" id="gst-amount">$10.00</span>
                            </div>
                            <div class="result-item border-t-2 border-primary pt-4">
                                <span class="result-label font-semibold">Amount (Inc GST)</span>
                                <span class="result-value positive text-xl font-bold" id="amount-inc-gst">$110.00</span>
                            </div>
                        </div>

                        <!-- GST Breakdown -->
                        <div class="gst-breakdown">
                            <h4>GST Calculation Formula</h4>
                            <div class="text-sm space-y-2" id="formula-display">
                                <div>GST Amount = $100.00 × 10% = $10.00</div>
                                <div>Total Amount = $100.00 + $10.00 = $110.00</div>
                            </div>
                        </div>

                        <!-- Visual Chart -->
                        <div class="mt-6">
                            <canvas id="gstChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="border-t bg-muted/50">
            <div class="container mx-auto px-4 py-8">
                <div class="text-center">
                    <p class="text-sm text-muted-foreground">
                        © 2024 PayCal.com.au. Made for Aussies ❤️
                    </p>
                </div>
            </div>
        </footer>
    </div>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
        let gstChart = null;
        let calculationType = 'add';

        document.addEventListener('DOMContentLoaded', function() {
            calculateGST();
            initializeGSTChart();
        });

        function setCalculationType(type) {
            calculationType = type;
            document.getElementById('add-gst-btn').classList.toggle('active', type === 'add');
            document.getElementById('remove-gst-btn').classList.toggle('active', type === 'remove');
            document.getElementById('amount-label').textContent = type === 'add' ? 'Amount (GST Exclusive)' : 'Amount (GST Inclusive)';
            calculateGST();
        }

        function setAmount(value) {
            document.getElementById('amount').value = value;
            calculateGST();
        }

        function calculateGST() {
            const amount = parseFloat(document.getElementById('amount').value || 0);
            const gstRate = parseFloat(document.getElementById('gstRate').value || 10) / 100;

            let amountExGST, gstAmount, amountIncGST;

            if (calculationType === 'add') {
                amountExGST = amount;
                gstAmount = amount * gstRate;
                amountIncGST = amount + gstAmount;
            } else {
                amountIncGST = amount;
                amountExGST = amount / (1 + gstRate);
                gstAmount = amountIncGST - amountExGST;
            }

            document.getElementById('amount-ex-gst').textContent = formatCurrency(amountExGST);
            document.getElementById('gst-amount').textContent = formatCurrency(gstAmount);
            document.getElementById('amount-inc-gst').textContent = formatCurrency(amountIncGST);

            updateFormula(amountExGST, gstAmount, amountIncGST, gstRate);
            updateGSTChart(amountExGST, gstAmount);
        }

        function updateFormula(amountExGST, gstAmount, amountIncGST, gstRate) {
            const formulaDiv = document.getElementById('formula-display');
            const ratePercent = (gstRate * 100).toFixed(1);

            if (calculationType === 'add') {
                formulaDiv.innerHTML = `
                    <div>GST Amount = ${formatCurrency(amountExGST)} × ${ratePercent}% = ${formatCurrency(gstAmount)}</div>
                    <div>Total Amount = ${formatCurrency(amountExGST)} + ${formatCurrency(gstAmount)} = ${formatCurrency(amountIncGST)}</div>
                `;
            } else {
                formulaDiv.innerHTML = `
                    <div>Amount Ex GST = ${formatCurrency(amountIncGST)} ÷ 1.${ratePercent.replace('.', '')} = ${formatCurrency(amountExGST)}</div>
                    <div>GST Amount = ${formatCurrency(amountIncGST)} - ${formatCurrency(amountExGST)} = ${formatCurrency(gstAmount)}</div>
                `;
            }
        }

        function formatCurrency(amount) {
            return new Intl.NumberFormat('en-AU', {
                style: 'currency',
                currency: 'AUD',
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(amount);
        }

        function initializeGSTChart() {
            const ctx = document.getElementById('gstChart').getContext('2d');
            gstChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Amount (Ex GST)', 'GST Amount'],
                    datasets: [{
                        data: [100, 10],
                        backgroundColor: ['#3b82f6', '#f59e0b'],
                        borderWidth: 2,
                        borderColor: '#ffffff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: { padding: 20, usePointStyle: true }
                        }
                    }
                }
            });
        }

        function updateGSTChart(amountExGST, gstAmount) {
            if (gstChart) {
                gstChart.data.datasets[0].data = [amountExGST, gstAmount];
                gstChart.update();
            }
        }

        function dismissBanner() {
            const banner = document.querySelector('.bg-orange-100\\/90');
            if (banner) banner.style.display = 'none';
        }
    </script>
</body>
</html>
