<!DOCTYPE html>
<html lang="en" class="light" style="color-scheme: light;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Australian Pay Calculator | Salary, Tax & Super Calculator | PayCal Australia</title>
    <meta name="description" content="Free Australian pay calculator to calculate your take-home salary, tax, superannuation and Medicare levy. Accurate calculations for employees and contractors across all Australian states.">
    <meta name="keywords" content="australian pay calculator,salary calculator,tax calculator,superannuation calculator,take home pay calculator,payroll calculator,income tax calculator,medicare levy calculator,australian tax calculator,pay slip calculator">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        border: "hsl(var(--border))",
                        input: "hsl(var(--input))",
                        ring: "hsl(var(--ring))",
                        background: "hsl(var(--background))",
                        foreground: "hsl(var(--foreground))",
                        primary: {
                            DEFAULT: "hsl(var(--primary))",
                            foreground: "hsl(var(--primary-foreground))",
                        },
                        secondary: {
                            DEFAULT: "hsl(var(--secondary))",
                            foreground: "hsl(var(--secondary-foreground))",
                        },
                        destructive: {
                            DEFAULT: "hsl(var(--destructive))",
                            foreground: "hsl(var(--destructive-foreground))",
                        },
                        muted: {
                            DEFAULT: "hsl(var(--muted))",
                            foreground: "hsl(var(--muted-foreground))",
                        },
                        accent: {
                            DEFAULT: "hsl(var(--accent))",
                            foreground: "hsl(var(--accent-foreground))",
                        },
                        popover: {
                            DEFAULT: "hsl(var(--popover))",
                            foreground: "hsl(var(--popover-foreground))",
                        },
                        card: {
                            DEFAULT: "hsl(var(--card))",
                            foreground: "hsl(var(--card-foreground))",
                        },
                    },
                }
            }
        }
    </script>
    <style>
        :root {
            --background: 0 0% 100%;
            --foreground: 222.2 84% 4.9%;
            --card: 0 0% 100%;
            --card-foreground: 222.2 84% 4.9%;
            --popover: 0 0% 100%;
            --popover-foreground: 222.2 84% 4.9%;
            --primary: 222.2 47.4% 11.2%;
            --primary-foreground: 210 40% 98%;
            --secondary: 210 40% 96%;
            --secondary-foreground: 222.2 84% 4.9%;
            --muted: 210 40% 96%;
            --muted-foreground: 215.4 16.3% 46.9%;
            --accent: 210 40% 96%;
            --accent-foreground: 222.2 84% 4.9%;
            --destructive: 0 84.2% 60.2%;
            --destructive-foreground: 210 40% 98%;
            --border: 214.3 31.8% 91.4%;
            --input: 214.3 31.8% 91.4%;
            --ring: 222.2 84% 4.9%;
            --radius: 0.5rem;
        }

        .dark {
            --background: 222.2 84% 4.9%;
            --foreground: 210 40% 98%;
            --card: 222.2 84% 4.9%;
            --card-foreground: 210 40% 98%;
            --popover: 222.2 84% 4.9%;
            --popover-foreground: 210 40% 98%;
            --primary: 210 40% 98%;
            --primary-foreground: 222.2 47.4% 11.2%;
            --secondary: 217.2 32.6% 17.5%;
            --secondary-foreground: 210 40% 98%;
            --muted: 217.2 32.6% 17.5%;
            --muted-foreground: 215 20.2% 65.1%;
            --accent: 217.2 32.6% 17.5%;
            --accent-foreground: 210 40% 98%;
            --destructive: 0 62.8% 30.6%;
            --destructive-foreground: 210 40% 98%;
            --border: 217.2 32.6% 17.5%;
            --input: 217.2 32.6% 17.5%;
            --ring: 212.7 26.8% 83.9%;
        }

        * {
            border-color: hsl(var(--border));
        }

        body {
            background-color: hsl(var(--background));
            color: hsl(var(--foreground));
        }

        .calculator-container {
            background: hsl(var(--card));
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
        }

        .input-group {
            margin-bottom: 1rem;
        }

        .input-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: hsl(var(--foreground));
        }

        .input-group input, .input-group select {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
            background: hsl(var(--background));
            color: hsl(var(--foreground));
        }

        .input-group input:focus, .input-group select:focus {
            outline: none;
            border-color: hsl(var(--ring));
            box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
        }

        .btn-primary {
            background: hsl(var(--primary));
            color: hsl(var(--primary-foreground));
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--radius);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary:hover {
            background: hsl(var(--primary) / 0.9);
        }

        .results-container {
            background: hsl(var(--muted));
            padding: 1.5rem;
            border-radius: var(--radius);
            margin-top: 1rem;
        }

        .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid hsl(var(--border));
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .result-label {
            font-weight: 500;
            color: hsl(var(--foreground));
        }

        .result-value {
            font-weight: 600;
            color: hsl(var(--primary));
            font-size: 1.1rem;
        }

        .result-value.positive {
            color: #16a34a;
        }

        .result-value.negative {
            color: #dc2626;
        }

        @media (max-width: 768px) {
            .calculator-container {
                padding: 1rem;
            }
            .input-group {
                flex-direction: column;
            }
        }
    </style>
</head>

<body class="min-h-screen bg-background font-sans antialiased">
    <div class="relative flex min-h-screen flex-col">
        <!-- PayCal Australia Header Component -->
        <div class="bg-orange-100/90 dark:bg-orange-900/30 border-b border-orange-200 dark:border-orange-800/30 transition-colors duration-500">
            <div class="container mx-auto px-4 py-3">
                <div class="flex items-center justify-between gap-x-4">
                    <div class="flex items-center gap-x-2 flex-1">
                        <span class="hidden sm:inline">📄 New!</span>
                        <span class="text-sm sm:text-base">Try our free <a class="font-semibold text-orange-600 dark:text-orange-400 hover:underline" href="invoice-generator.html">Invoice Generator</a><span class="hidden sm:inline"> - Create professional invoices in minutes!</span></span>
                    </div>
                    <div class="flex items-center gap-x-1 sm:gap-x-2">
                        <div class="hidden sm:flex items-center gap-x-1 mr-2">
                            <button class="w-2 h-2 rounded-full transition-colors bg-orange-600 dark:text-orange-400" aria-label="Show promotion 1"></button>
                            <button class="w-2 h-2 rounded-full transition-colors bg-muted-foreground/30 hover:bg-orange-600/50" aria-label="Show promotion 2"></button>
                            <button class="w-2 h-2 rounded-full transition-colors bg-muted-foreground/30 hover:bg-orange-600/50" aria-label="Show promotion 3"></button>
                        </div>
                        <a class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-9 rounded-md px-3 hidden sm:inline-flex border-orange-300 dark:border-orange-700 hover:bg-orange-200/70 dark:hover:bg-orange-800/30" href="invoice-generator.html">Try Now</a>
                        <a class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-8 w-8 sm:hidden border-orange-300 dark:border-orange-700 hover:bg-orange-200/70 dark:hover:bg-orange-800/30" href="invoice-generator.html">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                                <path d="M5 12h14"></path>
                                <path d="m12 5 7 7-7 7"></path>
                            </svg>
                            <span class="sr-only">Try Invoice Generator</span>
                        </a>
                        <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-8 w-8 hover:bg-orange-200/70 dark:hover:bg-orange-800/30" onclick="dismissBanner()">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                                <path d="M18 6 6 18"></path>
                                <path d="m6 6 12 12"></path>
                            </svg>
                            <span class="sr-only">Dismiss</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- PayCal Australia Main Header -->
        <header class="supports-backdrop-blur:bg-background/60 sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur">
            <div class="container flex h-14 items-center">
                <!-- Mobile Menu Button -->
                <button class="inline-flex items-center justify-center rounded-md font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-9 w-9 md:hidden" onclick="toggleMobileMenu()">
                    <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none" class="h-5 w-5">
                        <path d="M8 2H13.5C13.7761 2 14 2.22386 14 2.5V12.5C14 12.7761 13.7761 13 13.5 13H8V2ZM7 2H1.5C1.22386 2 1 2.22386 1 2.5V12.5C1 12.7761 1.22386 13 1.5 13H7V2ZM0 2.5C0 1.67157 0.671573 1 1.5 1H13.5C14.3284 1 15 1.67157 15 2.5V12.5C15 13.3284 14.3284 14 13.5 14H1.5C0.671573 14 0 13.3284 0 12.5V2.5Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path>
                    </svg>
                    <span class="sr-only">Toggle Menu</span>
                </button>

                <!-- Desktop Navigation -->
                <div class="mr-4 hidden md:flex">
                    <nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex max-w-max flex-1 items-center justify-center">
                        <!-- PayCal Logo -->
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256" class="h-6 w-6 dark:text-white" aria-hidden="true">
                            <path d="M0 0h256v256H0z" fill="none"></path>
                            <rect width="256" height="256" fill="none" stroke="currentColor" stroke-width="16"></rect>
                            <rect x="32" y="32" width="192" height="64" fill="currentColor"></rect>
                            <circle cx="64" cy="128" r="16" fill="currentColor"></circle>
                            <circle cx="128" cy="128" r="16" fill="currentColor"></circle>
                            <circle cx="192" cy="128" r="16" fill="currentColor"></circle>
                            <circle cx="64" cy="176" r="16" fill="currentColor"></circle>
                            <circle cx="128" cy="176" r="16" fill="currentColor"></circle>
                            <circle cx="192" cy="176" r="16" fill="currentColor"></circle>
                            <circle cx="64" cy="224" r="16" fill="currentColor"></circle>
                            <circle cx="128" cy="224" r="16" fill="currentColor"></circle>
                            <circle cx="192" cy="224" r="16" fill="currentColor"></circle>
                        </svg>

                        <div style="position:relative">
                            <ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr">
                                <li>
                                    <a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="index.html">
                                        Paycal.com.au
                                    </a>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger bg-accent/50">
                                            Pay Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden group-hover:block bg-background border rounded-md shadow-lg min-w-[250px] z-50">
                                            <div class="p-2">
                                                <a href="australian-pay-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md bg-accent/50">Australian Pay Calculator</a>
                                                <a href="contractor-pay-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Contractor Pay Calculator</a>
                                                <a href="#" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Contract Rate Benchmarking</a>
                                                <a href="#" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Salary Rate Benchmarking</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Home & Property Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden group-hover:block bg-background border rounded-md shadow-lg min-w-[250px] z-50">
                                            <div class="p-2">
                                                <a href="mortgage-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Mortgage Calculator</a>
                                                <a href="home-loan-comparison.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Home Loan Comparison</a>
                                                <a href="#" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Rate Cut Calculator</a>
                                                <a href="#" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Mortgage Payoff Calculator</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Tax Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden group-hover:block bg-background border rounded-md shadow-lg min-w-[200px] z-50">
                                            <div class="p-2">
                                                <a href="tax-cut-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Tax Cut Calculator</a>
                                                <a href="#" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Tax Calendar</a>
                                                <a href="gst-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">GST Calculator</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Financial Tools
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden group-hover:block bg-background border rounded-md shadow-lg min-w-[200px] z-50">
                                            <div class="p-2">
                                                <a href="invoice-generator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Invoice Generator</a>
                                                <a href="#" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Quote Generator</a>
                                                <a href="#" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Payslip Generator</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Vehicle Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden group-hover:block bg-background border rounded-md shadow-lg min-w-[250px] z-50">
                                            <div class="p-2">
                                                <a href="novated-lease-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">EV & ICE Novated Lease</a>
                                                <a href="#" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Vehicle Registration</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Holidays Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden group-hover:block bg-background border rounded-md shadow-lg min-w-[200px] z-50">
                                            <div class="p-2">
                                                <a href="public-holidays.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Public Holidays</a>
                                                <a href="school-holidays.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">School Holidays</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="#recommended">
                                        Recommended
                                    </a>
                                </li>
                            </ul>
                        </div>
                        <div class="absolute left-0 top-full flex justify-center"></div>
                    </nav>
                </div>

                <!-- Right Side Actions -->
                <div class="container flex h-16 items-center space-x-4 sm:justify-between sm:space-x-0">
                    <div class="flex flex-1 items-center justify-end space-x-4">
                        <nav class="flex items-center space-x-1">
                            <!-- Theme Toggle -->
                            <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 w-10" onclick="toggleTheme()">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-[1.5rem] w-[1.3rem] dark:hidden">
                                    <circle cx="12" cy="12" r="4"></circle>
                                    <path d="M12 2v2"></path>
                                    <path d="M12 20v2"></path>
                                    <path d="m4.93 4.93 1.41 1.41"></path>
                                    <path d="m17.66 17.66 1.41 1.41"></path>
                                    <path d="M2 12h2"></path>
                                    <path d="M20 12h2"></path>
                                    <path d="m6.34 17.66-1.41 1.41"></path>
                                    <path d="m19.07 4.93-1.41 1.41"></path>
                                </svg>
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="hidden h-5 w-5 dark:block dark:text-white">
                                    <path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"></path>
                                </svg>
                                <span class="sr-only">Toggle theme</span>
                            </button>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- Mobile Menu -->
            <div id="mobile-menu" class="hidden md:hidden border-t bg-background">
                <div class="container px-4 py-4">
                    <nav class="space-y-2">
                        <a href="index.html" class="block px-3 py-2 text-sm font-medium hover:bg-accent rounded-md">Paycal.com.au</a>
                        <div class="space-y-1">
                            <div class="px-3 py-2 text-sm font-medium text-muted-foreground">Pay Calculators</div>
                            <a href="australian-pay-calculator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md bg-accent/50">Australian Pay Calculator</a>
                            <a href="contractor-pay-calculator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Contractor Pay Calculator</a>
                            <a href="#" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Contract Rate Benchmarking</a>
                            <a href="#" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Salary Rate Benchmarking</a>
                        </div>
                        <div class="space-y-1">
                            <div class="px-3 py-2 text-sm font-medium text-muted-foreground">Home & Property Calculators</div>
                            <a href="mortgage-calculator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Mortgage Calculator</a>
                            <a href="home-loan-comparison.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Home Loan Comparison</a>
                            <a href="#" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Rate Cut Calculator</a>
                            <a href="#" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Mortgage Payoff Calculator</a>
                        </div>
                        <div class="space-y-1">
                            <div class="px-3 py-2 text-sm font-medium text-muted-foreground">Tax Calculators</div>
                            <a href="tax-cut-calculator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Tax Cut Calculator</a>
                            <a href="#" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Tax Calendar</a>
                            <a href="gst-calculator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">GST Calculator</a>
                        </div>
                        <div class="space-y-1">
                            <div class="px-3 py-2 text-sm font-medium text-muted-foreground">Financial Tools</div>
                            <a href="invoice-generator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Invoice Generator</a>
                            <a href="#" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Quote Generator</a>
                            <a href="#" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Payslip Generator</a>
                        </div>
                        <div class="space-y-1">
                            <div class="px-3 py-2 text-sm font-medium text-muted-foreground">Vehicle Calculators</div>
                            <a href="novated-lease-calculator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">EV & ICE Novated Lease</a>
                            <a href="#" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Vehicle Registration</a>
                        </div>
                        <div class="space-y-1">
                            <div class="px-3 py-2 text-sm font-medium text-muted-foreground">Holidays Calculators</div>
                            <a href="public-holidays.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Public Holidays</a>
                            <a href="school-holidays.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">School Holidays</a>
                        </div>
                        <a href="#recommended" class="block px-3 py-2 text-sm font-medium hover:bg-accent rounded-md">Recommended</a>
                    </nav>
                </div>
            </div>
        </header>
