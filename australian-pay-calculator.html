<!DOCTYPE html>
<html lang="en" class="light" style="color-scheme: light;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Australian Pay Calculator | Salary, Tax & Super Calculator | PayCal Australia</title>
    <meta name="description" content="Free Australian pay calculator to calculate your take-home salary, tax, superannuation and Medicare levy. Accurate calculations for employees and contractors across all Australian states.">
    <meta name="keywords" content="australian pay calculator,salary calculator,tax calculator,superannuation calculator,take home pay calculator,payroll calculator,income tax calculator,medicare levy calculator,australian tax calculator,pay slip calculator">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        border: "hsl(var(--border))",
                        input: "hsl(var(--input))",
                        ring: "hsl(var(--ring))",
                        background: "hsl(var(--background))",
                        foreground: "hsl(var(--foreground))",
                        primary: {
                            DEFAULT: "hsl(var(--primary))",
                            foreground: "hsl(var(--primary-foreground))",
                        },
                        secondary: {
                            DEFAULT: "hsl(var(--secondary))",
                            foreground: "hsl(var(--secondary-foreground))",
                        },
                        destructive: {
                            DEFAULT: "hsl(var(--destructive))",
                            foreground: "hsl(var(--destructive-foreground))",
                        },
                        muted: {
                            DEFAULT: "hsl(var(--muted))",
                            foreground: "hsl(var(--muted-foreground))",
                        },
                        accent: {
                            DEFAULT: "hsl(var(--accent))",
                            foreground: "hsl(var(--accent-foreground))",
                        },
                        popover: {
                            DEFAULT: "hsl(var(--popover))",
                            foreground: "hsl(var(--popover-foreground))",
                        },
                        card: {
                            DEFAULT: "hsl(var(--card))",
                            foreground: "hsl(var(--card-foreground))",
                        },
                    },
                }
            }
        }
    </script>
    <style>
        :root {
            --background: 0 0% 100%;
            --foreground: 222.2 84% 4.9%;
            --card: 0 0% 100%;
            --card-foreground: 222.2 84% 4.9%;
            --popover: 0 0% 100%;
            --popover-foreground: 222.2 84% 4.9%;
            --primary: 222.2 47.4% 11.2%;
            --primary-foreground: 210 40% 98%;
            --secondary: 210 40% 96%;
            --secondary-foreground: 222.2 84% 4.9%;
            --muted: 210 40% 96%;
            --muted-foreground: 215.4 16.3% 46.9%;
            --accent: 210 40% 96%;
            --accent-foreground: 222.2 84% 4.9%;
            --destructive: 0 84.2% 60.2%;
            --destructive-foreground: 210 40% 98%;
            --border: 214.3 31.8% 91.4%;
            --input: 214.3 31.8% 91.4%;
            --ring: 222.2 84% 4.9%;
            --radius: 0.5rem;
        }

        .dark {
            --background: 222.2 84% 4.9%;
            --foreground: 210 40% 98%;
            --card: 222.2 84% 4.9%;
            --card-foreground: 210 40% 98%;
            --popover: 222.2 84% 4.9%;
            --popover-foreground: 210 40% 98%;
            --primary: 210 40% 98%;
            --primary-foreground: 222.2 47.4% 11.2%;
            --secondary: 217.2 32.6% 17.5%;
            --secondary-foreground: 210 40% 98%;
            --muted: 217.2 32.6% 17.5%;
            --muted-foreground: 215 20.2% 65.1%;
            --accent: 217.2 32.6% 17.5%;
            --accent-foreground: 210 40% 98%;
            --destructive: 0 62.8% 30.6%;
            --destructive-foreground: 210 40% 98%;
            --border: 217.2 32.6% 17.5%;
            --input: 217.2 32.6% 17.5%;
            --ring: 212.7 26.8% 83.9%;
        }

        * {
            border-color: hsl(var(--border));
        }

        body {
            background-color: hsl(var(--background));
            color: hsl(var(--foreground));
        }

        .calculator-container {
            background: hsl(var(--card));
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
        }

        .input-group {
            margin-bottom: 1rem;
        }

        .input-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: hsl(var(--foreground));
        }

        .input-group input, .input-group select {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
            background: hsl(var(--background));
            color: hsl(var(--foreground));
        }

        .input-group input:focus, .input-group select:focus {
            outline: none;
            border-color: hsl(var(--ring));
            box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
        }

        .btn-primary {
            background: hsl(var(--primary));
            color: hsl(var(--primary-foreground));
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--radius);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary:hover {
            background: hsl(var(--primary) / 0.9);
        }

        .results-container {
            background: hsl(var(--muted));
            padding: 1.5rem;
            border-radius: var(--radius);
            margin-top: 1rem;
        }

        .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid hsl(var(--border));
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .result-label {
            font-weight: 500;
            color: hsl(var(--foreground));
        }

        .result-value {
            font-weight: 600;
            color: hsl(var(--primary));
            font-size: 1.1rem;
        }

        .result-value.positive {
            color: #16a34a;
        }

        .result-value.negative {
            color: #dc2626;
        }

        @media (max-width: 768px) {
            .calculator-container {
                padding: 1rem;
            }
            .input-group {
                flex-direction: column;
            }
        }
    </style>
</head>

<body class="min-h-screen bg-background font-sans antialiased">
    <div class="relative flex min-h-screen flex-col">
        <!-- PayCal Australia Header Component -->
        <div class="bg-orange-100/90 dark:bg-orange-900/30 border-b border-orange-200 dark:border-orange-800/30 transition-colors duration-500">
            <div class="container mx-auto px-4 py-3">
                <div class="flex items-center justify-between gap-x-4">
                    <div class="flex items-center gap-x-2 flex-1">
                        <span class="hidden sm:inline">📄 New!</span>
                        <span class="text-sm sm:text-base">Try our free <a class="font-semibold text-orange-600 dark:text-orange-400 hover:underline" href="invoice-generator.html">Invoice Generator</a><span class="hidden sm:inline"> - Create professional invoices in minutes!</span></span>
                    </div>
                    <div class="flex items-center gap-x-1 sm:gap-x-2">
                        <div class="hidden sm:flex items-center gap-x-1 mr-2">
                            <button class="w-2 h-2 rounded-full transition-colors bg-orange-600 dark:text-orange-400" aria-label="Show promotion 1"></button>
                            <button class="w-2 h-2 rounded-full transition-colors bg-muted-foreground/30 hover:bg-orange-600/50" aria-label="Show promotion 2"></button>
                            <button class="w-2 h-2 rounded-full transition-colors bg-muted-foreground/30 hover:bg-orange-600/50" aria-label="Show promotion 3"></button>
                        </div>
                        <a class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-9 rounded-md px-3 hidden sm:inline-flex border-orange-300 dark:border-orange-700 hover:bg-orange-200/70 dark:hover:bg-orange-800/30" href="invoice-generator.html">Try Now</a>
                        <a class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-8 w-8 sm:hidden border-orange-300 dark:border-orange-700 hover:bg-orange-200/70 dark:hover:bg-orange-800/30" href="invoice-generator.html">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                                <path d="M5 12h14"></path>
                                <path d="m12 5 7 7-7 7"></path>
                            </svg>
                            <span class="sr-only">Try Invoice Generator</span>
                        </a>
                        <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-8 w-8 hover:bg-orange-200/70 dark:hover:bg-orange-800/30" onclick="dismissBanner()">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                                <path d="M18 6 6 18"></path>
                                <path d="m6 6 12 12"></path>
                            </svg>
                            <span class="sr-only">Dismiss</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- PayCal Australia Main Header -->
        <header class="supports-backdrop-blur:bg-background/60 sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur">
            <div class="container flex h-14 items-center">
                <!-- Mobile Menu Button -->
                <button class="inline-flex items-center justify-center rounded-md font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-9 w-9 md:hidden" onclick="toggleMobileMenu()">
                    <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none" class="h-5 w-5">
                        <path d="M8 2H13.5C13.7761 2 14 2.22386 14 2.5V12.5C14 12.7761 13.7761 13 13.5 13H8V2ZM7 2H1.5C1.22386 2 1 2.22386 1 2.5V12.5C1 12.7761 1.22386 13 1.5 13H7V2ZM0 2.5C0 1.67157 0.671573 1 1.5 1H13.5C14.3284 1 15 1.67157 15 2.5V12.5C15 13.3284 14.3284 14 13.5 14H1.5C0.671573 14 0 13.3284 0 12.5V2.5Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path>
                    </svg>
                    <span class="sr-only">Toggle Menu</span>
                </button>

                <!-- Desktop Navigation -->
                <div class="mr-4 hidden md:flex">
                    <nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex max-w-max flex-1 items-center justify-center">
                        <!-- PayCal Logo -->
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256" class="h-6 w-6 dark:text-white" aria-hidden="true">
                            <path d="M0 0h256v256H0z" fill="none"></path>
                            <rect width="256" height="256" fill="none" stroke="currentColor" stroke-width="16"></rect>
                            <rect x="32" y="32" width="192" height="64" fill="currentColor"></rect>
                            <circle cx="64" cy="128" r="16" fill="currentColor"></circle>
                            <circle cx="128" cy="128" r="16" fill="currentColor"></circle>
                            <circle cx="192" cy="128" r="16" fill="currentColor"></circle>
                            <circle cx="64" cy="176" r="16" fill="currentColor"></circle>
                            <circle cx="128" cy="176" r="16" fill="currentColor"></circle>
                            <circle cx="192" cy="176" r="16" fill="currentColor"></circle>
                            <circle cx="64" cy="224" r="16" fill="currentColor"></circle>
                            <circle cx="128" cy="224" r="16" fill="currentColor"></circle>
                            <circle cx="192" cy="224" r="16" fill="currentColor"></circle>
                        </svg>

                        <div style="position:relative">
                            <ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr">
                                <li>
                                    <a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="index.html">
                                        Paycal.com.au
                                    </a>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger bg-accent/50">
                                            Pay Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden group-hover:block bg-background border rounded-md shadow-lg min-w-[250px] z-50">
                                            <div class="p-2">
                                                <a href="australian-pay-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md bg-accent/50">Australian Pay Calculator</a>
                                                <a href="contractor-pay-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Contractor Pay Calculator</a>
                                                <a href="contract-rate-benchmarking.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Contract Rate Benchmarking</a>
                                                <a href="salary-rate-benchmarking.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Salary Rate Benchmarking</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Home & Property Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden group-hover:block bg-background border rounded-md shadow-lg min-w-[250px] z-50">
                                            <div class="p-2">
                                                <a href="mortgage-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Mortgage Calculator</a>
                                                <a href="home-loan-comparison.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Home Loan Comparison</a>
                                                <a href="rate-cut-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Rate Cut Calculator</a>
                                                <a href="mortgage-payoff-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Mortgage Payoff Calculator</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Tax Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden group-hover:block bg-background border rounded-md shadow-lg min-w-[200px] z-50">
                                            <div class="p-2">
                                                <a href="tax-cut-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Tax Cut Calculator</a>
                                                <a href="tax-calendar.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Tax Calendar</a>
                                                <a href="gst-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">GST Calculator</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Financial Tools
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden group-hover:block bg-background border rounded-md shadow-lg min-w-[200px] z-50">
                                            <div class="p-2">
                                                <a href="invoice-generator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Invoice Generator</a>
                                                <a href="quote-generator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Quote Generator</a>
                                                <a href="payslip-generator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Payslip Generator</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Vehicle Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden group-hover:block bg-background border rounded-md shadow-lg min-w-[250px] z-50">
                                            <div class="p-2">
                                                <a href="novated-lease-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">EV & ICE Novated Lease</a>
                                                <a href="vehicle-registration.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Vehicle Registration</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Holidays Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden group-hover:block bg-background border rounded-md shadow-lg min-w-[200px] z-50">
                                            <div class="p-2">
                                                <a href="public-holidays.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Public Holidays</a>
                                                <a href="school-holidays.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">School Holidays</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="#recommended">
                                        Recommended
                                    </a>
                                </li>
                            </ul>
                        </div>
                        <div class="absolute left-0 top-full flex justify-center"></div>
                    </nav>
                </div>

                <!-- Right Side Actions -->
                <div class="container flex h-16 items-center space-x-4 sm:justify-between sm:space-x-0">
                    <div class="flex flex-1 items-center justify-end space-x-4">
                        <nav class="flex items-center space-x-1">
                            <!-- Theme Toggle -->
                            <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 w-10" onclick="toggleTheme()">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-[1.5rem] w-[1.3rem] dark:hidden">
                                    <circle cx="12" cy="12" r="4"></circle>
                                    <path d="M12 2v2"></path>
                                    <path d="M12 20v2"></path>
                                    <path d="m4.93 4.93 1.41 1.41"></path>
                                    <path d="m17.66 17.66 1.41 1.41"></path>
                                    <path d="M2 12h2"></path>
                                    <path d="M20 12h2"></path>
                                    <path d="m6.34 17.66-1.41 1.41"></path>
                                    <path d="m19.07 4.93-1.41 1.41"></path>
                                </svg>
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="hidden h-5 w-5 dark:block dark:text-white">
                                    <path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"></path>
                                </svg>
                                <span class="sr-only">Toggle theme</span>
                            </button>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- Mobile Menu -->
            <div id="mobile-menu" class="hidden md:hidden border-t bg-background">
                <div class="container px-4 py-4">
                    <nav class="space-y-2">
                        <a href="index.html" class="block px-3 py-2 text-sm font-medium hover:bg-accent rounded-md">Paycal.com.au</a>
                        <div class="space-y-1">
                            <div class="px-3 py-2 text-sm font-medium text-muted-foreground">Pay Calculators</div>
                            <a href="australian-pay-calculator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md bg-accent/50">Australian Pay Calculator</a>
                            <a href="contractor-pay-calculator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Contractor Pay Calculator</a>
                            <a href="contract-rate-benchmarking.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Contract Rate Benchmarking</a>
                            <a href="salary-rate-benchmarking.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Salary Rate Benchmarking</a>
                        </div>
                        <div class="space-y-1">
                            <div class="px-3 py-2 text-sm font-medium text-muted-foreground">Home & Property Calculators</div>
                            <a href="mortgage-calculator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Mortgage Calculator</a>
                            <a href="home-loan-comparison.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Home Loan Comparison</a>
                            <a href="rate-cut-calculator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Rate Cut Calculator</a>
                            <a href="mortgage-payoff-calculator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Mortgage Payoff Calculator</a>
                        </div>
                        <div class="space-y-1">
                            <div class="px-3 py-2 text-sm font-medium text-muted-foreground">Tax Calculators</div>
                            <a href="tax-cut-calculator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Tax Cut Calculator</a>
                            <a href="tax-calendar.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Tax Calendar</a>
                            <a href="gst-calculator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">GST Calculator</a>
                        </div>
                        <div class="space-y-1">
                            <div class="px-3 py-2 text-sm font-medium text-muted-foreground">Financial Tools</div>
                            <a href="invoice-generator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Invoice Generator</a>
                            <a href="quote-generator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Quote Generator</a>
                            <a href="payslip-generator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Payslip Generator</a>
                        </div>
                        <div class="space-y-1">
                            <div class="px-3 py-2 text-sm font-medium text-muted-foreground">Vehicle Calculators</div>
                            <a href="novated-lease-calculator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">EV & ICE Novated Lease</a>
                            <a href="vehicle-registration.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Vehicle Registration</a>
                        </div>
                        <div class="space-y-1">
                            <div class="px-3 py-2 text-sm font-medium text-muted-foreground">Holidays Calculators</div>
                            <a href="public-holidays.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Public Holidays</a>
                            <a href="school-holidays.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">School Holidays</a>
                        </div>
                        <a href="#recommended" class="block px-3 py-2 text-sm font-medium hover:bg-accent rounded-md">Recommended</a>
                    </nav>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="flex-1">
            <div class="container mx-auto px-4 py-8">
                <!-- Hero Section -->
                <div class="text-center mb-8">
                    <div class="mb-6">
                        <div class="inline-flex items-center justify-center w-20 h-20 bg-primary/10 rounded-full mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary">
                                <rect width="20" height="14" x="2" y="5" rx="2"></rect>
                                <line x1="2" x2="22" y1="10" y2="10"></line>
                            </svg>
                        </div>
                    </div>
                    <h1 class="text-4xl font-bold tracking-tight text-foreground mb-4">
                        Australian Pay Calculator
                    </h1>
                    <p class="text-xl text-muted-foreground max-w-2xl mx-auto">
                        Calculate your take-home salary, tax, superannuation and Medicare levy with our free Australian pay calculator. Accurate calculations for all Australian states and territories.
                    </p>

                    <!-- Key Features -->
                    <div class="flex flex-wrap justify-center gap-4 mt-6">
                        <div class="inline-flex items-center gap-2 bg-muted px-3 py-1 rounded-full text-sm">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-green-600">
                                <path d="M9 12l2 2 4-4"></path>
                                <circle cx="12" cy="12" r="9"></circle>
                            </svg>
                            <span>2024-25 Tax Year</span>
                        </div>
                        <div class="inline-flex items-center gap-2 bg-muted px-3 py-1 rounded-full text-sm">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-blue-600">
                                <path d="M9 12l2 2 4-4"></path>
                                <circle cx="12" cy="12" r="9"></circle>
                            </svg>
                            <span>All Australian States</span>
                        </div>
                        <div class="inline-flex items-center gap-2 bg-muted px-3 py-1 rounded-full text-sm">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-orange-600">
                                <path d="M9 12l2 2 4-4"></path>
                                <circle cx="12" cy="12" r="9"></circle>
                            </svg>
                            <span>Free & Accurate</span>
                        </div>
                    </div>
                </div>

                <!-- Calculator Section -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
                    <!-- Input Form - Mobile -->
                    <div class="lg:hidden">
                        <div class="calculator-container p-6">
                            <div class="mb-6">
                                <h2 class="text-2xl font-semibold mb-2">Income Details</h2>
                                <p class="text-muted-foreground">Enter your income details</p>
                            </div>

                            <form class="grid gap-6" id="mobile-calculator-form">
                                <!-- Salary Input -->
                                <div class="input-group">
                                    <label for="salary-mobile">Salary</label>
                                    <div class="flex border h-10 w-full rounded-md border-input bg-background">
                                        <span class="flex items-center px-3 h-10">$</span>
                                        <input
                                            type="number"
                                            id="salary-mobile"
                                            class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-l-none m-[-1]"
                                            min="0"
                                            placeholder="Enter your salary"
                                            value="50000"
                                            oninput="calculatePay()"
                                        />
                                    </div>
                                </div>

                                <!-- Pay Cycle -->
                                <div class="input-group">
                                    <label for="paycycle-mobile">Time Period</label>
                                    <select
                                        id="paycycle-mobile"
                                        class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                        onchange="calculatePay()"
                                    >
                                        <option value="Weekly">Weekly</option>
                                        <option value="Fortnightly">Fortnightly</option>
                                        <option value="Monthly">Monthly</option>
                                        <option value="Annually" selected>Annually</option>
                                    </select>
                                </div>

                                <!-- Super Rate -->
                                <div class="input-group">
                                    <label for="superRate-mobile">Super Rate %</label>
                                    <input
                                        type="number"
                                        id="superRate-mobile"
                                        class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                        placeholder="Enter super rate (e.g., 9.5 for 9.5%)"
                                        value="11.5"
                                        step="0.1"
                                        min="0"
                                        max="100"
                                        oninput="calculatePay()"
                                    />
                                </div>

                                <!-- Tax Year -->
                                <div class="input-group">
                                    <label for="fiscalYear-mobile">Tax Year</label>
                                    <select
                                        id="fiscalYear-mobile"
                                        class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                        onchange="calculatePay()"
                                    >
                                        <option value="2024-25" selected>2024-25</option>
                                        <option value="2023-24">2023-24</option>
                                        <option value="2022-23">2022-23</option>
                                    </select>
                                </div>
                            </form>
                        </div>

                        <!-- Options - Mobile -->
                        <div class="calculator-container p-6 mt-6">
                            <div class="mb-6">
                                <h3 class="text-lg font-semibold">Options</h3>
                            </div>

                            <div class="grid gap-6">
                                <!-- Includes Superannuation -->
                                <div class="flex items-center justify-between space-x-2">
                                    <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex flex-col space-y-1" for="includesSuper-mobile">
                                        <span>Includes Superannuation</span>
                                    </label>
                                    <button
                                        type="button"
                                        role="switch"
                                        aria-checked="false"
                                        data-state="unchecked"
                                        value="on"
                                        id="includesSuper-mobile"
                                        class="peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input"
                                        onclick="toggleSwitch(this); calculatePay();"
                                    >
                                        <span class="pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0"></span>
                                    </button>
                                </div>

                                <!-- No Private Hospital Cover -->
                                <div class="flex items-center justify-between space-x-2">
                                    <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex flex-col space-y-1" for="noPrivateHospitalCover-mobile">
                                        <span>No Private Hospital Cover</span>
                                    </label>
                                    <button
                                        type="button"
                                        role="switch"
                                        aria-checked="false"
                                        data-state="unchecked"
                                        value="on"
                                        id="noPrivateHospitalCover-mobile"
                                        class="peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input"
                                        onclick="toggleSwitch(this); calculatePay();"
                                    >
                                        <span class="pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0"></span>
                                    </button>
                                </div>

                                <!-- Non-Resident -->
                                <div class="flex items-center justify-between space-x-2">
                                    <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex flex-col space-y-1" for="nonResident-mobile">
                                        <span>Non-Resident</span>
                                    </label>
                                    <button
                                        type="button"
                                        role="switch"
                                        aria-checked="false"
                                        data-state="unchecked"
                                        value="on"
                                        id="nonResident-mobile"
                                        class="peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input"
                                        onclick="toggleSwitch(this); calculatePay();"
                                    >
                                        <span class="pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0"></span>
                                    </button>
                                </div>

                                <!-- Working Holiday Visa -->
                                <div class="flex items-center justify-between space-x-2">
                                    <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex flex-col space-y-1" for="workingHoliday-mobile">
                                        <span>Working Holiday Visa</span>
                                    </label>
                                    <button
                                        type="button"
                                        role="switch"
                                        aria-checked="false"
                                        data-state="unchecked"
                                        value="on"
                                        id="workingHoliday-mobile"
                                        class="peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input"
                                        onclick="toggleSwitch(this); calculatePay();"
                                    >
                                        <span class="pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0"></span>
                                    </button>
                                </div>

                                <!-- No Tax-Free Threshold -->
                                <div class="flex items-center justify-between space-x-2">
                                    <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex flex-col space-y-1" for="noTaxFree-mobile">
                                        <span>No tax-free threshold</span>
                                    </label>
                                    <button
                                        type="button"
                                        role="switch"
                                        aria-checked="false"
                                        data-state="unchecked"
                                        value="on"
                                        id="noTaxFree-mobile"
                                        class="peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input"
                                        onclick="toggleSwitch(this); calculatePay();"
                                    >
                                        <span class="pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0"></span>
                                    </button>
                                </div>

                                <!-- Student Loan -->
                                <div class="flex items-center justify-between space-x-2">
                                    <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex flex-col space-y-1" for="studentLoan-mobile">
                                        <span>Student Loan (HELP, VET, SSL, TSL, SFSS)</span>
                                    </label>
                                    <button
                                        type="button"
                                        role="switch"
                                        aria-checked="false"
                                        data-state="unchecked"
                                        value="on"
                                        id="studentLoan-mobile"
                                        class="peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input"
                                        onclick="toggleSwitch(this); calculatePay();"
                                    >
                                        <span class="pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0"></span>
                                    </button>
                                </div>

                                <!-- Withhold Tax Offsets -->
                                <div class="flex items-center justify-between space-x-2">
                                    <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex flex-col space-y-1" for="withholdTax-mobile">
                                        <span>Withhold Tax Offsets</span>
                                    </label>
                                    <button
                                        type="button"
                                        role="switch"
                                        aria-checked="false"
                                        data-state="unchecked"
                                        value="on"
                                        id="withholdTax-mobile"
                                        class="peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input"
                                        onclick="toggleSwitch(this); calculatePay();"
                                    >
                                        <span class="pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0"></span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Desktop Input Form -->
                    <div class="hidden lg:block">
                        <div class="calculator-container p-6">
                            <div class="mb-6">
                                <h2 class="text-2xl font-semibold mb-2">Income Details</h2>
                                <p class="text-muted-foreground">Enter your income details</p>
                            </div>

                            <form class="grid gap-6" id="desktop-calculator-form">
                                <!-- Salary Input -->
                                <div class="input-group">
                                    <label for="salary-desktop">Salary</label>
                                    <div class="flex border h-10 w-full rounded-md border-input bg-background">
                                        <span class="flex items-center px-3 h-10">$</span>
                                        <input
                                            type="number"
                                            id="salary-desktop"
                                            class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-l-none m-[-1]"
                                            min="0"
                                            placeholder="Enter your salary"
                                            value="50000"
                                            oninput="syncInputs('salary'); calculatePay();"
                                        />
                                    </div>
                                </div>

                                <!-- Pay Cycle -->
                                <div class="input-group">
                                    <label for="paycycle-desktop">Time Period</label>
                                    <select
                                        id="paycycle-desktop"
                                        class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                        onchange="syncInputs('paycycle'); calculatePay();"
                                    >
                                        <option value="Weekly">Weekly</option>
                                        <option value="Fortnightly">Fortnightly</option>
                                        <option value="Monthly">Monthly</option>
                                        <option value="Annually" selected>Annually</option>
                                    </select>
                                </div>

                                <!-- Super Rate -->
                                <div class="input-group">
                                    <label for="superRate-desktop">Super Rate %</label>
                                    <input
                                        type="number"
                                        id="superRate-desktop"
                                        class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                        placeholder="Enter super rate (e.g., 9.5 for 9.5%)"
                                        value="11.5"
                                        step="0.1"
                                        min="0"
                                        max="100"
                                        oninput="syncInputs('superRate'); calculatePay();"
                                    />
                                </div>

                                <!-- Tax Year -->
                                <div class="input-group">
                                    <label for="fiscalYear-desktop">Tax Year</label>
                                    <select
                                        id="fiscalYear-desktop"
                                        class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                        onchange="syncInputs('fiscalYear'); calculatePay();"
                                    >
                                        <option value="2024-25" selected>2024-25</option>
                                        <option value="2023-24">2023-24</option>
                                        <option value="2022-23">2022-23</option>
                                    </select>
                                </div>
                            </form>
                        </div>

                        <!-- Options - Desktop -->
                        <div class="calculator-container p-6 mt-6">
                            <div class="mb-6">
                                <h3 class="text-2xl font-semibold">Options</h3>
                            </div>

                            <div class="grid gap-6">
                                <!-- Includes Superannuation -->
                                <div class="flex items-center justify-between space-x-2">
                                    <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex flex-col space-y-1" for="includesSuper-desktop">
                                        <span>Includes Superannuation</span>
                                    </label>
                                    <button
                                        type="button"
                                        role="switch"
                                        aria-checked="false"
                                        data-state="unchecked"
                                        value="on"
                                        id="includesSuper-desktop"
                                        class="peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input"
                                        onclick="syncSwitches('includesSuper'); calculatePay();"
                                    >
                                        <span class="pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0"></span>
                                    </button>
                                </div>

                                <!-- No Private Hospital Cover -->
                                <div class="flex items-center justify-between space-x-2">
                                    <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex flex-col space-y-1" for="noPrivateHospitalCover-desktop">
                                        <span>No Private Hospital Cover</span>
                                    </label>
                                    <button
                                        type="button"
                                        role="switch"
                                        aria-checked="false"
                                        data-state="unchecked"
                                        value="on"
                                        id="noPrivateHospitalCover-desktop"
                                        class="peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input"
                                        onclick="syncSwitches('noPrivateHospitalCover'); calculatePay();"
                                    >
                                        <span class="pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0"></span>
                                    </button>
                                </div>

                                <!-- Non-Resident -->
                                <div class="flex items-center justify-between space-x-2">
                                    <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex flex-col space-y-1" for="nonResident-desktop">
                                        <span>Non-Resident</span>
                                    </label>
                                    <button
                                        type="button"
                                        role="switch"
                                        aria-checked="false"
                                        data-state="unchecked"
                                        value="on"
                                        id="nonResident-desktop"
                                        class="peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input"
                                        onclick="syncSwitches('nonResident'); calculatePay();"
                                    >
                                        <span class="pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0"></span>
                                    </button>
                                </div>

                                <!-- Working Holiday Visa -->
                                <div class="flex items-center justify-between space-x-2">
                                    <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex flex-col space-y-1" for="workingHoliday-desktop">
                                        <span>Working Holiday Visa</span>
                                    </label>
                                    <button
                                        type="button"
                                        role="switch"
                                        aria-checked="false"
                                        data-state="unchecked"
                                        value="on"
                                        id="workingHoliday-desktop"
                                        class="peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input"
                                        onclick="syncSwitches('workingHoliday'); calculatePay();"
                                    >
                                        <span class="pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0"></span>
                                    </button>
                                </div>

                                <!-- No Tax-Free Threshold -->
                                <div class="flex items-center justify-between space-x-2">
                                    <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex flex-col space-y-1" for="noTaxFree-desktop">
                                        <span>No tax-free threshold</span>
                                    </label>
                                    <button
                                        type="button"
                                        role="switch"
                                        aria-checked="false"
                                        data-state="unchecked"
                                        value="on"
                                        id="noTaxFree-desktop"
                                        class="peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input"
                                        onclick="syncSwitches('noTaxFree'); calculatePay();"
                                    >
                                        <span class="pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0"></span>
                                    </button>
                                </div>

                                <!-- Student Loan -->
                                <div class="flex items-center justify-between space-x-2">
                                    <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex flex-col space-y-1" for="studentLoan-desktop">
                                        <span>Student Loan (HELP, VET, SSL, TSL, SFSS)</span>
                                    </label>
                                    <button
                                        type="button"
                                        role="switch"
                                        aria-checked="false"
                                        data-state="unchecked"
                                        value="on"
                                        id="studentLoan-desktop"
                                        class="peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input"
                                        onclick="syncSwitches('studentLoan'); calculatePay();"
                                    >
                                        <span class="pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0"></span>
                                    </button>
                                </div>

                                <!-- Withhold Tax Offsets -->
                                <div class="flex items-center justify-between space-x-2">
                                    <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex flex-col space-y-1" for="withholdTax-desktop">
                                        <span>Withhold Tax Offsets</span>
                                    </label>
                                    <button
                                        type="button"
                                        role="switch"
                                        aria-checked="false"
                                        data-state="unchecked"
                                        value="on"
                                        id="withholdTax-desktop"
                                        class="peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input"
                                        onclick="syncSwitches('withholdTax'); calculatePay();"
                                    >
                                        <span class="pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0"></span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Results Section -->
                    <div class="lg:col-span-1">
                        <div class="calculator-container p-6">
                            <div class="mb-6">
                                <div class="flex items-center gap-3 mb-2">
                                    <div class="inline-flex items-center justify-center w-8 h-8 bg-green-100 rounded-full">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-green-600">
                                            <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                                        </svg>
                                    </div>
                                    <h2 class="text-2xl font-semibold">Your Results</h2>
                                </div>
                                <p class="text-muted-foreground">Calculated pay breakdown</p>
                            </div>

                            <div class="results-container" id="results-container">
                                <div class="result-item">
                                    <div class="flex items-center gap-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-blue-600">
                                            <circle cx="12" cy="12" r="10"></circle>
                                            <path d="M12 6v6l4 2"></path>
                                        </svg>
                                        <span class="result-label">Gross Income</span>
                                    </div>
                                    <span class="result-value" id="gross-income">$50,000</span>
                                </div>
                                <div class="result-item">
                                    <div class="flex items-center gap-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-red-600">
                                            <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                                            <circle cx="9" cy="7" r="4"></circle>
                                            <path d="m22 2-5 10-5-4Z"></path>
                                        </svg>
                                        <span class="result-label">Income Tax</span>
                                    </div>
                                    <span class="result-value negative" id="income-tax">-$5,092</span>
                                </div>
                                <div class="result-item">
                                    <div class="flex items-center gap-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-orange-600">
                                            <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.29 1.51 4.04 3 5.5l7 7Z"></path>
                                        </svg>
                                        <span class="result-label">Medicare Levy</span>
                                    </div>
                                    <span class="result-value negative" id="medicare-levy">-$1,000</span>
                                </div>
                                <div class="result-item">
                                    <div class="flex items-center gap-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-blue-600">
                                            <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                                        </svg>
                                        <span class="result-label">Superannuation</span>
                                    </div>
                                    <span class="result-value positive" id="superannuation">+$5,750</span>
                                </div>
                                <div class="result-item border-t-2 border-primary pt-4">
                                    <div class="flex items-center gap-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-green-600">
                                            <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                                            <circle cx="9" cy="7" r="4"></circle>
                                            <path d="m22 2-5 10-5-4Z"></path>
                                        </svg>
                                        <span class="result-label font-semibold">Net Income</span>
                                    </div>
                                    <span class="result-value positive text-xl font-bold" id="net-income">$43,908</span>
                                </div>
                            </div>

                            <!-- Chart Container -->
                            <div class="mt-6">
                                <h3 class="text-lg font-semibold mb-4 text-center">Income Breakdown</h3>
                                <div class="relative">
                                    <canvas id="payChart" width="400" height="200"></canvas>
                                </div>
                            </div>

                            <!-- Quick Stats -->
                            <div class="mt-6 grid grid-cols-2 gap-4">
                                <div class="text-center p-3 bg-muted rounded-lg">
                                    <div class="text-2xl font-bold text-green-600" id="effective-rate">12.2%</div>
                                    <div class="text-sm text-muted-foreground">Effective Tax Rate</div>
                                </div>
                                <div class="text-center p-3 bg-muted rounded-lg">
                                    <div class="text-2xl font-bold text-blue-600" id="take-home-rate">87.8%</div>
                                    <div class="text-sm text-muted-foreground">Take Home Rate</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Information Section -->
                <div class="mt-12 max-w-6xl mx-auto">
                    <!-- Tax Brackets Chart -->
                    <div class="calculator-container p-6 mb-8">
                        <h3 class="text-2xl font-semibold mb-6 text-center">Australian Tax Brackets 2024-25</h3>
                        <div class="overflow-x-auto">
                            <table class="w-full border-collapse">
                                <thead>
                                    <tr class="border-b-2 border-primary">
                                        <th class="text-left p-3 font-semibold">Taxable Income</th>
                                        <th class="text-left p-3 font-semibold">Tax Rate</th>
                                        <th class="text-left p-3 font-semibold">Tax on Income</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr class="border-b border-muted">
                                        <td class="p-3">$0 – $18,200</td>
                                        <td class="p-3">0%</td>
                                        <td class="p-3 text-green-600 font-semibold">Nil</td>
                                    </tr>
                                    <tr class="border-b border-muted bg-muted/30">
                                        <td class="p-3">$18,201 – $45,000</td>
                                        <td class="p-3">19%</td>
                                        <td class="p-3">19¢ for each $1 over $18,200</td>
                                    </tr>
                                    <tr class="border-b border-muted">
                                        <td class="p-3">$45,001 – $120,000</td>
                                        <td class="p-3">32.5%</td>
                                        <td class="p-3">$5,092 plus 32.5¢ for each $1 over $45,000</td>
                                    </tr>
                                    <tr class="border-b border-muted bg-muted/30">
                                        <td class="p-3">$120,001 – $180,000</td>
                                        <td class="p-3">37%</td>
                                        <td class="p-3">$29,467 plus 37¢ for each $1 over $120,000</td>
                                    </tr>
                                    <tr class="border-b border-muted">
                                        <td class="p-3">$180,001+</td>
                                        <td class="p-3">45%</td>
                                        <td class="p-3">$51,667 plus 45¢ for each $1 over $180,000</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <!-- About Australian Pay Calculator -->
                        <div class="calculator-container p-6">
                            <div class="flex items-center gap-3 mb-4">
                                <div class="inline-flex items-center justify-center w-10 h-10 bg-blue-100 rounded-full">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-blue-600">
                                        <path d="M9 11H1v3h8v3l3-3.5L9 11z"></path>
                                        <path d="M22 12h-6"></path>
                                        <path d="M16 16h6"></path>
                                        <path d="M16 8h6"></path>
                                    </svg>
                                </div>
                                <h3 class="text-xl font-semibold">About This Calculator</h3>
                            </div>
                            <p class="text-muted-foreground mb-4">
                                Our Australian pay calculator helps you understand your take-home salary after tax, superannuation, and Medicare levy deductions.
                                It's designed specifically for Australian employees and contractors.
                            </p>
                            <ul class="text-sm text-muted-foreground space-y-2">
                                <li class="flex items-center gap-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-green-600">
                                        <path d="M9 12l2 2 4-4"></path>
                                        <circle cx="12" cy="12" r="9"></circle>
                                    </svg>
                                    Accurate Australian tax calculations
                                </li>
                                <li class="flex items-center gap-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-green-600">
                                        <path d="M9 12l2 2 4-4"></path>
                                        <circle cx="12" cy="12" r="9"></circle>
                                    </svg>
                                    Includes Medicare levy and surcharge
                                </li>
                                <li class="flex items-center gap-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-green-600">
                                        <path d="M9 12l2 2 4-4"></path>
                                        <circle cx="12" cy="12" r="9"></circle>
                                    </svg>
                                    Superannuation calculations
                                </li>
                                <li class="flex items-center gap-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-green-600">
                                        <path d="M9 12l2 2 4-4"></path>
                                        <circle cx="12" cy="12" r="9"></circle>
                                    </svg>
                                    Support for all Australian states
                                </li>
                                <li class="flex items-center gap-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-green-600">
                                        <path d="M9 12l2 2 4-4"></path>
                                        <circle cx="12" cy="12" r="9"></circle>
                                    </svg>
                                    Updated for 2024-25 tax year
                                </li>
                            </ul>
                        </div>

                        <!-- Tax Information -->
                        <div class="calculator-container p-6">
                            <div class="flex items-center gap-3 mb-4">
                                <div class="inline-flex items-center justify-center w-10 h-10 bg-orange-100 rounded-full">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-orange-600">
                                        <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                                        <circle cx="9" cy="7" r="4"></circle>
                                        <path d="m22 2-5 10-5-4Z"></path>
                                    </svg>
                                </div>
                                <h3 class="text-xl font-semibold">Australian Tax Information</h3>
                            </div>
                            <p class="text-muted-foreground mb-4">
                                Understanding Australian tax brackets and rates is essential for financial planning.
                                Our calculator uses the latest ATO tax tables and rates.
                            </p>
                            <div class="space-y-3">
                                <div class="flex justify-between items-center p-3 bg-muted rounded-lg">
                                    <span class="font-medium">Tax-free threshold:</span>
                                    <span class="font-bold text-green-600">$18,200</span>
                                </div>
                                <div class="flex justify-between items-center p-3 bg-muted rounded-lg">
                                    <span class="font-medium">Medicare levy:</span>
                                    <span class="font-bold text-blue-600">2.0%</span>
                                </div>
                                <div class="flex justify-between items-center p-3 bg-muted rounded-lg">
                                    <span class="font-medium">Super guarantee:</span>
                                    <span class="font-bold text-orange-600">11.5%</span>
                                </div>
                                <div class="flex justify-between items-center p-3 bg-muted rounded-lg">
                                    <span class="font-medium">HELP threshold:</span>
                                    <span class="font-bold text-purple-600">$51,550</span>
                                </div>
                            </div>
                        </div>

                        <!-- Australian Features -->
                        <div class="calculator-container p-6">
                            <div class="flex items-center gap-3 mb-4">
                                <div class="inline-flex items-center justify-center w-10 h-10 bg-green-100 rounded-full">
                                    <span class="text-green-600 font-bold text-lg">🇦🇺</span>
                                </div>
                                <h3 class="text-xl font-semibold">Made for Aussies</h3>
                            </div>
                            <p class="text-muted-foreground mb-4">
                                This calculator is specifically designed for Australian tax residents and includes all the unique features of the Australian tax system.
                            </p>
                            <ul class="text-sm text-muted-foreground space-y-2">
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-green-600 rounded-full"></span>
                                    Working Holiday Visa calculations
                                </li>
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-blue-600 rounded-full"></span>
                                    Non-resident tax rates
                                </li>
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-orange-600 rounded-full"></span>
                                    Medicare levy surcharge
                                </li>
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-purple-600 rounded-full"></span>
                                    HELP/HECS debt repayments
                                </li>
                                <li class="flex items-center gap-2">
                                    <span class="w-2 h-2 bg-red-600 rounded-full"></span>
                                    Tax offset withholding
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- PayCal Australia Footer -->
        <footer class="border-t bg-muted/50">
            <div class="container mx-auto px-4 py-12">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                    <!-- Brand -->
                    <div class="space-y-4">
                        <div class="flex items-center space-x-2">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256" class="h-8 w-8" aria-hidden="true">
                                <path d="M0 0h256v256H0z" fill="none"></path>
                                <rect width="256" height="256" fill="none" stroke="currentColor" stroke-width="16"></rect>
                                <rect x="32" y="32" width="192" height="64" fill="currentColor"></rect>
                                <circle cx="64" cy="128" r="16" fill="currentColor"></circle>
                                <circle cx="128" cy="128" r="16" fill="currentColor"></circle>
                                <circle cx="192" cy="128" r="16" fill="currentColor"></circle>
                                <circle cx="64" cy="176" r="16" fill="currentColor"></circle>
                                <circle cx="128" cy="176" r="16" fill="currentColor"></circle>
                                <circle cx="192" cy="176" r="16" fill="currentColor"></circle>
                                <circle cx="64" cy="224" r="16" fill="currentColor"></circle>
                                <circle cx="128" cy="224" r="16" fill="currentColor"></circle>
                                <circle cx="192" cy="224" r="16" fill="currentColor"></circle>
                            </svg>
                            <span class="text-xl font-bold">PayCal.com.au</span>
                        </div>
                        <p class="text-sm text-muted-foreground">
                            Australia's leading calculator platform for pay, tax, and financial calculations.
                        </p>
                        <p class="text-xs text-muted-foreground">
                            Made for Aussies ❤️
                        </p>
                    </div>

                    <!-- Pay Calculators -->
                    <div class="space-y-4">
                        <h4 class="text-sm font-semibold">Pay Calculators</h4>
                        <ul class="space-y-2 text-sm text-muted-foreground">
                            <li><a href="australian-pay-calculator.html" class="hover:text-foreground transition-colors">Australian Pay Calculator</a></li>
                            <li><a href="contractor-pay-calculator.html" class="hover:text-foreground transition-colors">Contractor Pay Calculator</a></li>
                            <li><a href="#" class="hover:text-foreground transition-colors">Contract Rate Benchmarking</a></li>
                            <li><a href="#" class="hover:text-foreground transition-colors">Salary Rate Benchmarking</a></li>
                        </ul>
                    </div>

                    <!-- Property Calculators -->
                    <div class="space-y-4">
                        <h4 class="text-sm font-semibold">Property Calculators</h4>
                        <ul class="space-y-2 text-sm text-muted-foreground">
                            <li><a href="mortgage-calculator.html" class="hover:text-foreground transition-colors">Mortgage Calculator</a></li>
                            <li><a href="home-loan-comparison.html" class="hover:text-foreground transition-colors">Home Loan Comparison</a></li>
                            <li><a href="#" class="hover:text-foreground transition-colors">Rate Cut Calculator</a></li>
                            <li><a href="#" class="hover:text-foreground transition-colors">Mortgage Payoff Calculator</a></li>
                        </ul>
                    </div>

                    <!-- Tax Tools -->
                    <div class="space-y-4">
                        <h4 class="text-sm font-semibold">Tax Tools</h4>
                        <ul class="space-y-2 text-sm text-muted-foreground">
                            <li><a href="tax-cut-calculator.html" class="hover:text-foreground transition-colors">Tax Cut Calculator</a></li>
                            <li><a href="gst-calculator.html" class="hover:text-foreground transition-colors">GST Calculator</a></li>
                            <li><a href="#" class="hover:text-foreground transition-colors">Tax Calendar</a></li>
                        </ul>
                    </div>
                </div>

                <div class="border-t mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
                    <p class="text-xs text-muted-foreground">
                        © 2024 PayCal.com.au. All rights reserved. Made for Aussies ❤️
                    </p>
                    <div class="flex items-center space-x-4 mt-4 md:mt-0">
                        <a href="mailto:<EMAIL>" class="text-xs text-muted-foreground hover:text-foreground transition-colors">
                            Contact: <EMAIL>
                        </a>
                    </div>
                </div>
            </div>
        </footer>

        <!-- Back to Top Button -->
        <button id="back-to-top" class="fixed bottom-4 right-4 bg-primary text-primary-foreground p-3 rounded-full shadow-lg opacity-0 transition-opacity duration-300 hover:bg-primary/90 z-50" onclick="scrollToTop()">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                <path d="m18 15-6-6-6 6"></path>
            </svg>
        </button>
    </div>

    <!-- Chart.js for visualization -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
        // Australian Tax Brackets for 2024-25
        const TAX_BRACKETS_2024_25 = [
            { min: 0, max: 18200, rate: 0 },
            { min: 18201, max: 45000, rate: 0.19 },
            { min: 45001, max: 120000, rate: 0.325 },
            { min: 120001, max: 180000, rate: 0.37 },
            { min: 180001, max: Infinity, rate: 0.45 }
        ];

        const TAX_BRACKETS_2023_24 = [
            { min: 0, max: 18200, rate: 0 },
            { min: 18201, max: 45000, rate: 0.19 },
            { min: 45001, max: 120000, rate: 0.325 },
            { min: 120001, max: 180000, rate: 0.37 },
            { min: 180001, max: Infinity, rate: 0.45 }
        ];

        // Medicare Levy rates
        const MEDICARE_LEVY_RATE = 0.02;
        const MEDICARE_LEVY_SURCHARGE_RATE = 0.01; // 1% for no private health cover
        const MEDICARE_LEVY_THRESHOLD = 24276; // 2024-25

        // Super guarantee rate
        const SUPER_GUARANTEE_RATE = 0.115; // 11.5% for 2024-25

        // HELP/Student loan thresholds and rates
        const HELP_THRESHOLD = 51550; // 2024-25
        const HELP_RATES = [
            { min: 51550, max: 59518, rate: 0.01 },
            { min: 59519, max: 63089, rate: 0.02 },
            { min: 63090, max: 66875, rate: 0.025 },
            { min: 66876, max: 70888, rate: 0.03 },
            { min: 70889, max: 75140, rate: 0.035 },
            { min: 75141, max: 79649, rate: 0.04 },
            { min: 79650, max: 84429, rate: 0.045 },
            { min: 84430, max: 89494, rate: 0.05 },
            { min: 89495, max: 94865, rate: 0.055 },
            { min: 94866, max: 100557, rate: 0.06 },
            { min: 100558, max: 106590, rate: 0.065 },
            { min: 106591, max: 112985, rate: 0.07 },
            { min: 112986, max: 119764, rate: 0.075 },
            { min: 119765, max: 126950, rate: 0.08 },
            { min: 126951, max: 134568, rate: 0.085 },
            { min: 134569, max: 142642, rate: 0.09 },
            { min: 142643, max: 151200, rate: 0.095 },
            { min: 151201, max: Infinity, rate: 0.10 }
        ];

        let payChart = null;

        // Initialize calculator
        document.addEventListener('DOMContentLoaded', function() {
            calculatePay();
            initializeChart();

            // Show back to top button on scroll
            window.addEventListener('scroll', function() {
                const backToTop = document.getElementById('back-to-top');
                if (window.pageYOffset > 300) {
                    backToTop.style.opacity = '1';
                } else {
                    backToTop.style.opacity = '0';
                }
            });
        });

        // Calculate Australian pay with accurate tax calculations
        function calculatePay() {
            // Get input values (use desktop values as primary, mobile as fallback)
            const salary = parseFloat(document.getElementById('salary-desktop')?.value || document.getElementById('salary-mobile')?.value || 50000);
            const paycycle = document.getElementById('paycycle-desktop')?.value || document.getElementById('paycycle-mobile')?.value || 'Annually';
            const superRate = parseFloat(document.getElementById('superRate-desktop')?.value || document.getElementById('superRate-mobile')?.value || 11.5) / 100;
            const fiscalYear = document.getElementById('fiscalYear-desktop')?.value || document.getElementById('fiscalYear-mobile')?.value || '2024-25';

            // Get option switches
            const includesSuper = getSwitchValue('includesSuper');
            const noPrivateHospitalCover = getSwitchValue('noPrivateHospitalCover');
            const nonResident = getSwitchValue('nonResident');
            const workingHoliday = getSwitchValue('workingHoliday');
            const noTaxFree = getSwitchValue('noTaxFree');
            const studentLoan = getSwitchValue('studentLoan');
            const withholdTax = getSwitchValue('withholdTax');

            // Convert to annual salary
            let annualSalary = salary;
            switch(paycycle) {
                case 'Weekly':
                    annualSalary = salary * 52;
                    break;
                case 'Fortnightly':
                    annualSalary = salary * 26;
                    break;
                case 'Monthly':
                    annualSalary = salary * 12;
                    break;
            }

            // Calculate superannuation
            let grossIncome = annualSalary;
            let superannuation = 0;

            if (includesSuper) {
                // Salary includes super - need to separate it
                grossIncome = annualSalary / (1 + superRate);
                superannuation = grossIncome * superRate;
            } else {
                // Salary excludes super - add it
                superannuation = grossIncome * superRate;
            }

            // Calculate income tax
            const taxBrackets = fiscalYear === '2024-25' ? TAX_BRACKETS_2024_25 : TAX_BRACKETS_2023_24;
            let incomeTax = calculateIncomeTax(grossIncome, taxBrackets, nonResident, workingHoliday, noTaxFree);

            // Calculate Medicare levy
            let medicareLevy = 0;
            if (!nonResident && !workingHoliday) {
                if (grossIncome > MEDICARE_LEVY_THRESHOLD) {
                    medicareLevy = grossIncome * MEDICARE_LEVY_RATE;

                    // Add Medicare levy surcharge if no private health cover
                    if (noPrivateHospitalCover && grossIncome > 93000) { // Single threshold
                        medicareLevy += grossIncome * MEDICARE_LEVY_SURCHARGE_RATE;
                    }
                }
            }

            // Calculate HELP/Student loan repayment
            let helpRepayment = 0;
            if (studentLoan && grossIncome > HELP_THRESHOLD) {
                helpRepayment = calculateHelpRepayment(grossIncome);
            }

            // Calculate net income
            const totalDeductions = incomeTax + medicareLevy + helpRepayment;
            const netIncome = grossIncome - totalDeductions;

            // Convert results back to selected pay cycle
            const results = convertToPayCycle({
                grossIncome,
                incomeTax,
                medicareLevy,
                helpRepayment,
                superannuation,
                netIncome
            }, paycycle);

            // Update display
            updateResults(results);
            updateChart(results);
        }

        // Calculate income tax based on brackets
        function calculateIncomeTax(income, brackets, nonResident, workingHoliday, noTaxFree) {
            let tax = 0;
            let taxableIncome = income;

            // Apply tax-free threshold (unless non-resident, working holiday, or opted out)
            if (nonResident || workingHoliday || noTaxFree) {
                // No tax-free threshold
            } else {
                // Standard tax-free threshold applies
            }

            // Working holiday visa special rates
            if (workingHoliday) {
                if (income <= 45000) {
                    return income * 0.15; // 15% flat rate up to $45,000
                } else {
                    tax = 45000 * 0.15;
                    taxableIncome = income - 45000;
                    // Apply normal brackets to amount over $45,000
                    for (let i = 2; i < brackets.length; i++) { // Start from 32.5% bracket
                        const bracket = brackets[i];
                        if (taxableIncome > bracket.min - 45000) {
                            const taxableInBracket = Math.min(taxableIncome - (bracket.min - 45000), bracket.max - bracket.min);
                            tax += taxableInBracket * bracket.rate;
                        }
                    }
                    return tax;
                }
            }

            // Non-resident rates
            if (nonResident) {
                // Non-residents pay tax from first dollar
                for (const bracket of brackets) {
                    if (income > bracket.min) {
                        const taxableInBracket = Math.min(income - bracket.min, bracket.max - bracket.min);
                        tax += taxableInBracket * bracket.rate;
                    }
                }
                return tax;
            }

            // Standard resident rates
            for (const bracket of brackets) {
                if (income > bracket.min) {
                    const taxableInBracket = Math.min(income - bracket.min, bracket.max - bracket.min);
                    tax += taxableInBracket * bracket.rate;
                }
            }

            return tax;
        }

        // Calculate HELP repayment
        function calculateHelpRepayment(income) {
            for (const rate of HELP_RATES) {
                if (income >= rate.min && income <= rate.max) {
                    return income * rate.rate;
                }
            }
            return 0;
        }

        // Convert annual amounts to selected pay cycle
        function convertToPayCycle(amounts, paycycle) {
            const divisor = {
                'Weekly': 52,
                'Fortnightly': 26,
                'Monthly': 12,
                'Annually': 1
            }[paycycle];

            return {
                grossIncome: amounts.grossIncome / divisor,
                incomeTax: amounts.incomeTax / divisor,
                medicareLevy: amounts.medicareLevy / divisor,
                helpRepayment: amounts.helpRepayment / divisor,
                superannuation: amounts.superannuation / divisor,
                netIncome: amounts.netIncome / divisor
            };
        }

        // Update results display
        function updateResults(results) {
            document.getElementById('gross-income').textContent = formatCurrency(results.grossIncome);
            document.getElementById('income-tax').textContent = '-' + formatCurrency(results.incomeTax);
            document.getElementById('medicare-levy').textContent = '-' + formatCurrency(results.medicareLevy + results.helpRepayment);
            document.getElementById('superannuation').textContent = '+' + formatCurrency(results.superannuation);
            document.getElementById('net-income').textContent = formatCurrency(results.netIncome);

            // Calculate and display rates
            const totalDeductions = results.incomeTax + results.medicareLevy + results.helpRepayment;
            const effectiveRate = (totalDeductions / results.grossIncome) * 100;
            const takeHomeRate = (results.netIncome / results.grossIncome) * 100;

            document.getElementById('effective-rate').textContent = effectiveRate.toFixed(1) + '%';
            document.getElementById('take-home-rate').textContent = takeHomeRate.toFixed(1) + '%';
        }

        // Format currency
        function formatCurrency(amount) {
            return new Intl.NumberFormat('en-AU', {
                style: 'currency',
                currency: 'AUD',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(amount);
        }

        // Get switch value
        function getSwitchValue(switchName) {
            const mobileSwitch = document.getElementById(switchName + '-mobile');
            const desktopSwitch = document.getElementById(switchName + '-desktop');
            const switchElement = desktopSwitch || mobileSwitch;
            return switchElement ? switchElement.getAttribute('aria-checked') === 'true' : false;
        }

        // Toggle switch
        function toggleSwitch(element) {
            const isChecked = element.getAttribute('aria-checked') === 'true';
            const newState = !isChecked;

            element.setAttribute('aria-checked', newState);
            element.setAttribute('data-state', newState ? 'checked' : 'unchecked');

            // Update visual state
            const thumb = element.querySelector('span');
            if (newState) {
                element.classList.add('bg-primary');
                element.classList.remove('bg-input');
                thumb.style.transform = 'translateX(20px)';
            } else {
                element.classList.remove('bg-primary');
                element.classList.add('bg-input');
                thumb.style.transform = 'translateX(0px)';
            }
        }

        // Sync inputs between mobile and desktop
        function syncInputs(inputName) {
            const mobileInput = document.getElementById(inputName + '-mobile');
            const desktopInput = document.getElementById(inputName + '-desktop');

            if (mobileInput && desktopInput) {
                if (event.target === mobileInput) {
                    desktopInput.value = mobileInput.value;
                } else {
                    mobileInput.value = desktopInput.value;
                }
            }
        }

        // Sync switches between mobile and desktop
        function syncSwitches(switchName) {
            const mobileSwitch = document.getElementById(switchName + '-mobile');
            const desktopSwitch = document.getElementById(switchName + '-desktop');

            if (mobileSwitch && desktopSwitch) {
                const sourceSwitch = event.target.closest('button');
                const targetSwitch = sourceSwitch === mobileSwitch ? desktopSwitch : mobileSwitch;

                const isChecked = sourceSwitch.getAttribute('aria-checked') === 'true';
                targetSwitch.setAttribute('aria-checked', isChecked);
                targetSwitch.setAttribute('data-state', isChecked ? 'checked' : 'unchecked');

                // Update visual state
                const thumb = targetSwitch.querySelector('span');
                if (isChecked) {
                    targetSwitch.classList.add('bg-primary');
                    targetSwitch.classList.remove('bg-input');
                    thumb.style.transform = 'translateX(20px)';
                } else {
                    targetSwitch.classList.remove('bg-primary');
                    targetSwitch.classList.add('bg-input');
                    thumb.style.transform = 'translateX(0px)';
                }
            }

            // Toggle the current switch
            toggleSwitch(event.target.closest('button'));
        }

        // Initialize chart
        function initializeChart() {
            const ctx = document.getElementById('payChart').getContext('2d');
            payChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Net Income', 'Income Tax', 'Medicare Levy', 'Superannuation'],
                    datasets: [{
                        data: [43908, 5092, 1000, 5750],
                        backgroundColor: [
                            '#16a34a',
                            '#dc2626',
                            '#f59e0b',
                            '#3b82f6'
                        ],
                        borderWidth: 2,
                        borderColor: '#ffffff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        }
                    }
                }
            });
        }

        // Update chart
        function updateChart(results) {
            if (payChart) {
                payChart.data.datasets[0].data = [
                    results.netIncome,
                    results.incomeTax,
                    results.medicareLevy + results.helpRepayment,
                    results.superannuation
                ];
                payChart.update();
            }
        }

        // Theme toggle
        function toggleTheme() {
            const html = document.documentElement;
            const isDark = html.classList.contains('dark');

            if (isDark) {
                html.classList.remove('dark');
                html.classList.add('light');
                html.style.colorScheme = 'light';
            } else {
                html.classList.remove('light');
                html.classList.add('dark');
                html.style.colorScheme = 'dark';
            }
        }

        // Mobile menu toggle
        function toggleMobileMenu() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        }

        // Dismiss banner
        function dismissBanner() {
            const banner = document.querySelector('.bg-orange-100\\/90');
            if (banner) {
                banner.style.display = 'none';
            }
        }

        // Scroll to top
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // Smooth scroll for anchor links
        document.addEventListener('click', function(e) {
            if (e.target.matches('a[href^="#"]')) {
                e.preventDefault();
                const target = document.querySelector(e.target.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            }
        });
    </script>
</body>
</html>
