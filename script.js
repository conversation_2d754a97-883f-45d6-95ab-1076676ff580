// Australian Pay Calculator JavaScript

// Tax brackets for 2024-25
const TAX_BRACKETS_2024_25 = [
    { min: 0, max: 18200, rate: 0 },
    { min: 18201, max: 45000, rate: 0.19 },
    { min: 45001, max: 120000, rate: 0.325 },
    { min: 120001, max: 180000, rate: 0.37 },
    { min: 180001, max: Infinity, rate: 0.45 }
];

// Medicare levy rate
const MEDICARE_LEVY_RATE = 0.02;
const MEDICARE_LEVY_THRESHOLD = 29207;

// Low income tax offset
const LITO_MAX = 700;
const LITO_THRESHOLD = 37500;

// Theme Toggle Functionality
function toggleTheme() {
    const html = document.documentElement;
    const currentTheme = html.classList.contains('dark') ? 'dark' : 'light';

    if (currentTheme === 'light') {
        html.classList.remove('light');
        html.classList.add('dark');
        html.style.colorScheme = 'dark';
        localStorage.setItem('theme', 'dark');
    } else {
        html.classList.remove('dark');
        html.classList.add('light');
        html.style.colorScheme = 'light';
        localStorage.setItem('theme', 'light');
    }
}

// Initialize theme from localStorage
function initializeTheme() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    const html = document.documentElement;

    html.classList.remove('light', 'dark');
    html.classList.add(savedTheme);
    html.style.colorScheme = savedTheme;
}

// Calculate income tax
function calculateIncomeTax(annualIncome, isResident = true, hasStudentLoan = false) {
    if (!isResident) {
        // Non-resident tax rates (simplified)
        if (annualIncome <= 120000) return annualIncome * 0.325;
        if (annualIncome <= 180000) return 120000 * 0.325 + (annualIncome - 120000) * 0.37;
        return 120000 * 0.325 + 60000 * 0.37 + (annualIncome - 180000) * 0.45;
    }

    let tax = 0;
    for (const bracket of TAX_BRACKETS_2024_25) {
        if (annualIncome > bracket.min) {
            const taxableInBracket = Math.min(annualIncome, bracket.max) - bracket.min + 1;
            tax += taxableInBracket * bracket.rate;
        }
    }

    // Apply Low Income Tax Offset (LITO)
    if (annualIncome <= LITO_THRESHOLD) {
        tax = Math.max(0, tax - LITO_MAX);
    } else if (annualIncome <= 66667) {
        const reduction = LITO_MAX - (annualIncome - LITO_THRESHOLD) * 0.05;
        tax = Math.max(0, tax - Math.max(0, reduction));
    }

    return Math.max(0, tax);
}

// Calculate Medicare levy
function calculateMedicareLevy(annualIncome, hasPrivateHealth = true) {
    if (hasPrivateHealth || annualIncome <= MEDICARE_LEVY_THRESHOLD) {
        return 0;
    }
    return annualIncome * MEDICARE_LEVY_RATE;
}

// Calculate Medicare levy surcharge
function calculateMedicareLevySurcharge(annualIncome, hasPrivateHealth = true) {
    if (hasPrivateHealth) return 0;

    if (annualIncome <= 97000) return 0;
    if (annualIncome <= 113000) return annualIncome * 0.01;
    if (annualIncome <= 151000) return annualIncome * 0.0125;
    return annualIncome * 0.015;
}

// Calculate HELP debt repayment
function calculateHelpRepayment(annualIncome) {
    if (annualIncome <= 51550) return 0;
    if (annualIncome <= 59518) return annualIncome * 0.01;
    if (annualIncome <= 63089) return annualIncome * 0.02;
    if (annualIncome <= 66875) return annualIncome * 0.025;
    if (annualIncome <= 70888) return annualIncome * 0.03;
    if (annualIncome <= 75140) return annualIncome * 0.035;
    if (annualIncome <= 79649) return annualIncome * 0.04;
    if (annualIncome <= 84429) return annualIncome * 0.045;
    if (annualIncome <= 89494) return annualIncome * 0.05;
    if (annualIncome <= 94865) return annualIncome * 0.055;
    if (annualIncome <= 100557) return annualIncome * 0.06;
    if (annualIncome <= 106590) return annualIncome * 0.065;
    if (annualIncome <= 112985) return annualIncome * 0.07;
    if (annualIncome <= 119764) return annualIncome * 0.075;
    if (annualIncome <= 126950) return annualIncome * 0.08;
    if (annualIncome <= 134568) return annualIncome * 0.085;
    if (annualIncome <= 142642) return annualIncome * 0.09;
    if (annualIncome <= 151200) return annualIncome * 0.095;
    return annualIncome * 0.1;
}

// Main calculation function
function calculatePay() {
    // Get input values
    const salaryInput = document.getElementById('salary') || document.getElementById('salary-desktop');
    const timePeriodInput = document.getElementById('paycycle') || document.getElementById('paycycle-desktop');
    const superRateInput = document.getElementById('superRate') || document.getElementById('superRate-desktop');

    // Get option values
    const includesSuper = document.getElementById('includesSuper')?.checked || document.getElementById('includesSuper-desktop')?.checked || false;
    const noPrivateHealth = document.getElementById('noPrivateHospitalCover')?.checked || document.getElementById('noPrivateHospitalCover-desktop')?.checked || false;
    const nonResident = document.getElementById('nonResident')?.checked || document.getElementById('nonResident-desktop')?.checked || false;
    const hasStudentLoan = document.getElementById('studentLoan')?.checked || document.getElementById('studentLoan-desktop')?.checked || false;

    if (!salaryInput || !superRateInput) return;

    let salary = parseFloat(salaryInput.value) || 50000;
    const superRate = parseFloat(superRateInput.value) || 11.5;
    const timePeriod = timePeriodInput?.textContent?.trim() || 'Annually';

    // Convert to annual salary
    let annualSalary = salary;
    switch (timePeriod.toLowerCase()) {
        case 'weekly':
            annualSalary = salary * 52;
            break;
        case 'fortnightly':
            annualSalary = salary * 26;
            break;
        case 'monthly':
            annualSalary = salary * 12;
            break;
    }

    // Calculate superannuation
    let grossIncome = annualSalary;
    let superannuation = 0;

    if (includesSuper) {
        // Salary includes super, so we need to back-calculate
        grossIncome = annualSalary / (1 + superRate / 100);
        superannuation = grossIncome * (superRate / 100);
    } else {
        // Salary doesn't include super
        superannuation = grossIncome * (superRate / 100);
    }

    // Calculate taxes
    const incomeTax = calculateIncomeTax(grossIncome, !nonResident, hasStudentLoan);
    const medicareLevy = calculateMedicareLevy(grossIncome, !noPrivateHealth);
    const medicareLevySurcharge = calculateMedicareLevySurcharge(grossIncome, !noPrivateHealth);
    const helpRepayment = hasStudentLoan ? calculateHelpRepayment(grossIncome) : 0;

    const totalTax = incomeTax + medicareLevy + medicareLevySurcharge + helpRepayment;
    const takeHomePay = grossIncome - totalTax;

    // Update results in table
    updateResults({
        grossIncome,
        superannuation,
        totalTax,
        incomeTax,
        medicareLevy,
        medicareLevySurcharge,
        helpRepayment,
        takeHomePay
    });
}

// Update results in the table
function updateResults(results) {
    const {
        grossIncome,
        superannuation,
        totalTax,
        incomeTax,
        medicareLevy,
        medicareLevySurcharge,
        takeHomePay
    } = results;

    // Calculate periods
    const weekly = {
        gross: grossIncome / 52,
        super: superannuation / 52,
        totalTax: totalTax / 52,
        incomeTax: incomeTax / 52,
        medicareLevy: medicareLevy / 52,
        medicareLevySurcharge: medicareLevySurcharge / 52,
        takeHome: takeHomePay / 52
    };

    const fortnightly = {
        gross: grossIncome / 26,
        super: superannuation / 26,
        totalTax: totalTax / 26,
        incomeTax: incomeTax / 26,
        medicareLevy: medicareLevy / 26,
        medicareLevySurcharge: medicareLevySurcharge / 26,
        takeHome: takeHomePay / 26
    };

    const monthly = {
        gross: grossIncome / 12,
        super: superannuation / 12,
        totalTax: totalTax / 12,
        incomeTax: incomeTax / 12,
        medicareLevy: medicareLevy / 12,
        medicareLevySurcharge: medicareLevySurcharge / 12,
        takeHome: takeHomePay / 12
    };

    // Format currency
    const formatCurrency = (amount) => `$${amount.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}`;

    // Update all table cells
    const updateTableRow = (rowText, weekly, fortnightly, monthly, annually) => {
        const tables = document.querySelectorAll('table');
        tables.forEach(table => {
            const rows = table.querySelectorAll('tr');
            rows.forEach(row => {
                const firstCell = row.querySelector('td:first-child');
                if (firstCell && firstCell.textContent.includes(rowText)) {
                    const cells = row.querySelectorAll('td');
                    if (cells.length >= 5) {
                        cells[1].textContent = formatCurrency(weekly);
                        cells[2].textContent = formatCurrency(fortnightly);
                        cells[3].textContent = formatCurrency(monthly);
                        cells[4].textContent = formatCurrency(annually);
                    }
                }
            });
        });
    };

    // Update each row
    updateTableRow('Gross Income', weekly.gross, fortnightly.gross, monthly.gross, grossIncome);
    updateTableRow('Superannuation', weekly.super, fortnightly.super, monthly.super, superannuation);
    updateTableRow('Total Tax', weekly.totalTax, fortnightly.totalTax, monthly.totalTax, totalTax);
    updateTableRow('Income Tax', weekly.incomeTax, fortnightly.incomeTax, monthly.incomeTax, incomeTax);
    updateTableRow('Medicare Levy', weekly.medicareLevy, fortnightly.medicareLevy, monthly.medicareLevy, medicareLevy);
    updateTableRow('Medicare Levy Surcharge', weekly.medicareLevySurcharge, fortnightly.medicareLevySurcharge, monthly.medicareLevySurcharge, medicareLevySurcharge);
    updateTableRow('Take Home Pay', weekly.takeHome, fortnightly.takeHome, monthly.takeHome, takeHomePay);
}

// Initialize the calculator
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners to inputs
    const inputs = ['salary', 'salary-desktop', 'superRate', 'superRate-desktop'];
    inputs.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('input', calculatePay);
        }
    });

    // Add event listeners to checkboxes
    const checkboxes = [
        'includesSuper', 'includesSuper-desktop',
        'noPrivateHospitalCover', 'noPrivateHospitalCover-desktop',
        'nonResident', 'nonResident-desktop',
        'studentLoan', 'studentLoan-desktop'
    ];
    checkboxes.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', calculatePay);
        }
    });

    // Add theme toggle functionality
    const themeToggle = document.querySelector('button[aria-label="Toggle theme"]');
    if (themeToggle) {
        themeToggle.addEventListener('click', toggleTheme);
    }

    // Initialize theme
    initializeTheme();

    // Initial calculation
    calculatePay();
});

// Switch functionality for mobile/desktop forms
function syncFormValues() {
    // Sync salary values
    const salaryMobile = document.getElementById('salary');
    const salaryDesktop = document.getElementById('salary-desktop');
    if (salaryMobile && salaryDesktop) {
        salaryMobile.addEventListener('input', () => {
            salaryDesktop.value = salaryMobile.value;
            calculatePay();
        });
        salaryDesktop.addEventListener('input', () => {
            salaryMobile.value = salaryDesktop.value;
            calculatePay();
        });
    }

    // Sync super rate values
    const superMobile = document.getElementById('superRate');
    const superDesktop = document.getElementById('superRate-desktop');
    if (superMobile && superDesktop) {
        superMobile.addEventListener('input', () => {
            superDesktop.value = superMobile.value;
            calculatePay();
        });
        superDesktop.addEventListener('input', () => {
            superMobile.value = superDesktop.value;
            calculatePay();
        });
    }

    // Sync checkbox values
    const checkboxPairs = [
        ['includesSuper', 'includesSuper-desktop'],
        ['noPrivateHospitalCover', 'noPrivateHospitalCover-desktop'],
        ['nonResident', 'nonResident-desktop'],
        ['studentLoan', 'studentLoan-desktop']
    ];

    checkboxPairs.forEach(([mobileId, desktopId]) => {
        const mobile = document.getElementById(mobileId);
        const desktop = document.getElementById(desktopId);
        if (mobile && desktop) {
            mobile.addEventListener('change', () => {
                desktop.checked = mobile.checked;
                calculatePay();
            });
            desktop.addEventListener('change', () => {
                mobile.checked = desktop.checked;
                calculatePay();
            });
        }
    });
}

// Navigation dropdown functionality
function initializeNavigation() {
    // Get all dropdown buttons
    const dropdownButtons = document.querySelectorAll('.submenu-trigger');

    dropdownButtons.forEach(button => {
        // Show dropdown on mouse enter
        button.addEventListener('mouseenter', function() {
            // Close all other dropdowns first
            dropdownButtons.forEach(otherButton => {
                if (otherButton !== button) {
                    otherButton.setAttribute('data-state', 'closed');
                    otherButton.setAttribute('aria-expanded', 'false');
                    const otherControlsId = otherButton.getAttribute('aria-controls');
                    const otherDropdown = document.getElementById(otherControlsId);
                    if (otherDropdown) {
                        otherDropdown.classList.add('hidden');
                    }
                }
            });

            // Show current dropdown
            const controlsId = button.getAttribute('aria-controls');
            const dropdown = document.getElementById(controlsId);

            button.setAttribute('data-state', 'open');
            button.setAttribute('aria-expanded', 'true');
            if (dropdown) {
                dropdown.classList.remove('hidden');

                // Position dropdown relative to button
                const buttonRect = button.getBoundingClientRect();
                const navRect = button.closest('nav').getBoundingClientRect();
                dropdown.style.left = (buttonRect.left - navRect.left) + 'px';
            }
        });

        // Keep dropdown open when hovering over the button or dropdown
        const controlsId = button.getAttribute('aria-controls');
        const dropdown = document.getElementById(controlsId);

        if (dropdown) {
            // Create a container that includes both button and dropdown for hover detection
            const container = button.closest('li');

            container.addEventListener('mouseleave', function() {
                // Small delay to allow moving between button and dropdown
                setTimeout(() => {
                    if (!container.matches(':hover')) {
                        button.setAttribute('data-state', 'closed');
                        button.setAttribute('aria-expanded', 'false');
                        dropdown.classList.add('hidden');
                    }
                }, 100);
            });

            // Keep dropdown open when hovering over dropdown content
            dropdown.addEventListener('mouseenter', function() {
                button.setAttribute('data-state', 'open');
                button.setAttribute('aria-expanded', 'true');
                dropdown.classList.remove('hidden');
            });

            dropdown.addEventListener('mouseleave', function() {
                // Small delay to allow moving back to button
                setTimeout(() => {
                    if (!container.matches(':hover')) {
                        button.setAttribute('data-state', 'closed');
                        button.setAttribute('aria-expanded', 'false');
                        dropdown.classList.add('hidden');
                    }
                }, 100);
            });
        }

        // Prevent default click behavior but still allow links to work
        button.addEventListener('click', function(e) {
            e.preventDefault();
        });
    });

    // Close all dropdowns when clicking outside navigation
    document.addEventListener('click', function(e) {
        if (!e.target.closest('nav')) {
            dropdownButtons.forEach(button => {
                button.setAttribute('data-state', 'closed');
                button.setAttribute('aria-expanded', 'false');
                const controlsId = button.getAttribute('aria-controls');
                const dropdown = document.getElementById(controlsId);
                if (dropdown) {
                    dropdown.classList.add('hidden');
                }
            });
        }
    });
}

// Call sync function when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    syncFormValues();
    initializeNavigation();
});
