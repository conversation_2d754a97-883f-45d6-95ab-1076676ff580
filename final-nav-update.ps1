# Final navigation update script for remaining files

$filesToUpdate = @(
    "bmi-calculator.html",
    "header-footer.html", 
    "loan-calculator.html"
)

Write-Host "Updating remaining navigation links..."

foreach ($file in $filesToUpdate) {
    if (Test-Path $file) {
        Write-Host "Processing $file..."
        
        $content = Get-Content -Path $file -Raw
        $originalContent = $content
        
        # Apply all navigation link fixes
        $content = $content -replace 'href="#"([^>]*Contract Rate Benchmarking)', 'href="contract-rate-benchmarking.html"$1'
        $content = $content -replace 'href="#"([^>]*Salary Rate Benchmarking)', 'href="salary-rate-benchmarking.html"$1'
        $content = $content -replace 'href="#"([^>]*Rate Cut Calculator)', 'href="rate-cut-calculator.html"$1'
        $content = $content -replace 'href="#"([^>]*Mortgage Payoff Calculator)', 'href="mortgage-payoff-calculator.html"$1'
        $content = $content -replace 'href="#"([^>]*Tax Calendar)', 'href="tax-calendar.html"$1'
        $content = $content -replace 'href="#"([^>]*Quote Generator)', 'href="quote-generator.html"$1'
        $content = $content -replace 'href="#"([^>]*Payslip Generator)', 'href="payslip-generator.html"$1'
        $content = $content -replace 'href="#"([^>]*Vehicle Registration)', 'href="vehicle-registration.html"$1'
        $content = $content -replace 'href="#"([^>]*Home Loan Comparison)', 'href="home-loan-comparison.html"$1'
        $content = $content -replace 'href="#"([^>]*Tax Cut Calculator)', 'href="tax-cut-calculator.html"$1'
        $content = $content -replace 'href="#"([^>]*GST Calculator)', 'href="gst-calculator.html"$1'
        $content = $content -replace 'href="#"([^>]*EV & ICE Novated Lease)', 'href="novated-lease-calculator.html"$1'
        $content = $content -replace 'href="#"([^>]*Public Holidays)', 'href="public-holidays.html"$1'
        $content = $content -replace 'href="#"([^>]*School Holidays)', 'href="school-holidays.html"$1'
        $content = $content -replace 'href="#"([^>]*Contractor Pay Calculator)', 'href="contractor-pay-calculator.html"$1'
        
        if ($content -ne $originalContent) {
            Set-Content -Path $file -Value $content -NoNewline
            Write-Host "  Updated $file"
        } else {
            Write-Host "  No changes needed for $file"
        }
    } else {
        Write-Host "  File $file not found"
    }
}

Write-Host "Final navigation update complete!"
