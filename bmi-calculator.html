<!DOCTYPE html>
<html lang="en" class="light" style="color-scheme: light;">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>BMI Calculator - Body Mass Index Calculator | Health Calculator</title>
    <meta name="description" content="Free BMI calculator to calculate your Body Mass Index. Determine if you're underweight, normal weight, overweight, or obese. Get health recommendations based on your BMI results.">
    <meta name="keywords" content="BMI calculator,body mass index calculator,BMI chart,healthy weight calculator,obesity calculator,weight status calculator,health BMI,BMI categories,ideal weight calculator,body fat calculator">
    <meta name="theme-color" media="(prefers-color-scheme: light)" content="white">
    <meta name="theme-color" media="(prefers-color-scheme: dark)" content="black">
    <meta name="creator" content="Health Calculator">
    <link rel="canonical" href="/bmi-calculator.html">
    <meta property="og:title" content="BMI Calculator - Body Mass Index Calculator">
    <meta property="og:description" content="Free BMI calculator to calculate your Body Mass Index. Determine if you're underweight, normal weight, overweight, or obese. Get health recommendations based on your BMI results.">
    <meta property="og:type" content="website">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="BMI Calculator - Body Mass Index Calculator">
    <meta name="twitter:description" content="Free BMI calculator to calculate your Body Mass Index. Determine if you're underweight, normal weight, overweight, or obese. Get health recommendations based on your BMI results.">
    <link rel="stylesheet" href="styles.css">
</head>
<body class="min-h-screen bg-background font-sans antialiased">
    <div class="relative flex min-h-screen flex-col">
        <!-- Promotion Banner -->
        <div class="bg-green-100/90 dark:bg-green-900/30 border-b border-green-200 dark:border-green-800/30 transition-colors duration-500">
            <div class="container mx-auto px-4 py-3">
                <div class="flex items-center justify-between gap-x-4">
                    <div class="flex items-center gap-x-2 flex-1">
                        <span class="hidden sm:inline">⚡ New!</span>
                        <span class="text-sm sm:text-base">Calculate your <a class="font-semibold text-green-600 dark:text-green-400 hover:underline" href="#calculator">BMI instantly</a><span class="hidden sm:inline"> - Get personalized health recommendations!</span></span>
                    </div>
                    <div class="flex items-center gap-x-1 sm:gap-x-2">
                        <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-8 w-8 hover:bg-green-200/70 dark:hover:bg-green-800/30">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                                <path d="M18 6 6 18"></path>
                                <path d="m6 6 12 12"></path>
                            </svg>
                            <span class="sr-only">Dismiss</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Header -->
        <header class="supports-backdrop-blur:bg-background/60 sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur">
            <div class="container flex h-14 items-center">
                <div class="mr-4 hidden md:flex">
                    <nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex max-w-max flex-1 items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256" class="h-6 w-6 dark:text-white" aria-hidden="true">
                            <path d="M0 0h256v256H0z" fill="none"></path>
                            <rect width="256" height="256" fill="none" stroke="currentColor" stroke-width="16"></rect>
                            <rect x="32" y="32" width="192" height="64" fill="currentColor"></rect>
                            <circle cx="64" cy="128" r="16" fill="currentColor"></circle>
                            <circle cx="128" cy="128" r="16" fill="currentColor"></circle>
                            <circle cx="192" cy="128" r="16" fill="currentColor"></circle>
                            <circle cx="64" cy="176" r="16" fill="currentColor"></circle>
                            <circle cx="128" cy="176" r="16" fill="currentColor"></circle>
                            <circle cx="192" cy="176" r="16" fill="currentColor"></circle>
                            <circle cx="64" cy="224" r="16" fill="currentColor"></circle>
                            <circle cx="128" cy="224" r="16" fill="currentColor"></circle>
                            <circle cx="192" cy="224" r="16" fill="currentColor"></circle>
                        </svg>
                        <div style="position:relative">
                            <ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr">
                                <li><a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="index.html">Health Calculator Suite</a></li>
                                <li><a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="#calculator">BMI Calculator</a></li>
                                <li><a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="#about">About BMI</a></li>
                            </ul>
                        </div>
                    </nav>
                </div>
                <div class="container flex h-16 items-center space-x-4 sm:justify-between sm:space-x-0">
                    <div class="flex flex-1 items-center justify-end space-x-4">
                        <nav class="flex items-center space-x-1">
                            <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 w-10" onclick="toggleTheme()">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-[1.5rem] w-[1.3rem] dark:hidden">
                                    <circle cx="12" cy="12" r="4"></circle>
                                    <path d="M12 2v2"></path>
                                    <path d="M12 20v2"></path>
                                    <path d="m4.93 4.93 1.41 1.41"></path>
                                    <path d="m17.66 17.66 1.41 1.41"></path>
                                    <path d="M2 12h2"></path>
                                    <path d="M20 12h2"></path>
                                    <path d="m6.34 17.66-1.41 1.41"></path>
                                    <path d="m19.07 4.93-1.41 1.41"></path>
                                </svg>
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="hidden h-5 w-5 dark:block dark:text-white">
                                    <path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"></path>
                                </svg>
                                <span class="sr-only">Toggle theme</span>
                            </button>
                        </nav>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <div class="flex-1">
            <main class="flex flex-col items-center justify-between">
                <div class="container relative">
                    <div class="flex flex-col md:flex-row md:gap-8">
                        <div class="flex-1">
                            <section class="flex flex-col items-start gap-2 px-4 pt-8 md:pt-12 pb-8">
                                <a class="group inline-flex items-center rounded-lg text-sm font-medium transition-colors mb-6 hover:text-primary" href="#health-tips">
                                    <span class="mr-2">💪</span>Looking for health tips? Check our Health Guide
                                    <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" class="ml-1 h-4 w-4 group-hover:translate-x-0.5 transition-transform">
                                        <path d="M8.14645 3.14645C8.34171 2.95118 8.65829 2.95118 8.85355 3.14645L12.8536 7.14645C13.0488 7.34171 13.0488 7.65829 12.8536 7.85355L8.85355 11.8536C8.65829 12.0488 8.34171 12.0488 8.14645 11.8536C7.95118 11.6583 7.95118 11.3417 8.14645 11.1464L11.2929 8H2.5C2.22386 8 2 7.77614 2 7.5C2 7.22386 2.22386 7 2.5 7H11.2929L8.14645 3.85355C7.95118 3.65829 7.95118 3.34171 8.14645 3.14645Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path>
                                    </svg>
                                </a>
                                <h1 class="text-3xl font-bold leading-tight tracking-tighter md:text-5xl lg:leading-[1.1]">BMI Calculator</h1>
                                <span class="max-w-[750px] text-lg text-muted-foreground sm:text-xl">
                                    Calculate your Body Mass Index and understand your weight status with personalized health recommendations
                                </span>
                            </section>
                        </div>
                    </div>

                    <!-- Calculator Section -->
                    <section id="calculator" class="px-4 py-8">
                        <div class="flex flex-wrap justify-center items-center w-full gap-4 flex-grow overflow-hidden rounded-[0.5rem] p-4 sm:p-8 border bg-background shadow">
                            <div class="flex flex-col md:flex-row gap-4 w-full max-w-screen-xl">
                                <!-- Calculator Input Panel -->
                                <div class="md:w-1/3 flex flex-col gap-4">
                                    <div class="rounded-lg border bg-card text-card-foreground shadow-sm m-2 flex-1">
                                        <div class="flex flex-col space-y-1.5 p-6">
                                            <h3 class="text-2xl font-semibold leading-none tracking-tight">Your Details</h3>
                                            <p class="text-sm text-muted-foreground">Enter your height and weight</p>
                                        </div>
                                        <div class="p-6 pt-0">
                                            <form class="grid gap-6" id="bmiForm">
                                                <!-- Unit System Selection -->
                                                <div class="flex flex-col space-y-1.5">
                                                    <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="unitSystem">Unit System</label>
                                                    <select class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" id="unitSystem" onchange="switchUnits()">
                                                        <option value="metric">Metric (kg, cm)</option>
                                                        <option value="imperial">Imperial (lbs, ft/in)</option>
                                                    </select>
                                                </div>

                                                <!-- Metric System Fields -->
                                                <div id="metricFields" class="unit-fields">
                                                    <div class="flex flex-col space-y-1.5">
                                                        <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="heightCm">Height (cm)</label>
                                                        <input type="number" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" id="heightCm" placeholder="Enter height in cm" value="170" min="50" max="300">
                                                    </div>
                                                    <div class="flex flex-col space-y-1.5">
                                                        <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="weightKg">Weight (kg)</label>
                                                        <input type="number" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" id="weightKg" placeholder="Enter weight in kg" value="70" min="20" max="500" step="0.1">
                                                    </div>
                                                </div>

                                                <!-- Imperial System Fields -->
                                                <div id="imperialFields" class="unit-fields" style="display: none;">
                                                    <div class="flex flex-col space-y-1.5">
                                                        <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Height</label>
                                                        <div class="flex gap-2">
                                                            <input type="number" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" id="heightFt" placeholder="Feet" value="5" min="3" max="8">
                                                            <input type="number" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" id="heightIn" placeholder="Inches" value="7" min="0" max="11">
                                                        </div>
                                                    </div>
                                                    <div class="flex flex-col space-y-1.5">
                                                        <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="weightLbs">Weight (lbs)</label>
                                                        <input type="number" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" id="weightLbs" placeholder="Enter weight in lbs" value="154" min="50" max="1000" step="0.1">
                                                    </div>
                                                </div>

                                                <!-- Age and Gender (Optional) -->
                                                <div class="flex flex-col space-y-1.5">
                                                    <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="age">Age (optional)</label>
                                                    <input type="number" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" id="age" placeholder="Enter your age" min="1" max="120">
                                                </div>

                                                <div class="flex flex-col space-y-1.5">
                                                    <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="gender">Gender (optional)</label>
                                                    <select class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" id="gender">
                                                        <option value="">Select gender</option>
                                                        <option value="male">Male</option>
                                                        <option value="female">Female</option>
                                                    </select>
                                                </div>

                                                <!-- Calculate Button -->
                                                <button type="button" class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2" onclick="calculateBMI()">
                                                    Calculate BMI
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>

                                <!-- Results Panel -->
                                <div class="md:w-2/3 flex flex-col gap-4">
                                    <div class="rounded-lg border bg-card text-card-foreground shadow-sm m-2 flex-1">
                                        <div class="flex flex-col space-y-1.5 p-6">
                                            <h3 class="text-2xl font-semibold leading-none tracking-tight">BMI Results</h3>
                                        </div>
                                        <div class="p-6 pt-0">
                                            <div id="bmiResults" class="w-full overflow-auto">
                                                <div class="text-center text-muted-foreground py-8">
                                                    Enter your details and click Calculate BMI to see results
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- BMI Chart Panel -->
                                    <div class="rounded-lg border bg-card text-card-foreground shadow-sm m-2 flex-1">
                                        <div class="flex flex-col space-y-1.5 p-6">
                                            <h3 class="text-2xl font-semibold leading-none tracking-tight">BMI Chart</h3>
                                        </div>
                                        <div class="p-6 pt-0">
                                            <div id="bmiChart" class="w-full h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                                                <canvas id="bmiCanvas" width="400" height="200"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- Educational Content Section -->
                    <section id="about" class="px-4 py-8">
                        <div class="rounded-lg border bg-card text-card-foreground shadow-sm m-2 w-full flex-1 flex flex-col">
                            <div class="flex flex-col space-y-1.5 p-6">
                                <h3 class="text-2xl font-semibold leading-none tracking-tight">Understanding Body Mass Index (BMI)</h3>
                            </div>
                            <div class="p-6 pt-0">
                                <div class="container mx-auto p-6">
                                    <div class="grid md:grid-cols-2 gap-6">
                                        <!-- Health Image -->
                                        <div class="space-y-4">
                                            <div class="bg-gradient-to-br from-green-100 to-blue-100 p-6 rounded-lg">
                                                <div class="w-full h-48 bg-gradient-to-r from-green-200 to-blue-200 rounded-lg flex items-center justify-center">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="text-green-600">
                                                        <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.29 1.51 4.04 3 5.5l7 7Z"/>
                                                    </svg>
                                                </div>
                                                <h4 class="text-lg font-semibold mt-4 text-green-800">Health & Wellness</h4>
                                                <p class="text-green-700 text-sm">Monitor your health with accurate BMI calculations and personalized recommendations.</p>
                                            </div>
                                        </div>

                                        <!-- Content -->
                                        <div class="space-y-4">
                                            <h4 class="text-xl font-semibold">What is BMI?</h4>
                                            <p class="text-lg mb-4">Body Mass Index (BMI) is a simple calculation using a person's height and weight. The BMI is defined as the body mass divided by the square of the body height, and is expressed in units of kg/m².</p>

                                            <h4 class="text-lg font-medium mt-4 mb-2">BMI Categories:</h4>
                                            <ul class="list-disc pl-5 mb-4 space-y-2">
                                                <li class="mb-2"><strong>Underweight:</strong> BMI less than 18.5</li>
                                                <li class="mb-2"><strong>Normal weight:</strong> BMI 18.5-24.9</li>
                                                <li class="mb-2"><strong>Overweight:</strong> BMI 25-29.9</li>
                                                <li class="mb-2"><strong>Obese:</strong> BMI 30 or greater</li>
                                            </ul>
                                        </div>
                                    </div>

                                    <!-- BMI Information Grid -->
                                    <div class="mt-8 grid md:grid-cols-3 gap-4">
                                        <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                                            <div class="flex items-center mb-3">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-blue-600 mr-2">
                                                    <path d="M9 12l2 2 4-4"/>
                                                    <path d="M21 12c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1"/>
                                                    <path d="M3 12c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1"/>
                                                    <path d="M12 21c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1"/>
                                                    <path d="M12 3c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1"/>
                                                </svg>
                                                <h5 class="font-semibold text-blue-800">Accurate Calculation</h5>
                                            </div>
                                            <p class="text-sm text-blue-700">Our BMI calculator uses the standard formula: weight (kg) / height (m)²</p>
                                        </div>

                                        <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                                            <div class="flex items-center mb-3">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-green-600 mr-2">
                                                    <path d="M22 12h-4l-3 9L9 3l-3 9H2"/>
                                                </svg>
                                                <h5 class="font-semibold text-green-800">Health Insights</h5>
                                            </div>
                                            <p class="text-sm text-green-700">Get personalized health recommendations based on your BMI category.</p>
                                        </div>

                                        <div class="bg-purple-50 p-4 rounded-lg border border-purple-200">
                                            <div class="flex items-center mb-3">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-purple-600 mr-2">
                                                    <circle cx="12" cy="12" r="10"/>
                                                    <path d="M12 6v6l4 2"/>
                                                </svg>
                                                <h5 class="font-semibold text-purple-800">Track Progress</h5>
                                            </div>
                                            <p class="text-sm text-purple-700">Monitor your BMI changes over time to track your health journey.</p>
                                        </div>
                                    </div>

                                    <!-- Health Tips -->
                                    <div class="mt-8 bg-yellow-50 p-6 rounded-lg border border-yellow-200" id="health-tips">
                                        <h4 class="text-lg font-semibold text-yellow-800 mb-3">💡 Health & Wellness Tips</h4>
                                        <div class="grid md:grid-cols-2 gap-4 text-sm text-yellow-700">
                                            <div>
                                                <p class="mb-2"><strong>Maintain Balance:</strong> A healthy BMI is just one indicator of overall health.</p>
                                                <p class="mb-2"><strong>Stay Active:</strong> Regular physical activity is crucial for maintaining a healthy weight.</p>
                                            </div>
                                            <div>
                                                <p class="mb-2"><strong>Eat Well:</strong> Focus on a balanced diet with plenty of fruits and vegetables.</p>
                                                <p class="mb-2"><strong>Consult Professionals:</strong> Always consult healthcare providers for personalized advice.</p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Important Note -->
                                    <div class="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                                        <p class="text-sm text-red-800">
                                            <strong>Important:</strong> BMI is a screening tool and not a diagnostic tool. It doesn't directly measure body fat or muscle mass.
                                            Athletes and very muscular individuals may have a high BMI but low body fat. Always consult with healthcare professionals for comprehensive health assessment.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
            </main>
        </div>
    </div>

    <script src="bmi-calculator.js"></script>
</body>
</html>
