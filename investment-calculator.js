// Investment Calculator JavaScript

// Theme Toggle Functionality
function toggleTheme() {
    const html = document.documentElement;
    const currentTheme = html.classList.contains('dark') ? 'dark' : 'light';
    
    if (currentTheme === 'light') {
        html.classList.remove('light');
        html.classList.add('dark');
        html.style.colorScheme = 'dark';
        localStorage.setItem('theme', 'dark');
    } else {
        html.classList.remove('dark');
        html.classList.add('light');
        html.style.colorScheme = 'light';
        localStorage.setItem('theme', 'light');
    }
}

// Initialize theme from localStorage
function initializeTheme() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    const html = document.documentElement;
    
    html.classList.remove('light', 'dark');
    html.classList.add(savedTheme);
    html.style.colorScheme = savedTheme;
}

// Investment Calculation Function
function calculateInvestment() {
    const initialAmount = parseFloat(document.getElementById('initialAmount').value);
    const monthlyContribution = parseFloat(document.getElementById('monthlyContribution').value) || 0;
    const annualReturn = parseFloat(document.getElementById('annualReturn').value) / 100;
    const investmentPeriod = parseFloat(document.getElementById('investmentPeriod').value);
    const compoundingFrequency = parseFloat(document.getElementById('compoundingFrequency').value);
    const investmentType = document.getElementById('investmentType').value;
    
    // Validation
    if (isNaN(initialAmount) || initialAmount <= 0) {
        showError('Please enter a valid initial investment amount');
        return;
    }
    
    if (isNaN(annualReturn) || annualReturn < 0) {
        showError('Please enter a valid annual return rate');
        return;
    }
    
    if (isNaN(investmentPeriod) || investmentPeriod <= 0) {
        showError('Please enter a valid investment period');
        return;
    }
    
    // Calculate compound interest with regular contributions
    const monthlyRate = annualReturn / 12;
    const totalMonths = investmentPeriod * 12;
    
    // Future value of initial investment
    const futureValueInitial = initialAmount * Math.pow(1 + annualReturn / compoundingFrequency, compoundingFrequency * investmentPeriod);
    
    // Future value of monthly contributions (annuity)
    let futureValueContributions = 0;
    if (monthlyContribution > 0 && monthlyRate > 0) {
        futureValueContributions = monthlyContribution * (Math.pow(1 + monthlyRate, totalMonths) - 1) / monthlyRate;
    } else if (monthlyContribution > 0) {
        futureValueContributions = monthlyContribution * totalMonths;
    }
    
    const totalFutureValue = futureValueInitial + futureValueContributions;
    const totalContributions = initialAmount + (monthlyContribution * totalMonths);
    const totalGains = totalFutureValue - totalContributions;
    const totalReturn = (totalGains / totalContributions) * 100;
    
    // Calculate year-by-year breakdown for chart
    const yearlyData = calculateYearlyBreakdown(initialAmount, monthlyContribution, annualReturn, investmentPeriod);
    
    // Get investment type information
    const investmentInfo = getInvestmentTypeInfo(investmentType);
    
    // Display results
    const results = `
        <div class="space-y-6">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div class="bg-green-50 p-4 rounded-lg">
                    <div class="text-2xl font-bold text-green-600">$${totalFutureValue.toLocaleString(undefined, {maximumFractionDigits: 0})}</div>
                    <div class="text-sm text-green-800">Final Value</div>
                </div>
                <div class="bg-blue-50 p-4 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600">$${totalContributions.toLocaleString(undefined, {maximumFractionDigits: 0})}</div>
                    <div class="text-sm text-blue-800">Total Invested</div>
                </div>
                <div class="bg-purple-50 p-4 rounded-lg">
                    <div class="text-2xl font-bold text-purple-600">$${totalGains.toLocaleString(undefined, {maximumFractionDigits: 0})}</div>
                    <div class="text-sm text-purple-800">Total Gains</div>
                </div>
                <div class="bg-orange-50 p-4 rounded-lg">
                    <div class="text-2xl font-bold text-orange-600">${totalReturn.toFixed(1)}%</div>
                    <div class="text-sm text-orange-800">Total Return</div>
                </div>
            </div>
            
            <table class="w-full caption-bottom text-sm">
                <thead class="bg-gray-200">
                    <tr class="border-b transition-colors hover:bg-muted/50">
                        <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground">Description</th>
                        <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="border-b transition-colors hover:bg-muted/50">
                        <td class="p-4 align-middle">Initial Investment</td>
                        <td class="p-4 align-middle">$${initialAmount.toLocaleString()}</td>
                    </tr>
                    <tr class="border-b transition-colors hover:bg-muted/50">
                        <td class="p-4 align-middle">Monthly Contribution</td>
                        <td class="p-4 align-middle">$${monthlyContribution.toLocaleString()}</td>
                    </tr>
                    <tr class="border-b transition-colors hover:bg-muted/50">
                        <td class="p-4 align-middle">Annual Return Rate</td>
                        <td class="p-4 align-middle">${(annualReturn * 100).toFixed(1)}%</td>
                    </tr>
                    <tr class="border-b transition-colors hover:bg-muted/50">
                        <td class="p-4 align-middle">Investment Period</td>
                        <td class="p-4 align-middle">${investmentPeriod} years</td>
                    </tr>
                    <tr class="border-b transition-colors hover:bg-muted/50">
                        <td class="p-4 align-middle">Compounding</td>
                        <td class="p-4 align-middle">${getCompoundingText(compoundingFrequency)}</td>
                    </tr>
                    <tr class="border-b transition-colors hover:bg-muted/50">
                        <td class="p-4 align-middle">Investment Type</td>
                        <td class="p-4 align-middle">${investmentInfo.name}</td>
                    </tr>
                    <tr class="border-b transition-colors hover:bg-muted/50 font-bold">
                        <td class="p-4 align-middle">Final Investment Value</td>
                        <td class="p-4 align-middle">$${totalFutureValue.toLocaleString(undefined, {maximumFractionDigits: 0})}</td>
                    </tr>
                    <tr class="border-b transition-colors hover:bg-muted/50">
                        <td class="p-4 align-middle">Total Contributions</td>
                        <td class="p-4 align-middle">$${totalContributions.toLocaleString(undefined, {maximumFractionDigits: 0})}</td>
                    </tr>
                    <tr class="border-b transition-colors hover:bg-muted/50">
                        <td class="p-4 align-middle">Investment Gains</td>
                        <td class="p-4 align-middle">$${totalGains.toLocaleString(undefined, {maximumFractionDigits: 0})}</td>
                    </tr>
                </tbody>
            </table>
            
            <div class="bg-blue-50 p-4 rounded-lg">
                <h4 class="font-semibold text-blue-800 mb-2">${investmentInfo.name} Information:</h4>
                <p class="text-sm text-blue-700">${investmentInfo.description}</p>
                <ul class="text-sm text-blue-700 mt-2 space-y-1">
                    ${investmentInfo.features.map(feature => `<li>• ${feature}</li>`).join('')}
                </ul>
            </div>
            
            <div class="text-xs text-muted-foreground text-center">
                <p><strong>Note:</strong> This calculation assumes a constant rate of return and doesn't account for taxes, fees, or market volatility.</p>
            </div>
        </div>
    `;
    
    document.getElementById('investmentResults').innerHTML = results;
    
    // Draw investment growth chart
    drawInvestmentChart(yearlyData);
}

// Calculate year-by-year breakdown
function calculateYearlyBreakdown(initial, monthly, rate, years) {
    const data = [];
    let currentValue = initial;
    let totalContributions = initial;
    
    for (let year = 0; year <= years; year++) {
        if (year === 0) {
            data.push({
                year: year,
                value: currentValue,
                contributions: totalContributions,
                gains: 0
            });
        } else {
            // Add monthly contributions for the year
            const yearlyContributions = monthly * 12;
            totalContributions += yearlyContributions;
            
            // Calculate growth
            currentValue = (currentValue + yearlyContributions) * (1 + rate);
            
            data.push({
                year: year,
                value: currentValue,
                contributions: totalContributions,
                gains: currentValue - totalContributions
            });
        }
    }
    
    return data;
}

// Get compounding frequency text
function getCompoundingText(frequency) {
    const frequencies = {
        1: 'Annually',
        2: 'Semi-annually',
        4: 'Quarterly',
        12: 'Monthly',
        365: 'Daily'
    };
    return frequencies[frequency] || 'Custom';
}

// Get investment type information
function getInvestmentTypeInfo(type) {
    const types = {
        stocks: {
            name: 'Stocks/Equity',
            description: 'Ownership shares in companies with potential for high returns and higher risk.',
            features: [
                'Historical average: 7-10% annually',
                'Higher volatility and risk',
                'Potential for significant growth',
                'Dividend income possible'
            ]
        },
        bonds: {
            name: 'Bonds',
            description: 'Fixed-income securities that provide steady, predictable returns.',
            features: [
                'Lower risk than stocks',
                'Steady income stream',
                'Typical returns: 2-5% annually',
                'Good for portfolio diversification'
            ]
        },
        mutual: {
            name: 'Mutual Funds',
            description: 'Professionally managed portfolios of stocks, bonds, or other securities.',
            features: [
                'Professional management',
                'Built-in diversification',
                'Various risk levels available',
                'Management fees apply'
            ]
        },
        etf: {
            name: 'ETFs (Exchange-Traded Funds)',
            description: 'Funds that track indexes and trade like stocks with low fees.',
            features: [
                'Low expense ratios',
                'Instant diversification',
                'Trade like individual stocks',
                'Tax efficient'
            ]
        },
        savings: {
            name: 'Savings Account',
            description: 'Low-risk, FDIC-insured accounts with modest returns.',
            features: [
                'FDIC insured up to $250,000',
                'Very low risk',
                'Low returns: 0.5-2% typically',
                'High liquidity'
            ]
        },
        cd: {
            name: 'Certificate of Deposit',
            description: 'Time deposits with fixed terms and guaranteed returns.',
            features: [
                'FDIC insured',
                'Fixed interest rate',
                'Penalty for early withdrawal',
                'Terms from 3 months to 5+ years'
            ]
        },
        retirement: {
            name: 'Retirement Account',
            description: 'Tax-advantaged accounts for long-term retirement savings.',
            features: [
                'Tax benefits (401k, IRA, Roth IRA)',
                'Long-term growth focus',
                'Contribution limits apply',
                'Withdrawal restrictions'
            ]
        },
        other: {
            name: 'Other Investment',
            description: 'Various other investment vehicles with different characteristics.',
            features: [
                'Risk varies by investment type',
                'Returns depend on market conditions',
                'Research thoroughly before investing',
                'Consider professional advice'
            ]
        }
    };
    
    return types[type] || types.other;
}

// Investment Chart Drawing Function
function drawInvestmentChart(data) {
    const canvas = document.getElementById('investmentCanvas');
    const ctx = canvas.getContext('2d');
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    if (data.length === 0) return;
    
    // Chart dimensions
    const padding = 40;
    const chartWidth = canvas.width - 2 * padding;
    const chartHeight = canvas.height - 2 * padding;
    
    // Find max value for scaling
    const maxValue = Math.max(...data.map(d => d.value));
    const maxYear = data[data.length - 1].year;
    
    // Draw axes
    ctx.strokeStyle = '#ccc';
    ctx.lineWidth = 1;
    
    // Y-axis
    ctx.beginPath();
    ctx.moveTo(padding, padding);
    ctx.lineTo(padding, padding + chartHeight);
    ctx.stroke();
    
    // X-axis
    ctx.beginPath();
    ctx.moveTo(padding, padding + chartHeight);
    ctx.lineTo(padding + chartWidth, padding + chartHeight);
    ctx.stroke();
    
    // Draw data lines
    if (data.length > 1) {
        // Total value line (green)
        ctx.strokeStyle = '#22c55e';
        ctx.lineWidth = 3;
        ctx.beginPath();
        
        data.forEach((point, index) => {
            const x = padding + (point.year / maxYear) * chartWidth;
            const y = padding + chartHeight - (point.value / maxValue) * chartHeight;
            
            if (index === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        });
        ctx.stroke();
        
        // Contributions line (blue)
        ctx.strokeStyle = '#3b82f6';
        ctx.lineWidth = 2;
        ctx.beginPath();
        
        data.forEach((point, index) => {
            const x = padding + (point.year / maxYear) * chartWidth;
            const y = padding + chartHeight - (point.contributions / maxValue) * chartHeight;
            
            if (index === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        });
        ctx.stroke();
    }
    
    // Add labels
    ctx.fillStyle = '#000';
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('Investment Growth Over Time', canvas.width / 2, 20);
    
    // Legend
    ctx.fillStyle = '#22c55e';
    ctx.fillRect(20, canvas.height - 40, 15, 3);
    ctx.fillStyle = '#000';
    ctx.font = '10px Arial';
    ctx.textAlign = 'left';
    ctx.fillText('Total Value', 40, canvas.height - 36);
    
    ctx.fillStyle = '#3b82f6';
    ctx.fillRect(20, canvas.height - 25, 15, 3);
    ctx.fillStyle = '#000';
    ctx.fillText('Contributions', 40, canvas.height - 21);
    
    // Y-axis labels
    ctx.fillStyle = '#666';
    ctx.font = '10px Arial';
    ctx.textAlign = 'right';
    for (let i = 0; i <= 4; i++) {
        const value = (maxValue / 4) * i;
        const y = padding + chartHeight - (i / 4) * chartHeight;
        ctx.fillText(`$${(value / 1000).toFixed(0)}k`, padding - 5, y + 3);
    }
    
    // X-axis labels
    ctx.textAlign = 'center';
    for (let i = 0; i <= maxYear; i += Math.max(1, Math.floor(maxYear / 5))) {
        const x = padding + (i / maxYear) * chartWidth;
        ctx.fillText(`${i}y`, x, canvas.height - 5);
    }
}

// Error Display Function
function showError(message) {
    document.getElementById('investmentResults').innerHTML = `
        <div class="text-center py-8">
            <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                <p class="text-red-800 font-medium">${message}</p>
            </div>
        </div>
    `;
}

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeTheme();
    
    // Add event listeners for auto-calculation
    const inputs = document.querySelectorAll('input[type="number"], select');
    inputs.forEach(input => {
        input.addEventListener('input', function() {
            // Auto-calculate if all required fields are filled
            const initialAmount = document.getElementById('initialAmount').value;
            const annualReturn = document.getElementById('annualReturn').value;
            const investmentPeriod = document.getElementById('investmentPeriod').value;
            
            if (initialAmount && annualReturn && investmentPeriod) {
                // Debounce the calculation
                clearTimeout(this.calcTimeout);
                this.calcTimeout = setTimeout(calculateInvestment, 500);
            }
        });
    });
});
