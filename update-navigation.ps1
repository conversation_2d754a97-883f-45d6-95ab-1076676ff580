# PowerShell script to update navigation links in all HTML files

# Define the mapping of href="#" to actual file names
$linkMappings = @{
    'href="#".*Contract Rate Benchmarking' = 'href="contract-rate-benchmarking.html".*Contract Rate Benchmarking'
    'href="#".*Salary Rate Benchmarking' = 'href="salary-rate-benchmarking.html".*Salary Rate Benchmarking'
    'href="#".*Rate Cut Calculator' = 'href="rate-cut-calculator.html".*Rate Cut Calculator'
    'href="#".*Mortgage Payoff Calculator' = 'href="mortgage-payoff-calculator.html".*Mortgage Payoff Calculator'
    'href="#".*Tax Calendar' = 'href="tax-calendar.html".*Tax Calendar'
    'href="#".*Quote Generator' = 'href="quote-generator.html".*Quote Generator'
    'href="#".*Payslip Generator' = 'href="payslip-generator.html".*Payslip Generator'
    'href="#".*Vehicle Registration' = 'href="vehicle-registration.html".*Vehicle Registration'
}

# Get all HTML files except index.html
$htmlFiles = Get-ChildItem -Path "." -Name "*.html" | Where-Object { $_ -ne "index.html" }

Write-Host "Found $($htmlFiles.Count) HTML files to update"

foreach ($file in $htmlFiles) {
    Write-Host "Updating $file..."
    
    $content = Get-Content -Path $file -Raw
    $originalContent = $content
    
    # Apply each mapping
    foreach ($pattern in $linkMappings.Keys) {
        $replacement = $linkMappings[$pattern]
        
        # Simple replacement for href="#" patterns
        $content = $content -replace 'href="#"([^>]*Contract Rate Benchmarking)', 'href="contract-rate-benchmarking.html"$1'
        $content = $content -replace 'href="#"([^>]*Salary Rate Benchmarking)', 'href="salary-rate-benchmarking.html"$1'
        $content = $content -replace 'href="#"([^>]*Rate Cut Calculator)', 'href="rate-cut-calculator.html"$1'
        $content = $content -replace 'href="#"([^>]*Mortgage Payoff Calculator)', 'href="mortgage-payoff-calculator.html"$1'
        $content = $content -replace 'href="#"([^>]*Tax Calendar)', 'href="tax-calendar.html"$1'
        $content = $content -replace 'href="#"([^>]*Quote Generator)', 'href="quote-generator.html"$1'
        $content = $content -replace 'href="#"([^>]*Payslip Generator)', 'href="payslip-generator.html"$1'
        $content = $content -replace 'href="#"([^>]*Vehicle Registration)', 'href="vehicle-registration.html"$1'
    }
    
    # Only write if content changed
    if ($content -ne $originalContent) {
        Set-Content -Path $file -Value $content -NoNewline
        Write-Host "  Updated $file"
    } else {
        Write-Host "  No changes needed for $file"
    }
}

Write-Host "Navigation update complete!"
