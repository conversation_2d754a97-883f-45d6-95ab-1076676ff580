# PowerShell script to fix all navigation links across all HTML files

# Define the link mappings
$linkMappings = @{
    'href="#"([^>]*Contractor Pay Calculator)' = 'href="contractor-pay-calculator.html"$1'
    'href="#"([^>]*Contract Rate Benchmarking)' = 'href="contract-rate-benchmarking.html"$1'
    'href="#"([^>]*Salary Rate Benchmarking)' = 'href="salary-rate-benchmarking.html"$1'
    'href="#"([^>]*Home Loan Comparison)' = 'href="home-loan-comparison.html"$1'
    'href="#"([^>]*Rate Cut Calculator)' = 'href="rate-cut-calculator.html"$1'
    'href="#"([^>]*Mortgage Payoff Calculator)' = 'href="mortgage-payoff-calculator.html"$1'
    'href="#"([^>]*Tax Cut Calculator)' = 'href="tax-cut-calculator.html"$1'
    'href="#"([^>]*Tax Calendar)' = 'href="tax-calendar.html"$1'
    'href="#"([^>]*GST Calculator)' = 'href="gst-calculator.html"$1'
    'href="#"([^>]*Quote Generator)' = 'href="quote-generator.html"$1'
    'href="#"([^>]*Payslip Generator)' = 'href="payslip-generator.html"$1'
    'href="#"([^>]*EV & ICE Novated Lease)' = 'href="novated-lease-calculator.html"$1'
    'href="#"([^>]*Vehicle Registration)' = 'href="vehicle-registration.html"$1'
    'href="#"([^>]*Public Holidays)' = 'href="public-holidays.html"$1'
    'href="#"([^>]*School Holidays)' = 'href="school-holidays.html"$1'
}

# Get all HTML files except utility files
$htmlFiles = Get-ChildItem -Path "." -Name "*.html" | Where-Object { 
    $_ -ne "index.html" -and 
    $_ -ne "header-footer.html" -and 
    $_ -ne "paycal-header-footer.html" 
}

Write-Host "Found $($htmlFiles.Count) HTML files to update"
Write-Host "Files to update: $($htmlFiles -join ', ')"

$totalUpdates = 0

foreach ($file in $htmlFiles) {
    Write-Host "`nProcessing $file..."
    
    try {
        $content = Get-Content -Path $file -Raw -Encoding UTF8
        $originalContent = $content
        $fileUpdates = 0
        
        # Apply each mapping
        foreach ($pattern in $linkMappings.Keys) {
            $replacement = $linkMappings[$pattern]
            $beforeCount = ([regex]::Matches($content, $pattern)).Count
            $content = $content -replace $pattern, $replacement
            $afterCount = ([regex]::Matches($content, $pattern)).Count
            $updates = $beforeCount - $afterCount
            
            if ($updates -gt 0) {
                Write-Host "  Updated $updates instances of: $($pattern.Split('(')[1].Split(')')[0])"
                $fileUpdates += $updates
            }
        }
        
        # Only write if content changed
        if ($content -ne $originalContent) {
            Set-Content -Path $file -Value $content -NoNewline -Encoding UTF8
            Write-Host "  ✅ Updated $file with $fileUpdates changes"
            $totalUpdates += $fileUpdates
        } else {
            Write-Host "  ℹ️  No changes needed for $file"
        }
    }
    catch {
        Write-Host "  ❌ Error processing $file`: $($_.Exception.Message)"
    }
}

Write-Host "`n🎉 Navigation update complete!"
Write-Host "Total updates made: $totalUpdates"
Write-Host "`nAll navigation links should now point to the correct calculator files."
