// Retirement Calculator JavaScript

// Theme Toggle Functionality
function toggleTheme() {
    const html = document.documentElement;
    const currentTheme = html.classList.contains('dark') ? 'dark' : 'light';
    
    if (currentTheme === 'light') {
        html.classList.remove('light');
        html.classList.add('dark');
        html.style.colorScheme = 'dark';
        localStorage.setItem('theme', 'dark');
    } else {
        html.classList.remove('dark');
        html.classList.add('light');
        html.style.colorScheme = 'light';
        localStorage.setItem('theme', 'light');
    }
}

// Initialize theme from localStorage
function initializeTheme() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    const html = document.documentElement;
    
    html.classList.remove('light', 'dark');
    html.classList.add(savedTheme);
    html.style.colorScheme = savedTheme;
}

// Retirement Calculation Function
function calculateRetirement() {
    const currentAge = parseFloat(document.getElementById('currentAge').value);
    const retirementAge = parseFloat(document.getElementById('retirementAge').value);
    const currentSavings = parseFloat(document.getElementById('currentSavings').value) || 0;
    const monthlyContribution = parseFloat(document.getElementById('monthlyContribution').value) || 0;
    const annualIncome = parseFloat(document.getElementById('annualIncome').value);
    const incomeReplacement = parseFloat(document.getElementById('incomeReplacement').value) / 100;
    const expectedReturn = parseFloat(document.getElementById('expectedReturn').value) / 100;
    const lifeExpectancy = parseFloat(document.getElementById('lifeExpectancy').value);
    
    // Validation
    if (isNaN(currentAge) || currentAge < 18 || currentAge > 100) {
        showError('Please enter a valid current age (18-100)');
        return;
    }
    
    if (isNaN(retirementAge) || retirementAge <= currentAge || retirementAge > 100) {
        showError('Please enter a valid retirement age greater than current age');
        return;
    }
    
    if (isNaN(annualIncome) || annualIncome <= 0) {
        showError('Please enter a valid annual income');
        return;
    }
    
    if (isNaN(expectedReturn) || expectedReturn < 0) {
        showError('Please enter a valid expected return rate');
        return;
    }
    
    if (isNaN(lifeExpectancy) || lifeExpectancy <= retirementAge) {
        showError('Please enter a valid life expectancy greater than retirement age');
        return;
    }
    
    // Calculate retirement planning metrics
    const yearsToRetirement = retirementAge - currentAge;
    const yearsInRetirement = lifeExpectancy - retirementAge;
    const desiredAnnualIncome = annualIncome * incomeReplacement;
    
    // Calculate future value of current savings
    const futureValueCurrentSavings = currentSavings * Math.pow(1 + expectedReturn, yearsToRetirement);
    
    // Calculate future value of monthly contributions
    const monthlyRate = expectedReturn / 12;
    const totalMonths = yearsToRetirement * 12;
    let futureValueContributions = 0;
    
    if (monthlyContribution > 0 && monthlyRate > 0) {
        futureValueContributions = monthlyContribution * (Math.pow(1 + monthlyRate, totalMonths) - 1) / monthlyRate;
    } else if (monthlyContribution > 0) {
        futureValueContributions = monthlyContribution * totalMonths;
    }
    
    const totalRetirementSavings = futureValueCurrentSavings + futureValueContributions;
    
    // Calculate required retirement savings using 4% rule
    const requiredRetirementSavings = desiredAnnualIncome / 0.04;
    
    // Calculate shortfall or surplus
    const shortfall = requiredRetirementSavings - totalRetirementSavings;
    const isOnTrack = shortfall <= 0;
    
    // Calculate additional monthly savings needed if there's a shortfall
    let additionalMonthlySavingsNeeded = 0;
    if (shortfall > 0 && monthlyRate > 0) {
        additionalMonthlySavingsNeeded = shortfall / ((Math.pow(1 + monthlyRate, totalMonths) - 1) / monthlyRate);
    } else if (shortfall > 0) {
        additionalMonthlySavingsNeeded = shortfall / totalMonths;
    }
    
    // Calculate total contributions
    const totalContributions = currentSavings + (monthlyContribution * totalMonths);
    const totalGrowth = totalRetirementSavings - totalContributions;
    
    // Calculate retirement income scenarios
    const conservativeIncome = totalRetirementSavings * 0.03; // 3% withdrawal rate
    const moderateIncome = totalRetirementSavings * 0.04; // 4% withdrawal rate
    const aggressiveIncome = totalRetirementSavings * 0.05; // 5% withdrawal rate
    
    // Calculate year-by-year breakdown for chart
    const yearlyData = calculateRetirementTimeline(currentAge, retirementAge, lifeExpectancy, currentSavings, monthlyContribution, expectedReturn, desiredAnnualIncome);
    
    // Display results
    const statusColor = isOnTrack ? 'text-green-600' : 'text-red-600';
    const statusBg = isOnTrack ? 'bg-green-50' : 'bg-red-50';
    const statusText = isOnTrack ? 'On Track!' : 'Needs Attention';
    
    const results = `
        <div class="space-y-6">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div class="${statusBg} p-4 rounded-lg">
                    <div class="text-2xl font-bold ${statusColor}">${statusText}</div>
                    <div class="text-sm ${statusColor.replace('text-', 'text-').replace('-600', '-800')}">Retirement Status</div>
                </div>
                <div class="bg-blue-50 p-4 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600">$${totalRetirementSavings.toLocaleString(undefined, {maximumFractionDigits: 0})}</div>
                    <div class="text-sm text-blue-800">Projected Savings</div>
                </div>
                <div class="bg-purple-50 p-4 rounded-lg">
                    <div class="text-2xl font-bold text-purple-600">$${requiredRetirementSavings.toLocaleString(undefined, {maximumFractionDigits: 0})}</div>
                    <div class="text-sm text-purple-800">Required Savings</div>
                </div>
                <div class="bg-orange-50 p-4 rounded-lg">
                    <div class="text-2xl font-bold text-orange-600">$${moderateIncome.toLocaleString(undefined, {maximumFractionDigits: 0})}</div>
                    <div class="text-sm text-orange-800">Annual Income (4%)</div>
                </div>
            </div>
            
            <table class="w-full caption-bottom text-sm">
                <thead class="bg-gray-200">
                    <tr class="border-b transition-colors hover:bg-muted/50">
                        <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground">Retirement Planning Details</th>
                        <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground">Value</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="border-b transition-colors hover:bg-muted/50">
                        <td class="p-4 align-middle">Current Age</td>
                        <td class="p-4 align-middle">${currentAge} years</td>
                    </tr>
                    <tr class="border-b transition-colors hover:bg-muted/50">
                        <td class="p-4 align-middle">Retirement Age</td>
                        <td class="p-4 align-middle">${retirementAge} years</td>
                    </tr>
                    <tr class="border-b transition-colors hover:bg-muted/50">
                        <td class="p-4 align-middle">Years to Retirement</td>
                        <td class="p-4 align-middle">${yearsToRetirement} years</td>
                    </tr>
                    <tr class="border-b transition-colors hover:bg-muted/50">
                        <td class="p-4 align-middle">Years in Retirement</td>
                        <td class="p-4 align-middle">${yearsInRetirement} years</td>
                    </tr>
                    <tr class="border-b transition-colors hover:bg-muted/50">
                        <td class="p-4 align-middle">Current Savings</td>
                        <td class="p-4 align-middle">$${currentSavings.toLocaleString()}</td>
                    </tr>
                    <tr class="border-b transition-colors hover:bg-muted/50">
                        <td class="p-4 align-middle">Monthly Contribution</td>
                        <td class="p-4 align-middle">$${monthlyContribution.toLocaleString()}</td>
                    </tr>
                    <tr class="border-b transition-colors hover:bg-muted/50">
                        <td class="p-4 align-middle">Expected Annual Return</td>
                        <td class="p-4 align-middle">${(expectedReturn * 100).toFixed(1)}%</td>
                    </tr>
                    <tr class="border-b transition-colors hover:bg-muted/50">
                        <td class="p-4 align-middle">Desired Annual Income</td>
                        <td class="p-4 align-middle">$${desiredAnnualIncome.toLocaleString()}</td>
                    </tr>
                    <tr class="border-b transition-colors hover:bg-muted/50 font-bold">
                        <td class="p-4 align-middle">Projected Retirement Savings</td>
                        <td class="p-4 align-middle">$${totalRetirementSavings.toLocaleString(undefined, {maximumFractionDigits: 0})}</td>
                    </tr>
                    <tr class="border-b transition-colors hover:bg-muted/50">
                        <td class="p-4 align-middle">Required Retirement Savings</td>
                        <td class="p-4 align-middle">$${requiredRetirementSavings.toLocaleString(undefined, {maximumFractionDigits: 0})}</td>
                    </tr>
                    <tr class="border-b transition-colors hover:bg-muted/50">
                        <td class="p-4 align-middle">${isOnTrack ? 'Surplus' : 'Shortfall'}</td>
                        <td class="p-4 align-middle ${isOnTrack ? 'text-green-600' : 'text-red-600'}">${isOnTrack ? '+' : '-'}$${Math.abs(shortfall).toLocaleString(undefined, {maximumFractionDigits: 0})}</td>
                    </tr>
                </tbody>
            </table>
            
            ${!isOnTrack ? `
            <div class="bg-red-50 p-4 rounded-lg border border-red-200">
                <h4 class="font-semibold text-red-800 mb-2">⚠️ Action Required:</h4>
                <p class="text-sm text-red-700 mb-2">You need to save an additional <strong>$${additionalMonthlySavingsNeeded.toFixed(0)} per month</strong> to reach your retirement goal.</p>
                <p class="text-sm text-red-700">Consider increasing your monthly contributions, extending your working years, or adjusting your retirement income expectations.</p>
            </div>
            ` : `
            <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                <h4 class="font-semibold text-green-800 mb-2">✅ Great Job!</h4>
                <p class="text-sm text-green-700">You're on track to meet your retirement goals. Keep up the good work and consider reviewing your plan annually.</p>
            </div>
            `}
            
            <div class="bg-blue-50 p-4 rounded-lg">
                <h4 class="font-semibold text-blue-800 mb-2">💰 Retirement Income Scenarios:</h4>
                <div class="grid md:grid-cols-3 gap-4 text-sm text-blue-700">
                    <div>
                        <p class="font-medium">Conservative (3%)</p>
                        <p>$${conservativeIncome.toLocaleString(undefined, {maximumFractionDigits: 0})} annually</p>
                    </div>
                    <div>
                        <p class="font-medium">Moderate (4%)</p>
                        <p>$${moderateIncome.toLocaleString(undefined, {maximumFractionDigits: 0})} annually</p>
                    </div>
                    <div>
                        <p class="font-medium">Aggressive (5%)</p>
                        <p>$${aggressiveIncome.toLocaleString(undefined, {maximumFractionDigits: 0})} annually</p>
                    </div>
                </div>
            </div>
            
            <div class="text-xs text-muted-foreground text-center">
                <p><strong>Note:</strong> This calculation assumes constant returns and doesn't account for inflation, taxes, or market volatility. Consult with financial advisors for comprehensive planning.</p>
            </div>
        </div>
    `;
    
    document.getElementById('retirementResults').innerHTML = results;
    
    // Draw retirement timeline chart
    drawRetirementChart(yearlyData);
}

// Calculate retirement timeline data
function calculateRetirementTimeline(currentAge, retirementAge, lifeExpectancy, currentSavings, monthlyContribution, expectedReturn, desiredIncome) {
    const data = [];
    let savings = currentSavings;
    const monthlyRate = expectedReturn / 12;
    
    for (let age = currentAge; age <= lifeExpectancy; age++) {
        if (age < retirementAge) {
            // Accumulation phase
            const yearlyContributions = monthlyContribution * 12;
            savings = (savings + yearlyContributions) * (1 + expectedReturn);
            
            data.push({
                age: age,
                savings: savings,
                phase: 'accumulation',
                income: 0
            });
        } else {
            // Withdrawal phase
            const annualWithdrawal = Math.min(desiredIncome, savings * 0.04);
            savings = Math.max(0, savings - annualWithdrawal);
            
            data.push({
                age: age,
                savings: savings,
                phase: 'withdrawal',
                income: annualWithdrawal
            });
        }
    }
    
    return data;
}

// Retirement Chart Drawing Function
function drawRetirementChart(data) {
    const canvas = document.getElementById('retirementCanvas');
    const ctx = canvas.getContext('2d');
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    if (data.length === 0) return;
    
    // Chart dimensions
    const padding = 40;
    const chartWidth = canvas.width - 2 * padding;
    const chartHeight = canvas.height - 2 * padding;
    
    // Find max values for scaling
    const maxSavings = Math.max(...data.map(d => d.savings));
    const minAge = data[0].age;
    const maxAge = data[data.length - 1].age;
    const ageRange = maxAge - minAge;
    
    // Draw axes
    ctx.strokeStyle = '#ccc';
    ctx.lineWidth = 1;
    
    // Y-axis
    ctx.beginPath();
    ctx.moveTo(padding, padding);
    ctx.lineTo(padding, padding + chartHeight);
    ctx.stroke();
    
    // X-axis
    ctx.beginPath();
    ctx.moveTo(padding, padding + chartHeight);
    ctx.lineTo(padding + chartWidth, padding + chartHeight);
    ctx.stroke();
    
    // Draw retirement age line
    const retirementData = data.find(d => d.phase === 'withdrawal');
    if (retirementData) {
        const retirementX = padding + ((retirementData.age - minAge) / ageRange) * chartWidth;
        ctx.strokeStyle = '#f97316';
        ctx.lineWidth = 2;
        ctx.setLineDash([5, 5]);
        ctx.beginPath();
        ctx.moveTo(retirementX, padding);
        ctx.lineTo(retirementX, padding + chartHeight);
        ctx.stroke();
        ctx.setLineDash([]);
    }
    
    // Draw savings line
    if (data.length > 1) {
        ctx.strokeStyle = '#3b82f6';
        ctx.lineWidth = 3;
        ctx.beginPath();
        
        data.forEach((point, index) => {
            const x = padding + ((point.age - minAge) / ageRange) * chartWidth;
            const y = padding + chartHeight - (point.savings / maxSavings) * chartHeight;
            
            if (index === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        });
        ctx.stroke();
    }
    
    // Add labels
    ctx.fillStyle = '#000';
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('Retirement Savings Timeline', canvas.width / 2, 20);
    
    // Legend
    ctx.fillStyle = '#3b82f6';
    ctx.fillRect(20, canvas.height - 40, 15, 3);
    ctx.fillStyle = '#000';
    ctx.font = '10px Arial';
    ctx.textAlign = 'left';
    ctx.fillText('Savings Balance', 40, canvas.height - 36);
    
    ctx.fillStyle = '#f97316';
    ctx.fillRect(20, canvas.height - 25, 15, 3);
    ctx.fillStyle = '#000';
    ctx.fillText('Retirement Age', 40, canvas.height - 21);
    
    // Y-axis labels (savings amounts)
    ctx.fillStyle = '#666';
    ctx.font = '10px Arial';
    ctx.textAlign = 'right';
    for (let i = 0; i <= 4; i++) {
        const value = (maxSavings / 4) * i;
        const y = padding + chartHeight - (i / 4) * chartHeight;
        ctx.fillText(`$${(value / 1000).toFixed(0)}k`, padding - 5, y + 3);
    }
    
    // X-axis labels (ages)
    ctx.textAlign = 'center';
    const ageStep = Math.max(5, Math.floor(ageRange / 8));
    for (let age = minAge; age <= maxAge; age += ageStep) {
        const x = padding + ((age - minAge) / ageRange) * chartWidth;
        ctx.fillText(`${age}`, x, canvas.height - 5);
    }
}

// Error Display Function
function showError(message) {
    document.getElementById('retirementResults').innerHTML = `
        <div class="text-center py-8">
            <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                <p class="text-red-800 font-medium">${message}</p>
            </div>
        </div>
    `;
}

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeTheme();
    
    // Add event listeners for auto-calculation
    const inputs = document.querySelectorAll('input[type="number"], select');
    inputs.forEach(input => {
        input.addEventListener('input', function() {
            // Auto-calculate if all required fields are filled
            const currentAge = document.getElementById('currentAge').value;
            const retirementAge = document.getElementById('retirementAge').value;
            const annualIncome = document.getElementById('annualIncome').value;
            const expectedReturn = document.getElementById('expectedReturn').value;
            const lifeExpectancy = document.getElementById('lifeExpectancy').value;
            
            if (currentAge && retirementAge && annualIncome && expectedReturn && lifeExpectancy) {
                // Debounce the calculation
                clearTimeout(this.calcTimeout);
                this.calcTimeout = setTimeout(calculateRetirement, 500);
            }
        });
    });
});
