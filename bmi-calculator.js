// BMI Calculator JavaScript

// Theme Toggle Functionality
function toggleTheme() {
    const html = document.documentElement;
    const currentTheme = html.classList.contains('dark') ? 'dark' : 'light';
    
    if (currentTheme === 'light') {
        html.classList.remove('light');
        html.classList.add('dark');
        html.style.colorScheme = 'dark';
        localStorage.setItem('theme', 'dark');
    } else {
        html.classList.remove('dark');
        html.classList.add('light');
        html.style.colorScheme = 'light';
        localStorage.setItem('theme', 'light');
    }
}

// Initialize theme from localStorage
function initializeTheme() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    const html = document.documentElement;
    
    html.classList.remove('light', 'dark');
    html.classList.add(savedTheme);
    html.style.colorScheme = savedTheme;
}

// Unit System Switching
function switchUnits() {
    const unitSystem = document.getElementById('unitSystem').value;
    const metricFields = document.getElementById('metricFields');
    const imperialFields = document.getElementById('imperialFields');
    
    if (unitSystem === 'metric') {
        metricFields.style.display = 'block';
        imperialFields.style.display = 'none';
    } else {
        metricFields.style.display = 'none';
        imperialFields.style.display = 'block';
    }
    
    // Clear results when switching units
    document.getElementById('bmiResults').innerHTML = `
        <div class="text-center text-muted-foreground py-8">
            Enter your details and click Calculate BMI to see results
        </div>
    `;
}

// BMI Calculation Function
function calculateBMI() {
    const unitSystem = document.getElementById('unitSystem').value;
    let height, weight;
    
    // Get height and weight based on unit system
    if (unitSystem === 'metric') {
        height = parseFloat(document.getElementById('heightCm').value);
        weight = parseFloat(document.getElementById('weightKg').value);
        
        if (isNaN(height) || height <= 0 || height < 50 || height > 300) {
            showError('Please enter a valid height between 50-300 cm');
            return;
        }
        
        if (isNaN(weight) || weight <= 0 || weight < 20 || weight > 500) {
            showError('Please enter a valid weight between 20-500 kg');
            return;
        }
        
        // Convert height from cm to meters
        height = height / 100;
    } else {
        const heightFt = parseFloat(document.getElementById('heightFt').value);
        const heightIn = parseFloat(document.getElementById('heightIn').value);
        const weightLbs = parseFloat(document.getElementById('weightLbs').value);
        
        if (isNaN(heightFt) || heightFt <= 0 || heightFt < 3 || heightFt > 8) {
            showError('Please enter a valid height in feet (3-8)');
            return;
        }
        
        if (isNaN(heightIn) || heightIn < 0 || heightIn > 11) {
            showError('Please enter valid inches (0-11)');
            return;
        }
        
        if (isNaN(weightLbs) || weightLbs <= 0 || weightLbs < 50 || weightLbs > 1000) {
            showError('Please enter a valid weight between 50-1000 lbs');
            return;
        }
        
        // Convert to metric
        height = ((heightFt * 12) + heightIn) * 0.0254; // Convert to meters
        weight = weightLbs * 0.453592; // Convert to kg
    }
    
    // Calculate BMI
    const bmi = weight / (height * height);
    
    // Get optional fields
    const age = document.getElementById('age').value;
    const gender = document.getElementById('gender').value;
    
    // Determine BMI category and color
    let category, categoryColor, healthStatus, recommendations;
    
    if (bmi < 18.5) {
        category = 'Underweight';
        categoryColor = 'text-blue-600';
        healthStatus = 'You may be underweight';
        recommendations = [
            'Consider consulting a healthcare provider',
            'Focus on nutrient-dense foods',
            'Consider strength training exercises',
            'Monitor your health regularly'
        ];
    } else if (bmi >= 18.5 && bmi < 25) {
        category = 'Normal Weight';
        categoryColor = 'text-green-600';
        healthStatus = 'You have a healthy weight';
        recommendations = [
            'Maintain your current lifestyle',
            'Continue regular physical activity',
            'Keep eating a balanced diet',
            'Monitor your weight regularly'
        ];
    } else if (bmi >= 25 && bmi < 30) {
        category = 'Overweight';
        categoryColor = 'text-orange-600';
        healthStatus = 'You may be overweight';
        recommendations = [
            'Consider a balanced diet plan',
            'Increase physical activity',
            'Consult a healthcare provider',
            'Set realistic weight loss goals'
        ];
    } else {
        category = 'Obese';
        categoryColor = 'text-red-600';
        healthStatus = 'You may be in the obese category';
        recommendations = [
            'Consult a healthcare provider immediately',
            'Consider a structured weight loss program',
            'Focus on gradual lifestyle changes',
            'Regular medical monitoring is important'
        ];
    }
    
    // Display results
    const results = `
        <div class="space-y-6">
            <div class="text-center">
                <div class="text-4xl font-bold ${categoryColor} mb-2">${bmi.toFixed(1)}</div>
                <div class="text-xl font-semibold ${categoryColor}">${category}</div>
                <div class="text-sm text-muted-foreground mt-2">${healthStatus}</div>
            </div>
            
            <table class="w-full caption-bottom text-sm">
                <thead class="bg-gray-200">
                    <tr class="border-b transition-colors hover:bg-muted/50">
                        <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground">Measurement</th>
                        <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground">Value</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="border-b transition-colors hover:bg-muted/50">
                        <td class="p-4 align-middle">BMI</td>
                        <td class="p-4 align-middle font-semibold">${bmi.toFixed(1)}</td>
                    </tr>
                    <tr class="border-b transition-colors hover:bg-muted/50">
                        <td class="p-4 align-middle">Category</td>
                        <td class="p-4 align-middle font-semibold ${categoryColor}">${category}</td>
                    </tr>
                    <tr class="border-b transition-colors hover:bg-muted/50">
                        <td class="p-4 align-middle">Height</td>
                        <td class="p-4 align-middle">${unitSystem === 'metric' ? (height * 100).toFixed(0) + ' cm' : document.getElementById('heightFt').value + "'" + document.getElementById('heightIn').value + '"'}</td>
                    </tr>
                    <tr class="border-b transition-colors hover:bg-muted/50">
                        <td class="p-4 align-middle">Weight</td>
                        <td class="p-4 align-middle">${unitSystem === 'metric' ? weight.toFixed(1) + ' kg' : document.getElementById('weightLbs').value + ' lbs'}</td>
                    </tr>
                    ${age ? `<tr class="border-b transition-colors hover:bg-muted/50">
                        <td class="p-4 align-middle">Age</td>
                        <td class="p-4 align-middle">${age} years</td>
                    </tr>` : ''}
                    ${gender ? `<tr class="border-b transition-colors hover:bg-muted/50">
                        <td class="p-4 align-middle">Gender</td>
                        <td class="p-4 align-middle">${gender.charAt(0).toUpperCase() + gender.slice(1)}</td>
                    </tr>` : ''}
                </tbody>
            </table>
            
            <div class="bg-blue-50 p-4 rounded-lg">
                <h4 class="font-semibold text-blue-800 mb-2">Health Recommendations:</h4>
                <ul class="text-sm text-blue-700 space-y-1">
                    ${recommendations.map(rec => `<li>• ${rec}</li>`).join('')}
                </ul>
            </div>
            
            <div class="text-xs text-muted-foreground text-center">
                <p><strong>Disclaimer:</strong> This BMI calculation is for informational purposes only and should not replace professional medical advice.</p>
            </div>
        </div>
    `;
    
    document.getElementById('bmiResults').innerHTML = results;
    
    // Draw BMI chart
    drawBMIChart(bmi, category);
}

// BMI Chart Drawing Function
function drawBMIChart(userBMI, userCategory) {
    const canvas = document.getElementById('bmiCanvas');
    const ctx = canvas.getContext('2d');
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // BMI ranges and colors
    const ranges = [
        { min: 0, max: 18.5, color: '#3b82f6', label: 'Underweight' },
        { min: 18.5, max: 25, color: '#22c55e', label: 'Normal' },
        { min: 25, max: 30, color: '#f97316', label: 'Overweight' },
        { min: 30, max: 40, color: '#ef4444', label: 'Obese' }
    ];
    
    // Chart dimensions
    const chartWidth = canvas.width - 80;
    const chartHeight = 40;
    const startX = 40;
    const startY = canvas.height / 2 - 20;
    
    // Draw BMI scale
    ranges.forEach((range, index) => {
        const width = (range.max - range.min) / 40 * chartWidth;
        const x = startX + (range.min / 40 * chartWidth);
        
        ctx.fillStyle = range.color;
        ctx.fillRect(x, startY, width, chartHeight);
        
        // Add labels
        ctx.fillStyle = '#000';
        ctx.font = '10px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(range.label, x + width/2, startY + chartHeight + 15);
        ctx.fillText(range.min.toString(), x, startY + chartHeight + 30);
    });
    
    // Add final label
    ctx.fillText('40+', startX + chartWidth, startY + chartHeight + 30);
    
    // Draw user BMI indicator
    const userX = startX + (Math.min(userBMI, 40) / 40 * chartWidth);
    ctx.fillStyle = '#000';
    ctx.beginPath();
    ctx.moveTo(userX, startY - 10);
    ctx.lineTo(userX - 5, startY);
    ctx.lineTo(userX + 5, startY);
    ctx.closePath();
    ctx.fill();
    
    // Add user BMI text
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(`Your BMI: ${userBMI.toFixed(1)}`, userX, startY - 15);
    
    // Add title
    ctx.font = '14px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('BMI Scale', canvas.width / 2, 20);
}

// Error Display Function
function showError(message) {
    document.getElementById('bmiResults').innerHTML = `
        <div class="text-center py-8">
            <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                <p class="text-red-800 font-medium">${message}</p>
            </div>
        </div>
    `;
}

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeTheme();
    
    // Add event listeners for auto-calculation
    const inputs = document.querySelectorAll('input[type="number"], select');
    inputs.forEach(input => {
        input.addEventListener('input', function() {
            // Auto-calculate if all required fields are filled
            const unitSystem = document.getElementById('unitSystem').value;
            let allFilled = false;
            
            if (unitSystem === 'metric') {
                const height = document.getElementById('heightCm').value;
                const weight = document.getElementById('weightKg').value;
                allFilled = height && weight;
            } else {
                const heightFt = document.getElementById('heightFt').value;
                const heightIn = document.getElementById('heightIn').value;
                const weight = document.getElementById('weightLbs').value;
                allFilled = heightFt && heightIn !== '' && weight;
            }
            
            if (allFilled) {
                // Debounce the calculation
                clearTimeout(this.calcTimeout);
                this.calcTimeout = setTimeout(calculateBMI, 500);
            }
        });
    });
});
