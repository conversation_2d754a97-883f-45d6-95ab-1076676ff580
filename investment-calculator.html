<!DOCTYPE html>
<html lang="en" class="light" style="color-scheme: light;">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Investment Calculator - ROI & Compound Interest Calculator | Financial Calculator</title>
    <meta name="description" content="Free investment calculator to calculate returns, compound interest, and future value. Plan your investments with detailed projections and growth analysis.">
    <meta name="keywords" content="investment calculator,ROI calculator,compound interest calculator,investment return calculator,future value calculator,portfolio calculator,retirement calculator,savings calculator,wealth calculator,financial planning">
    <meta name="theme-color" media="(prefers-color-scheme: light)" content="white">
    <meta name="theme-color" media="(prefers-color-scheme: dark)" content="black">
    <meta name="creator" content="Investment Calculator">
    <link rel="canonical" href="/investment-calculator.html">
    <meta property="og:title" content="Investment Calculator - ROI & Compound Interest Calculator">
    <meta property="og:description" content="Free investment calculator to calculate returns, compound interest, and future value. Plan your investments with detailed projections and growth analysis.">
    <meta property="og:type" content="website">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Investment Calculator - ROI & Compound Interest Calculator">
    <meta name="twitter:description" content="Free investment calculator to calculate returns, compound interest, and future value. Plan your investments with detailed projections and growth analysis.">
    <link rel="stylesheet" href="styles.css">
</head>
<body class="min-h-screen bg-background font-sans antialiased">
    <div class="relative flex min-h-screen flex-col">
        <!-- PayCal Header Component -->
        <div class="bg-purple-100/90 dark:bg-purple-900/30 border-b border-purple-200 dark:border-purple-800/30 transition-colors duration-500">
            <div class="container mx-auto px-4 py-3">
                <div class="flex items-center justify-between gap-x-4">
                    <div class="flex items-center gap-x-2 flex-1">
                        <span class="hidden sm:inline">📈 New!</span>
                        <span class="text-sm sm:text-base">Calculate your <a class="font-semibold text-purple-600 dark:text-purple-400 hover:underline" href="#calculator">investment returns instantly</a><span class="hidden sm:inline"> - Plan your financial future!</span></span>
                    </div>
                    <div class="flex items-center gap-x-1 sm:gap-x-2">
                        <a class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-9 rounded-md px-3 hidden sm:inline-flex border-purple-300 dark:border-purple-700 hover:bg-purple-200/70 dark:hover:bg-purple-800/30" href="#calculator">Try Now</a>
                        <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-8 w-8 hover:bg-purple-200/70 dark:hover:bg-purple-800/30" onclick="dismissBanner()">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                                <path d="M18 6 6 18"></path>
                                <path d="m6 6 12 12"></path>
                            </svg>
                            <span class="sr-only">Dismiss</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Header -->
        <header class="supports-backdrop-blur:bg-background/60 sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur">
            <div class="container flex h-14 items-center">
                <div class="mr-4 hidden md:flex">
                    <nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex max-w-max flex-1 items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256" class="h-6 w-6 dark:text-white" aria-hidden="true">
                            <path d="M0 0h256v256H0z" fill="none"></path>
                            <rect width="256" height="256" fill="none" stroke="currentColor" stroke-width="16"></rect>
                            <rect x="32" y="32" width="192" height="64" fill="currentColor"></rect>
                            <circle cx="64" cy="128" r="16" fill="currentColor"></circle>
                            <circle cx="128" cy="128" r="16" fill="currentColor"></circle>
                            <circle cx="192" cy="128" r="16" fill="currentColor"></circle>
                            <circle cx="64" cy="176" r="16" fill="currentColor"></circle>
                            <circle cx="128" cy="176" r="16" fill="currentColor"></circle>
                            <circle cx="192" cy="176" r="16" fill="currentColor"></circle>
                            <circle cx="64" cy="224" r="16" fill="currentColor"></circle>
                            <circle cx="128" cy="224" r="16" fill="currentColor"></circle>
                            <circle cx="192" cy="224" r="16" fill="currentColor"></circle>
                        </svg>
                        <div style="position:relative">
                            <ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr">
                                <li><a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="index.html">Investment Calculator Suite</a></li>
                                <li><a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="#calculator">Investment Calculator</a></li>
                                <li><a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="#about">About Investing</a></li>
                            </ul>
                        </div>
                    </nav>
                </div>
                <div class="container flex h-16 items-center space-x-4 sm:justify-between sm:space-x-0">
                    <div class="flex flex-1 items-center justify-end space-x-4">
                        <nav class="flex items-center space-x-1">
                            <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 w-10" onclick="toggleTheme()">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-[1.5rem] w-[1.3rem] dark:hidden">
                                    <circle cx="12" cy="12" r="4"></circle>
                                    <path d="M12 2v2"></path>
                                    <path d="M12 20v2"></path>
                                    <path d="m4.93 4.93 1.41 1.41"></path>
                                    <path d="m17.66 17.66 1.41 1.41"></path>
                                    <path d="M2 12h2"></path>
                                    <path d="M20 12h2"></path>
                                    <path d="m6.34 17.66-1.41 1.41"></path>
                                    <path d="m19.07 4.93-1.41 1.41"></path>
                                </svg>
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="hidden h-5 w-5 dark:block dark:text-white">
                                    <path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"></path>
                                </svg>
                                <span class="sr-only">Toggle theme</span>
                            </button>
                        </nav>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <div class="flex-1">
            <main class="flex flex-col items-center justify-between">
                <div class="container relative">
                    <div class="flex flex-col md:flex-row md:gap-8">
                        <div class="flex-1">
                            <section class="flex flex-col items-start gap-2 px-4 pt-8 md:pt-12 pb-8">
                                <a class="group inline-flex items-center rounded-lg text-sm font-medium transition-colors mb-6 hover:text-primary" href="#investment-tips">
                                    <span class="mr-2">💰</span>Looking for investment tips? Check our Investment Guide
                                    <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" class="ml-1 h-4 w-4 group-hover:translate-x-0.5 transition-transform">
                                        <path d="M8.14645 3.14645C8.34171 2.95118 8.65829 2.95118 8.85355 3.14645L12.8536 7.14645C13.0488 7.34171 13.0488 7.65829 12.8536 7.85355L8.85355 11.8536C8.65829 12.0488 8.34171 12.0488 8.14645 11.8536C7.95118 11.6583 7.95118 11.3417 8.14645 11.1464L11.2929 8H2.5C2.22386 8 2 7.77614 2 7.5C2 7.22386 2.22386 7 2.5 7H11.2929L8.14645 3.85355C7.95118 3.65829 7.95118 3.34171 8.14645 3.14645Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path>
                                    </svg>
                                </a>
                                <h1 class="text-3xl font-bold leading-tight tracking-tighter md:text-5xl lg:leading-[1.1]">Investment Return Calculator</h1>
                                <span class="max-w-[750px] text-lg text-muted-foreground sm:text-xl">
                                    Calculate compound interest, future value, and investment returns to plan your financial goals
                                </span>
                            </section>
                        </div>
                    </div>

                    <!-- Calculator Section -->
                    <section id="calculator" class="px-4 py-8">
                        <div class="flex flex-wrap justify-center items-center w-full gap-4 flex-grow overflow-hidden rounded-[0.5rem] p-4 sm:p-8 border bg-background shadow">
                            <div class="flex flex-col md:flex-row gap-4 w-full max-w-screen-xl">
                                <!-- Calculator Input Panel -->
                                <div class="md:w-1/3 flex flex-col gap-4">
                                    <div class="rounded-lg border bg-card text-card-foreground shadow-sm m-2 flex-1">
                                        <div class="flex flex-col space-y-1.5 p-6">
                                            <h3 class="text-2xl font-semibold leading-none tracking-tight">Investment Details</h3>
                                            <p class="text-sm text-muted-foreground">Enter your investment parameters</p>
                                        </div>
                                        <div class="p-6 pt-0">
                                            <form class="grid gap-6" id="investmentForm">
                                                <!-- Initial Investment -->
                                                <div class="flex flex-col space-y-1.5">
                                                    <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="initialAmount">Initial Investment</label>
                                                    <div class="flex border h-10 w-full rounded-md border-input bg-background">
                                                        <span class="flex items-center px-3 h-10">$</span>
                                                        <input type="number" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-l-none m-[-1]" id="initialAmount" placeholder="Enter initial amount" value="10000" min="1" max="10000000">
                                                    </div>
                                                </div>

                                                <!-- Monthly Contribution -->
                                                <div class="flex flex-col space-y-1.5">
                                                    <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="monthlyContribution">Monthly Contribution</label>
                                                    <div class="flex border h-10 w-full rounded-md border-input bg-background">
                                                        <span class="flex items-center px-3 h-10">$</span>
                                                        <input type="number" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-l-none m-[-1]" id="monthlyContribution" placeholder="Monthly addition" value="500" min="0" max="100000">
                                                    </div>
                                                </div>

                                                <!-- Annual Return Rate -->
                                                <div class="flex flex-col space-y-1.5">
                                                    <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="annualReturn">Expected Annual Return (%)</label>
                                                    <input type="number" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" id="annualReturn" placeholder="Expected return rate" value="7" min="0" max="50" step="0.1">
                                                </div>

                                                <!-- Investment Period -->
                                                <div class="flex flex-col space-y-1.5">
                                                    <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="investmentPeriod">Investment Period (Years)</label>
                                                    <input type="number" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" id="investmentPeriod" placeholder="Years to invest" value="10" min="1" max="50">
                                                </div>

                                                <!-- Compounding Frequency -->
                                                <div class="flex flex-col space-y-1.5">
                                                    <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="compoundingFrequency">Compounding Frequency</label>
                                                    <select class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" id="compoundingFrequency">
                                                        <option value="1">Annually</option>
                                                        <option value="2">Semi-annually</option>
                                                        <option value="4">Quarterly</option>
                                                        <option value="12" selected>Monthly</option>
                                                        <option value="365">Daily</option>
                                                    </select>
                                                </div>

                                                <!-- Investment Type -->
                                                <div class="flex flex-col space-y-1.5">
                                                    <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70" for="investmentType">Investment Type</label>
                                                    <select class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" id="investmentType">
                                                        <option value="stocks">Stocks/Equity</option>
                                                        <option value="bonds">Bonds</option>
                                                        <option value="mutual">Mutual Funds</option>
                                                        <option value="etf">ETFs</option>
                                                        <option value="savings">Savings Account</option>
                                                        <option value="cd">Certificate of Deposit</option>
                                                        <option value="retirement">Retirement Account</option>
                                                        <option value="other">Other</option>
                                                    </select>
                                                </div>

                                                <!-- Calculate Button -->
                                                <button type="button" class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2" onclick="calculateInvestment()">
                                                    Calculate Investment
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>

                                <!-- Results Panel -->
                                <div class="md:w-2/3 flex flex-col gap-4">
                                    <div class="rounded-lg border bg-card text-card-foreground shadow-sm m-2 flex-1">
                                        <div class="flex flex-col space-y-1.5 p-6">
                                            <h3 class="text-2xl font-semibold leading-none tracking-tight">Investment Projection</h3>
                                        </div>
                                        <div class="p-6 pt-0">
                                            <div id="investmentResults" class="w-full overflow-auto">
                                                <div class="text-center text-muted-foreground py-8">
                                                    Enter your investment details and click Calculate Investment to see projections
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Growth Chart Panel -->
                                    <div class="rounded-lg border bg-card text-card-foreground shadow-sm m-2 flex-1">
                                        <div class="flex flex-col space-y-1.5 p-6">
                                            <h3 class="text-2xl font-semibold leading-none tracking-tight">Growth Projection Chart</h3>
                                        </div>
                                        <div class="p-6 pt-0">
                                            <div id="investmentChart" class="w-full h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                                                <canvas id="investmentCanvas" width="400" height="200"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- Educational Content Section -->
                    <section id="about" class="px-4 py-8">
                        <div class="rounded-lg border bg-card text-card-foreground shadow-sm m-2 w-full flex-1 flex flex-col">
                            <div class="flex flex-col space-y-1.5 p-6">
                                <h3 class="text-2xl font-semibold leading-none tracking-tight">Understanding Investment Returns</h3>
                            </div>
                            <div class="p-6 pt-0">
                                <div class="container mx-auto p-6">
                                    <div class="grid md:grid-cols-2 gap-6">
                                        <!-- Investment Image -->
                                        <div class="space-y-4">
                                            <div class="bg-gradient-to-br from-purple-100 to-pink-100 p-6 rounded-lg">
                                                <div class="w-full h-48 bg-gradient-to-r from-purple-200 to-pink-200 rounded-lg flex items-center justify-center">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="text-purple-600">
                                                        <polyline points="22,12 18,12 15,21 9,3 6,12 2,12"/>
                                                    </svg>
                                                </div>
                                                <h4 class="text-lg font-semibold mt-4 text-purple-800">Smart Investing</h4>
                                                <p class="text-purple-700 text-sm">Plan your financial future with compound interest and strategic investment planning.</p>
                                            </div>
                                        </div>

                                        <!-- Content -->
                                        <div class="space-y-4">
                                            <h4 class="text-xl font-semibold">What is Compound Interest?</h4>
                                            <p class="text-lg mb-4">Compound interest is the addition of interest to the principal sum of a loan or deposit. It's interest on interest, which can significantly accelerate the growth of your investments over time.</p>

                                            <h4 class="text-lg font-medium mt-4 mb-2">Key Investment Concepts:</h4>
                                            <ul class="list-disc pl-5 mb-4 space-y-2">
                                                <li class="mb-2"><strong>Principal:</strong> Your initial investment amount</li>
                                                <li class="mb-2"><strong>Return Rate:</strong> The percentage gain on your investment</li>
                                                <li class="mb-2"><strong>Time Horizon:</strong> How long you plan to invest</li>
                                                <li class="mb-2"><strong>Compounding:</strong> How often returns are reinvested</li>
                                            </ul>
                                        </div>
                                    </div>

                                    <!-- Investment Types Grid -->
                                    <div class="mt-8 grid md:grid-cols-3 gap-4">
                                        <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                                            <div class="flex items-center mb-3">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-green-600 mr-2">
                                                    <line x1="12" y1="1" x2="12" y2="23"/>
                                                    <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/>
                                                </svg>
                                                <h5 class="font-semibold text-green-800">Stocks & Equity</h5>
                                            </div>
                                            <p class="text-sm text-green-700">Higher potential returns with increased risk. Historical average: 7-10% annually.</p>
                                        </div>

                                        <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                                            <div class="flex items-center mb-3">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-blue-600 mr-2">
                                                    <rect width="20" height="14" x="2" y="5" rx="2"/>
                                                    <line x1="2" x2="22" y1="10" y2="10"/>
                                                </svg>
                                                <h5 class="font-semibold text-blue-800">Bonds & Fixed Income</h5>
                                            </div>
                                            <p class="text-sm text-blue-700">Lower risk with steady returns. Typical range: 2-5% annually.</p>
                                        </div>

                                        <div class="bg-purple-50 p-4 rounded-lg border border-purple-200">
                                            <div class="flex items-center mb-3">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-purple-600 mr-2">
                                                    <circle cx="8" cy="21" r="1"/>
                                                    <circle cx="19" cy="21" r="1"/>
                                                    <path d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12"/>
                                                </svg>
                                                <h5 class="font-semibold text-purple-800">Diversified Funds</h5>
                                            </div>
                                            <p class="text-sm text-purple-700">Balanced risk through diversification. ETFs and mutual funds.</p>
                                        </div>
                                    </div>

                                    <!-- Investment Tips -->
                                    <div class="mt-8 bg-yellow-50 p-6 rounded-lg border border-yellow-200" id="investment-tips">
                                        <h4 class="text-lg font-semibold text-yellow-800 mb-3">💡 Smart Investment Tips</h4>
                                        <div class="grid md:grid-cols-2 gap-4 text-sm text-yellow-700">
                                            <div>
                                                <p class="mb-2"><strong>Start Early:</strong> Time is your greatest asset in building wealth through compounding.</p>
                                                <p class="mb-2"><strong>Diversify:</strong> Don't put all your eggs in one basket - spread risk across asset classes.</p>
                                            </div>
                                            <div>
                                                <p class="mb-2"><strong>Regular Contributions:</strong> Consistent investing can smooth out market volatility.</p>
                                                <p class="mb-2"><strong>Long-term Focus:</strong> Stay invested through market ups and downs for best results.</p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Important Note -->
                                    <div class="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                                        <p class="text-sm text-red-800">
                                            <strong>Important:</strong> This calculator provides estimates based on your inputs. Past performance doesn't guarantee future results.
                                            Investment returns can vary significantly due to market conditions, fees, taxes, and other factors. Always consult with financial advisors before making investment decisions.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
            </main>
        </div>
    </div>

    <script src="investment-calculator.js"></script>
</body>
</html>
