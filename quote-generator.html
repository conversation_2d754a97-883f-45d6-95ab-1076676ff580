<!DOCTYPE html>
<html lang="en" class="light" style="color-scheme: light;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free Quote Generator | Business Quote Template | PayCal Australia</title>
    <meta name="description" content="Create professional business quotes for your Australian business. Include GST calculations, terms and conditions, and professional formatting. Download as PDF instantly.">
    <meta name="keywords" content="quote generator,business quote,quote template,professional quote,australian business quote,gst quote,quote maker,free quote generator">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        border: "hsl(var(--border))",
                        input: "hsl(var(--input))",
                        ring: "hsl(var(--ring))",
                        background: "hsl(var(--background))",
                        foreground: "hsl(var(--foreground))",
                        primary: {
                            DEFAULT: "hsl(var(--primary))",
                            foreground: "hsl(var(--primary-foreground))",
                        },
                        secondary: {
                            DEFAULT: "hsl(var(--secondary))",
                            foreground: "hsl(var(--secondary-foreground))",
                        },
                        muted: {
                            DEFAULT: "hsl(var(--muted))",
                            foreground: "hsl(var(--muted-foreground))",
                        },
                    },
                }
            }
        }
    </script>
    <style>
        :root {
            --background: 0 0% 100%;
            --foreground: 222.2 84% 4.9%;
            --card: 0 0% 100%;
            --card-foreground: 222.2 84% 4.9%;
            --primary: 222.2 47.4% 11.2%;
            --primary-foreground: 210 40% 98%;
            --secondary: 210 40% 96%;
            --secondary-foreground: 222.2 84% 4.9%;
            --muted: 210 40% 96%;
            --muted-foreground: 215.4 16.3% 46.9%;
            --border: 214.3 31.8% 91.4%;
            --input: 214.3 31.8% 91.4%;
            --ring: 222.2 84% 4.9%;
            --radius: 0.5rem;
        }

        * {
            border-color: hsl(var(--border));
        }

        body {
            background-color: hsl(var(--background));
            color: hsl(var(--foreground));
        }

        .calculator-container {
            background: hsl(var(--card));
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
        }

        .input-group {
            margin-bottom: 1rem;
        }

        .input-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: hsl(var(--foreground));
        }

        .input-group input, .input-group select, .input-group textarea {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
            background: hsl(var(--background));
            color: hsl(var(--foreground));
        }

        .input-group input:focus, .input-group select:focus, .input-group textarea:focus {
            outline: none;
            border-color: hsl(var(--ring));
            box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
        }

        .btn-primary {
            background: hsl(var(--primary));
            color: hsl(var(--primary-foreground));
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--radius);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-secondary {
            background: hsl(var(--secondary));
            color: hsl(var(--secondary-foreground));
            padding: 0.75rem 1.5rem;
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .quote-preview {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: var(--radius);
            padding: 2rem;
            font-family: 'Arial', sans-serif;
            color: #000;
        }

        .quote-header {
            display: flex;
            justify-content: space-between;
            align-items: start;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e5e7eb;
        }

        .quote-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1.5rem 0;
        }

        .quote-table th,
        .quote-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }

        .quote-table th {
            background: #f9fafb;
            font-weight: 600;
        }

        .quote-total {
            margin-top: 1rem;
            text-align: right;
        }

        .total-row {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            margin-left: auto;
            width: 300px;
        }

        .total-row.final {
            border-top: 2px solid #000;
            font-weight: bold;
            font-size: 1.1rem;
        }

        @media (max-width: 768px) {
            .calculator-container {
                padding: 1rem;
            }
            .quote-preview {
                padding: 1rem;
            }
            .quote-header {
                flex-direction: column;
                gap: 1rem;
            }
            .total-row {
                width: 100%;
            }
        }
    </style>
</head>

<body class="min-h-screen bg-background font-sans antialiased">
    <div class="relative flex min-h-screen flex-col">
        <!-- PayCal Australia Header -->
        <div class="bg-orange-100/90 border-b border-orange-200 transition-colors duration-500">
            <div class="container mx-auto px-4 py-3">
                <div class="flex items-center justify-between gap-x-4">
                    <div class="flex items-center gap-x-2 flex-1">
                        <span class="hidden sm:inline">📋 Free!</span>
                        <span class="text-sm sm:text-base">Create professional <a class="font-semibold text-orange-600 hover:underline" href="#generator">business quotes</a><span class="hidden sm:inline"> - Download instantly!</span></span>
                    </div>
                    <div class="flex items-center gap-x-1 sm:gap-x-2">
                        <a class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-9 rounded-md px-3 hidden sm:inline-flex border-orange-300 hover:bg-orange-200/70" href="#generator">Create Quote</a>
                        <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-8 w-8 hover:bg-orange-200/70" onclick="dismissBanner()">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                                <path d="M18 6 6 18"></path>
                                <path d="m6 6 12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <main class="flex-1">
            <div class="container mx-auto px-4 py-8">
                <!-- Hero Section -->
                <div class="text-center mb-8">
                    <h1 class="text-4xl font-bold tracking-tight text-foreground mb-4">
                        Free Quote Generator
                    </h1>
                    <p class="text-xl text-muted-foreground max-w-2xl mx-auto">
                        Create professional business quotes for your Australian business. Include GST calculations, terms and conditions, and professional formatting.
                    </p>
                </div>

                <!-- Quote Generator -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-7xl mx-auto" id="generator">
                    <!-- Input Form -->
                    <div class="calculator-container p-6">
                        <h2 class="text-2xl font-semibold mb-4">Quote Details</h2>
                        
                        <form class="grid gap-4" id="quote-form">
                            <!-- Business Details -->
                            <div class="border-b pb-4">
                                <h3 class="text-lg font-semibold mb-3">Your Business</h3>
                                <div class="grid gap-3">
                                    <div class="input-group">
                                        <label for="businessName">Business Name</label>
                                        <input type="text" id="businessName" placeholder="Your Business Name" value="ABC Services Pty Ltd" oninput="updateQuote()">
                                    </div>
                                    <div class="input-group">
                                        <label for="abn">ABN</label>
                                        <input type="text" id="abn" placeholder="**************" value="**************" oninput="updateQuote()">
                                    </div>
                                    <div class="input-group">
                                        <label for="businessAddress">Address</label>
                                        <textarea id="businessAddress" rows="2" placeholder="Business address" oninput="updateQuote()">123 Business St
Sydney NSW 2000</textarea>
                                    </div>
                                    <div class="input-group">
                                        <label for="businessContact">Contact Details</label>
                                        <input type="text" id="businessContact" placeholder="Phone: (02) 1234 5678 | Email: <EMAIL>" value="Phone: (02) 1234 5678 | Email: <EMAIL>" oninput="updateQuote()">
                                    </div>
                                </div>
                            </div>

                            <!-- Client Details -->
                            <div class="border-b pb-4">
                                <h3 class="text-lg font-semibold mb-3">Quote For</h3>
                                <div class="grid gap-3">
                                    <div class="input-group">
                                        <label for="clientName">Client Name</label>
                                        <input type="text" id="clientName" placeholder="Client Name" value="XYZ Corporation" oninput="updateQuote()">
                                    </div>
                                    <div class="input-group">
                                        <label for="clientAddress">Client Address</label>
                                        <textarea id="clientAddress" rows="2" placeholder="Client address" oninput="updateQuote()">456 Client Ave
Melbourne VIC 3000</textarea>
                                    </div>
                                </div>
                            </div>

                            <!-- Quote Details -->
                            <div class="border-b pb-4">
                                <h3 class="text-lg font-semibold mb-3">Quote Info</h3>
                                <div class="grid grid-cols-2 gap-3">
                                    <div class="input-group">
                                        <label for="quoteNumber">Quote #</label>
                                        <input type="text" id="quoteNumber" placeholder="Q-001" value="Q-001" oninput="updateQuote()">
                                    </div>
                                    <div class="input-group">
                                        <label for="quoteDate">Date</label>
                                        <input type="date" id="quoteDate" oninput="updateQuote()">
                                    </div>
                                    <div class="input-group">
                                        <label for="validUntil">Valid Until</label>
                                        <input type="date" id="validUntil" oninput="updateQuote()">
                                    </div>
                                    <div class="input-group">
                                        <label for="gstRegistered">GST Registered</label>
                                        <select id="gstRegistered" onchange="updateQuote()">
                                            <option value="yes">Yes</option>
                                            <option value="no">No</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Line Items -->
                            <div>
                                <h3 class="text-lg font-semibold mb-3">Items/Services</h3>
                                <div id="line-items">
                                    <div class="line-item grid grid-cols-12 gap-2 mb-2">
                                        <div class="col-span-6">
                                            <input type="text" placeholder="Description" value="Website Design & Development" class="item-description" oninput="updateQuote()">
                                        </div>
                                        <div class="col-span-2">
                                            <input type="number" placeholder="Qty" value="1" class="item-quantity" oninput="updateQuote()">
                                        </div>
                                        <div class="col-span-2">
                                            <input type="number" placeholder="Rate" value="2500" class="item-rate" oninput="updateQuote()">
                                        </div>
                                        <div class="col-span-2">
                                            <input type="text" placeholder="Amount" class="item-amount" readonly>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" class="btn-secondary mt-2" onclick="addQuoteItem()">Add Item</button>
                            </div>

                            <!-- Actions -->
                            <div class="flex gap-2 pt-4">
                                <button type="button" class="btn-primary" onclick="downloadQuotePDF()">Download PDF</button>
                                <button type="button" class="btn-secondary" onclick="printQuote()">Print</button>
                            </div>
                        </form>
                    </div>

                    <!-- Quote Preview -->
                    <div class="calculator-container p-6">
                        <h2 class="text-2xl font-semibold mb-4">Quote Preview</h2>
                        
                        <div class="quote-preview" id="quote-preview">
                            <div class="quote-header">
                                <div>
                                    <h1 class="text-2xl font-bold" id="preview-business-name">ABC Services Pty Ltd</h1>
                                    <p class="text-sm text-gray-600">ABN: <span id="preview-abn">**************</span></p>
                                    <div class="text-sm text-gray-600 mt-2" id="preview-business-address">
                                        123 Business St<br>
                                        Sydney NSW 2000
                                    </div>
                                    <div class="text-sm text-gray-600 mt-1" id="preview-business-contact">
                                        Phone: (02) 1234 5678 | Email: <EMAIL>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <h2 class="text-xl font-bold">QUOTE</h2>
                                    <p class="text-sm">Quote #: <span id="preview-quote-number">Q-001</span></p>
                                    <p class="text-sm">Date: <span id="preview-quote-date"></span></p>
                                    <p class="text-sm">Valid Until: <span id="preview-valid-until"></span></p>
                                </div>
                            </div>

                            <div class="mb-4">
                                <h3 class="font-semibold mb-2">Quote For:</h3>
                                <div id="preview-client-name" class="font-medium">XYZ Corporation</div>
                                <div id="preview-client-address" class="text-sm text-gray-600">
                                    456 Client Ave<br>
                                    Melbourne VIC 3000
                                </div>
                            </div>

                            <table class="quote-table">
                                <thead>
                                    <tr>
                                        <th>Description</th>
                                        <th>Qty</th>
                                        <th>Rate</th>
                                        <th>Amount</th>
                                    </tr>
                                </thead>
                                <tbody id="preview-quote-items">
                                    <tr>
                                        <td>Website Design & Development</td>
                                        <td>1</td>
                                        <td>$2,500.00</td>
                                        <td>$2,500.00</td>
                                    </tr>
                                </tbody>
                            </table>

                            <div class="quote-total">
                                <div class="total-row">
                                    <span>Subtotal:</span>
                                    <span id="preview-quote-subtotal">$2,500.00</span>
                                </div>
                                <div class="total-row" id="quote-gst-row">
                                    <span>GST (10%):</span>
                                    <span id="preview-quote-gst">$250.00</span>
                                </div>
                                <div class="total-row final">
                                    <span>Total:</span>
                                    <span id="preview-quote-total">$2,750.00</span>
                                </div>
                            </div>

                            <div class="mt-6 text-sm text-gray-600">
                                <p><strong>Terms & Conditions:</strong></p>
                                <ul class="mt-2 space-y-1">
                                    <li>• Quote valid for 30 days from date of issue</li>
                                    <li>• 50% deposit required to commence work</li>
                                    <li>• Final payment due upon completion</li>
                                    <li>• Prices include GST where applicable</li>
                                </ul>
                                <p class="mt-4"><strong>Thank you for considering our services!</strong></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="border-t bg-muted/50">
            <div class="container mx-auto px-4 py-8">
                <div class="text-center">
                    <p class="text-sm text-muted-foreground">
                        © 2024 PayCal.com.au. Made for Aussies ❤️
                    </p>
                </div>
            </div>
        </footer>
    </div>

    <script>
        // Initialize dates
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date();
            const validUntil = new Date(today);
            validUntil.setDate(today.getDate() + 30);

            document.getElementById('quoteDate').value = today.toISOString().split('T')[0];
            document.getElementById('validUntil').value = validUntil.toISOString().split('T')[0];

            updateQuote();
        });

        // Add quote item
        function addQuoteItem() {
            const lineItems = document.getElementById('line-items');
            const newItem = document.createElement('div');
            newItem.className = 'line-item grid grid-cols-12 gap-2 mb-2';
            newItem.innerHTML = `
                <div class="col-span-6">
                    <input type="text" placeholder="Description" class="item-description" oninput="updateQuote()">
                </div>
                <div class="col-span-2">
                    <input type="number" placeholder="Qty" value="1" class="item-quantity" oninput="updateQuote()">
                </div>
                <div class="col-span-2">
                    <input type="number" placeholder="Rate" value="0" class="item-rate" oninput="updateQuote()">
                </div>
                <div class="col-span-2">
                    <input type="text" placeholder="Amount" class="item-amount" readonly>
                </div>
            `;
            lineItems.appendChild(newItem);
            updateQuote();
        }

        // Update quote preview
        function updateQuote() {
            // Update business details
            document.getElementById('preview-business-name').textContent = document.getElementById('businessName').value;
            document.getElementById('preview-abn').textContent = document.getElementById('abn').value;
            document.getElementById('preview-business-address').innerHTML = document.getElementById('businessAddress').value.replace(/\n/g, '<br>');
            document.getElementById('preview-business-contact').textContent = document.getElementById('businessContact').value;

            // Update client details
            document.getElementById('preview-client-name').textContent = document.getElementById('clientName').value;
            document.getElementById('preview-client-address').innerHTML = document.getElementById('clientAddress').value.replace(/\n/g, '<br>');

            // Update quote details
            document.getElementById('preview-quote-number').textContent = document.getElementById('quoteNumber').value;
            document.getElementById('preview-quote-date').textContent = formatDate(document.getElementById('quoteDate').value);
            document.getElementById('preview-valid-until').textContent = formatDate(document.getElementById('validUntil').value);

            // Update line items and calculate totals
            const lineItems = document.querySelectorAll('.line-item');
            const previewItems = document.getElementById('preview-quote-items');
            previewItems.innerHTML = '';

            let subtotal = 0;

            lineItems.forEach(item => {
                const description = item.querySelector('.item-description').value;
                const quantity = parseFloat(item.querySelector('.item-quantity').value) || 0;
                const rate = parseFloat(item.querySelector('.item-rate').value) || 0;
                const amount = quantity * rate;

                // Update amount field
                item.querySelector('.item-amount').value = formatCurrency(amount);

                if (description) {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${description}</td>
                        <td>${quantity}</td>
                        <td>${formatCurrency(rate)}</td>
                        <td>${formatCurrency(amount)}</td>
                    `;
                    previewItems.appendChild(row);
                    subtotal += amount;
                }
            });

            // Calculate GST and total
            const gstRegistered = document.getElementById('gstRegistered').value === 'yes';
            const gst = gstRegistered ? subtotal * 0.1 : 0;
            const total = subtotal + gst;

            // Update totals
            document.getElementById('preview-quote-subtotal').textContent = formatCurrency(subtotal);
            document.getElementById('preview-quote-gst').textContent = formatCurrency(gst);
            document.getElementById('preview-quote-total').textContent = formatCurrency(total);

            // Show/hide GST row
            document.getElementById('quote-gst-row').style.display = gstRegistered ? 'flex' : 'none';
        }

        // Format currency
        function formatCurrency(amount) {
            return new Intl.NumberFormat('en-AU', {
                style: 'currency',
                currency: 'AUD',
                minimumFractionDigits: 2
            }).format(amount);
        }

        // Format date
        function formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('en-AU');
        }

        // Print quote
        function printQuote() {
            window.print();
        }

        // Download PDF (simplified - would need a PDF library in real implementation)
        function downloadQuotePDF() {
            alert('PDF download feature would be implemented with a PDF library like jsPDF or similar.');
        }

        // Dismiss banner
        function dismissBanner() {
            const banner = document.querySelector('.bg-orange-100\\/90');
            if (banner) banner.style.display = 'none';
        }
    </script>
