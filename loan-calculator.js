// Loan Calculator JavaScript

// Theme Toggle Functionality
function toggleTheme() {
    const html = document.documentElement;
    const currentTheme = html.classList.contains('dark') ? 'dark' : 'light';
    
    if (currentTheme === 'light') {
        html.classList.remove('light');
        html.classList.add('dark');
        html.style.colorScheme = 'dark';
        localStorage.setItem('theme', 'dark');
    } else {
        html.classList.remove('dark');
        html.classList.add('light');
        html.style.colorScheme = 'light';
        localStorage.setItem('theme', 'light');
    }
}

// Initialize theme from localStorage
function initializeTheme() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    const html = document.documentElement;
    
    html.classList.remove('light', 'dark');
    html.classList.add(savedTheme);
    html.style.colorScheme = savedTheme;
}

// Loan Calculation Function
function calculateLoan() {
    const loanAmount = parseFloat(document.getElementById('loanAmount').value);
    const annualRate = parseFloat(document.getElementById('interestRate').value) / 100;
    const loanTermValue = parseFloat(document.getElementById('loanTerm').value);
    const termUnit = document.getElementById('termUnit').value;
    const paymentFrequency = document.getElementById('paymentFrequency').value;
    const loanType = document.getElementById('loanType').value;
    
    // Validation
    if (isNaN(loanAmount) || loanAmount <= 0) {
        showError('Please enter a valid loan amount');
        return;
    }
    
    if (isNaN(annualRate) || annualRate < 0) {
        showError('Please enter a valid interest rate');
        return;
    }
    
    if (isNaN(loanTermValue) || loanTermValue <= 0) {
        showError('Please enter a valid loan term');
        return;
    }
    
    // Convert loan term to months
    let loanTermMonths;
    if (termUnit === 'years') {
        loanTermMonths = loanTermValue * 12;
    } else {
        loanTermMonths = loanTermValue;
    }
    
    // Determine payment frequency
    let paymentsPerYear;
    let frequencyText;
    
    switch (paymentFrequency) {
        case 'weekly':
            paymentsPerYear = 52;
            frequencyText = 'Weekly';
            break;
        case 'fortnightly':
            paymentsPerYear = 26;
            frequencyText = 'Fortnightly';
            break;
        case 'monthly':
        default:
            paymentsPerYear = 12;
            frequencyText = 'Monthly';
            break;
    }
    
    // Calculate payment details
    const periodicRate = annualRate / paymentsPerYear;
    const totalPayments = (loanTermMonths / 12) * paymentsPerYear;
    
    let paymentAmount;
    if (periodicRate === 0) {
        // No interest case
        paymentAmount = loanAmount / totalPayments;
    } else {
        // Standard loan payment formula
        paymentAmount = loanAmount * (periodicRate * Math.pow(1 + periodicRate, totalPayments)) / 
                      (Math.pow(1 + periodicRate, totalPayments) - 1);
    }
    
    const totalPaid = paymentAmount * totalPayments;
    const totalInterest = totalPaid - loanAmount;
    
    // Calculate loan type specific information
    let loanTypeInfo = getLoanTypeInfo(loanType);
    
    // Display results
    const results = `
        <div class="space-y-6">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div class="bg-blue-50 p-4 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600">$${paymentAmount.toFixed(2)}</div>
                    <div class="text-sm text-blue-800">${frequencyText} Payment</div>
                </div>
                <div class="bg-green-50 p-4 rounded-lg">
                    <div class="text-2xl font-bold text-green-600">$${totalPaid.toFixed(2)}</div>
                    <div class="text-sm text-green-800">Total Paid</div>
                </div>
                <div class="bg-orange-50 p-4 rounded-lg">
                    <div class="text-2xl font-bold text-orange-600">$${totalInterest.toFixed(2)}</div>
                    <div class="text-sm text-orange-800">Total Interest</div>
                </div>
                <div class="bg-purple-50 p-4 rounded-lg">
                    <div class="text-2xl font-bold text-purple-600">${(totalInterest / loanAmount * 100).toFixed(1)}%</div>
                    <div class="text-sm text-purple-800">Interest Ratio</div>
                </div>
            </div>
            
            <table class="w-full caption-bottom text-sm">
                <thead class="bg-gray-200">
                    <tr class="border-b transition-colors hover:bg-muted/50">
                        <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground">Description</th>
                        <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="border-b transition-colors hover:bg-muted/50">
                        <td class="p-4 align-middle">Loan Amount</td>
                        <td class="p-4 align-middle">$${loanAmount.toLocaleString()}</td>
                    </tr>
                    <tr class="border-b transition-colors hover:bg-muted/50">
                        <td class="p-4 align-middle">Interest Rate</td>
                        <td class="p-4 align-middle">${(annualRate * 100).toFixed(2)}% per annum</td>
                    </tr>
                    <tr class="border-b transition-colors hover:bg-muted/50">
                        <td class="p-4 align-middle">Loan Term</td>
                        <td class="p-4 align-middle">${loanTermValue} ${termUnit}</td>
                    </tr>
                    <tr class="border-b transition-colors hover:bg-muted/50">
                        <td class="p-4 align-middle">Payment Frequency</td>
                        <td class="p-4 align-middle">${frequencyText}</td>
                    </tr>
                    <tr class="border-b transition-colors hover:bg-muted/50">
                        <td class="p-4 align-middle">Loan Type</td>
                        <td class="p-4 align-middle">${loanTypeInfo.name}</td>
                    </tr>
                    <tr class="border-b transition-colors hover:bg-muted/50 font-bold">
                        <td class="p-4 align-middle">${frequencyText} Payment</td>
                        <td class="p-4 align-middle">$${paymentAmount.toFixed(2)}</td>
                    </tr>
                    <tr class="border-b transition-colors hover:bg-muted/50">
                        <td class="p-4 align-middle">Total Payments</td>
                        <td class="p-4 align-middle">${totalPayments.toFixed(0)} payments</td>
                    </tr>
                    <tr class="border-b transition-colors hover:bg-muted/50">
                        <td class="p-4 align-middle">Total Amount Paid</td>
                        <td class="p-4 align-middle">$${totalPaid.toFixed(2)}</td>
                    </tr>
                    <tr class="border-b transition-colors hover:bg-muted/50">
                        <td class="p-4 align-middle">Total Interest Paid</td>
                        <td class="p-4 align-middle">$${totalInterest.toFixed(2)}</td>
                    </tr>
                </tbody>
            </table>
            
            <div class="bg-blue-50 p-4 rounded-lg">
                <h4 class="font-semibold text-blue-800 mb-2">${loanTypeInfo.name} Information:</h4>
                <p class="text-sm text-blue-700">${loanTypeInfo.description}</p>
                <ul class="text-sm text-blue-700 mt-2 space-y-1">
                    ${loanTypeInfo.features.map(feature => `<li>• ${feature}</li>`).join('')}
                </ul>
            </div>
            
            <div class="text-xs text-muted-foreground text-center">
                <p><strong>Note:</strong> This calculation is for estimation purposes only. Actual loan terms may vary based on creditworthiness and lender policies.</p>
            </div>
        </div>
    `;
    
    document.getElementById('loanResults').innerHTML = results;
    
    // Draw loan breakdown chart
    drawLoanChart(loanAmount, totalInterest, paymentAmount, frequencyText);
}

// Get loan type specific information
function getLoanTypeInfo(loanType) {
    const loanTypes = {
        personal: {
            name: 'Personal Loan',
            description: 'Unsecured loans for personal expenses, debt consolidation, or major purchases.',
            features: [
                'No collateral required',
                'Fixed interest rates available',
                'Flexible use of funds',
                'Typically 2-7 year terms'
            ]
        },
        auto: {
            name: 'Auto Loan',
            description: 'Secured loans specifically for purchasing vehicles with competitive rates.',
            features: [
                'Vehicle serves as collateral',
                'Lower interest rates than personal loans',
                'Terms typically 3-7 years',
                'New and used vehicle financing'
            ]
        },
        student: {
            name: 'Student Loan',
            description: 'Education financing with flexible repayment options and potential benefits.',
            features: [
                'Deferred payment options',
                'Potential tax benefits',
                'Income-driven repayment plans',
                'Forgiveness programs available'
            ]
        },
        business: {
            name: 'Business Loan',
            description: 'Financing for business operations, equipment, or expansion.',
            features: [
                'Business credit building',
                'Tax-deductible interest',
                'Various collateral options',
                'Flexible repayment terms'
            ]
        },
        other: {
            name: 'Other Loan',
            description: 'Various other loan types with different terms and conditions.',
            features: [
                'Terms vary by lender',
                'Purpose-specific options',
                'Secured or unsecured',
                'Competitive rates available'
            ]
        }
    };
    
    return loanTypes[loanType] || loanTypes.other;
}

// Loan Chart Drawing Function
function drawLoanChart(principal, interest, payment, frequency) {
    const canvas = document.getElementById('loanCanvas');
    const ctx = canvas.getContext('2d');
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Chart dimensions
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const radius = 80;
    
    // Calculate angles
    const total = principal + interest;
    const principalAngle = (principal / total) * 2 * Math.PI;
    const interestAngle = (interest / total) * 2 * Math.PI;
    
    // Draw pie chart
    let currentAngle = -Math.PI / 2;
    
    // Principal slice (blue)
    ctx.beginPath();
    ctx.moveTo(centerX, centerY);
    ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + principalAngle);
    ctx.closePath();
    ctx.fillStyle = '#3b82f6';
    ctx.fill();
    
    currentAngle += principalAngle;
    
    // Interest slice (orange)
    ctx.beginPath();
    ctx.moveTo(centerX, centerY);
    ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + interestAngle);
    ctx.closePath();
    ctx.fillStyle = '#f97316';
    ctx.fill();
    
    // Add labels
    ctx.fillStyle = '#000';
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('Loan Cost Breakdown', centerX, 20);
    
    // Legend
    ctx.fillStyle = '#3b82f6';
    ctx.fillRect(20, canvas.height - 40, 15, 15);
    ctx.fillStyle = '#000';
    ctx.font = '10px Arial';
    ctx.textAlign = 'left';
    ctx.fillText(`Principal: $${(principal/1000).toFixed(0)}k`, 40, canvas.height - 28);
    
    ctx.fillStyle = '#f97316';
    ctx.fillRect(20, canvas.height - 20, 15, 15);
    ctx.fillStyle = '#000';
    ctx.fillText(`Interest: $${(interest/1000).toFixed(0)}k`, 40, canvas.height - 8);
    
    // Payment info
    ctx.fillStyle = '#000';
    ctx.font = '10px Arial';
    ctx.textAlign = 'right';
    ctx.fillText(`${frequency} Payment: $${payment.toFixed(0)}`, canvas.width - 20, canvas.height - 28);
}

// Error Display Function
function showError(message) {
    document.getElementById('loanResults').innerHTML = `
        <div class="text-center py-8">
            <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                <p class="text-red-800 font-medium">${message}</p>
            </div>
        </div>
    `;
}

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeTheme();
    
    // Add event listeners for auto-calculation
    const inputs = document.querySelectorAll('input[type="number"], select');
    inputs.forEach(input => {
        input.addEventListener('input', function() {
            // Auto-calculate if all required fields are filled
            const loanAmount = document.getElementById('loanAmount').value;
            const interestRate = document.getElementById('interestRate').value;
            const loanTerm = document.getElementById('loanTerm').value;
            
            if (loanAmount && interestRate && loanTerm) {
                // Debounce the calculation
                clearTimeout(this.calcTimeout);
                this.calcTimeout = setTimeout(calculateLoan, 500);
            }
        });
    });
});
