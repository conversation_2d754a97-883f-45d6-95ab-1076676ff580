<!DOCTYPE html>
<html lang="en" class="light" style="color-scheme: light;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mortgage Payoff Calculator - Pay Off Your Home Loan Faster | Australia | PayCal Australia</title>
    <meta name="description" content="Free Australian mortgage payoff calculator to see how extra payments can help you pay off your home loan faster. Calculate time and interest savings with additional payments, refinancing, and payment frequency changes.">
    <meta name="keywords" content="mortgage payoff calculator,pay off mortgage faster,extra mortgage payments,home loan payoff calculator,mortgage early payoff,australian mortgage payoff,home loan extra payments,mortgage acceleration calculator,pay off home loan early,mortgage payoff time calculator,home loan savings calculator,mortgage interest savings">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        border: "hsl(var(--border))",
                        input: "hsl(var(--input))",
                        ring: "hsl(var(--ring))",
                        background: "hsl(var(--background))",
                        foreground: "hsl(var(--foreground))",
                        primary: {
                            DEFAULT: "hsl(var(--primary))",
                            foreground: "hsl(var(--primary-foreground))",
                        },
                        secondary: {
                            DEFAULT: "hsl(var(--secondary))",
                            foreground: "hsl(var(--secondary-foreground))",
                        },
                        destructive: {
                            DEFAULT: "hsl(var(--destructive))",
                            foreground: "hsl(var(--destructive-foreground))",
                        },
                        muted: {
                            DEFAULT: "hsl(var(--muted))",
                            foreground: "hsl(var(--muted-foreground))",
                        },
                        accent: {
                            DEFAULT: "hsl(var(--accent))",
                            foreground: "hsl(var(--accent-foreground))",
                        },
                        popover: {
                            DEFAULT: "hsl(var(--popover))",
                            foreground: "hsl(var(--popover-foreground))",
                        },
                        card: {
                            DEFAULT: "hsl(var(--card))",
                            foreground: "hsl(var(--card-foreground))",
                        },
                    },
                }
            }
        }
    </script>
    <style>
        :root {
            --background: 0 0% 100%;
            --foreground: 222.2 84% 4.9%;
            --card: 0 0% 100%;
            --card-foreground: 222.2 84% 4.9%;
            --popover: 0 0% 100%;
            --popover-foreground: 222.2 84% 4.9%;
            --primary: 222.2 47.4% 11.2%;
            --primary-foreground: 210 40% 98%;
            --secondary: 210 40% 96%;
            --secondary-foreground: 222.2 84% 4.9%;
            --muted: 210 40% 96%;
            --muted-foreground: 215.4 16.3% 46.9%;
            --accent: 210 40% 96%;
            --accent-foreground: 222.2 84% 4.9%;
            --destructive: 0 84.2% 60.2%;
            --destructive-foreground: 210 40% 98%;
            --border: 214.3 31.8% 91.4%;
            --input: 214.3 31.8% 91.4%;
            --ring: 222.2 84% 4.9%;
            --radius: 0.5rem;
        }

        .dark {
            --background: 222.2 84% 4.9%;
            --foreground: 210 40% 98%;
            --card: 222.2 84% 4.9%;
            --card-foreground: 210 40% 98%;
            --popover: 222.2 84% 4.9%;
            --popover-foreground: 210 40% 98%;
            --primary: 210 40% 98%;
            --primary-foreground: 222.2 47.4% 11.2%;
            --secondary: 217.2 32.6% 17.5%;
            --secondary-foreground: 210 40% 98%;
            --muted: 217.2 32.6% 17.5%;
            --muted-foreground: 215 20.2% 65.1%;
            --accent: 217.2 32.6% 17.5%;
            --accent-foreground: 210 40% 98%;
            --destructive: 0 62.8% 30.6%;
            --destructive-foreground: 210 40% 98%;
            --border: 217.2 32.6% 17.5%;
            --input: 217.2 32.6% 17.5%;
            --ring: 212.7 26.8% 83.9%;
        }

        * {
            border-color: hsl(var(--border));
        }

        body {
            background-color: hsl(var(--background));
            color: hsl(var(--foreground));
        }

        .calculator-container {
            background: hsl(var(--card));
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
        }

        .input-group {
            margin-bottom: 1rem;
        }

        .input-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: hsl(var(--foreground));
        }

        .input-group input, .input-group select {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
            background: hsl(var(--background));
            color: hsl(var(--foreground));
        }

        .input-group input:focus, .input-group select:focus {
            outline: none;
            border-color: hsl(var(--ring));
            box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
        }

        .btn-primary {
            background: hsl(var(--primary));
            color: hsl(var(--primary-foreground));
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--radius);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary:hover {
            background: hsl(var(--primary) / 0.9);
        }

        .results-container {
            background: hsl(var(--muted));
            padding: 1.5rem;
            border-radius: var(--radius);
            margin-top: 1rem;
        }

        .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid hsl(var(--border));
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .result-label {
            font-weight: 500;
            color: hsl(var(--foreground));
        }

        .result-value {
            font-weight: 600;
            color: hsl(var(--primary));
            font-size: 1.1rem;
        }

        .savings-highlight {
            color: #16a34a;
            font-weight: 700;
        }

        @media (max-width: 768px) {
            .calculator-container {
                padding: 1rem;
            }
            .input-group {
                flex-direction: column;
            }
        }
    </style>
</head>

<body class="min-h-screen bg-background font-sans antialiased">
    <div class="relative flex min-h-screen flex-col">
        <!-- PayCal Australia Header Component -->
        <div class="bg-green-100/90 dark:bg-green-900/30 border-b border-green-200 dark:border-green-800/30 transition-colors duration-500">
            <div class="container mx-auto px-4 py-3">
                <div class="flex items-center justify-between gap-x-4">
                    <div class="flex items-center gap-x-2 flex-1">
                        <span class="hidden sm:inline">🏠 New!</span>
                        <span class="text-sm sm:text-base">Try our free <a class="font-semibold text-green-600 dark:text-green-400 hover:underline" href="mortgage-calculator.html">Mortgage Calculator</a><span class="hidden sm:inline"> - Calculate your home loan repayments!</span></span>
                    </div>
                    <div class="flex items-center gap-x-1 sm:gap-x-2">
                        <div class="hidden sm:flex items-center gap-x-1 mr-2">
                            <button class="w-2 h-2 rounded-full transition-colors bg-green-600 dark:text-green-400" aria-label="Show promotion 1"></button>
                            <button class="w-2 h-2 rounded-full transition-colors bg-muted-foreground/30 hover:bg-green-600/50" aria-label="Show promotion 2"></button>
                            <button class="w-2 h-2 rounded-full transition-colors bg-muted-foreground/30 hover:bg-green-600/50" aria-label="Show promotion 3"></button>
                        </div>
                        <a class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-9 rounded-md px-3 hidden sm:inline-flex border-green-300 dark:border-green-700 hover:bg-green-200/70 dark:hover:bg-green-800/30" href="mortgage-calculator.html">Try Now</a>
                        <a class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-8 w-8 sm:hidden border-green-300 dark:border-green-700 hover:bg-green-200/70 dark:hover:bg-green-800/30" href="mortgage-calculator.html">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                                <path d="M5 12h14"></path>
                                <path d="m12 5 7 7-7 7"></path>
                            </svg>
                            <span class="sr-only">Try Mortgage Calculator</span>
                        </a>
                        <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-8 w-8 hover:bg-green-200/70 dark:hover:bg-green-800/30" onclick="dismissBanner()">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                                <path d="M18 6 6 18"></path>
                                <path d="m6 6 12 12"></path>
                            </svg>
                            <span class="sr-only">Dismiss</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- PayCal Australia Main Header -->
        <header class="supports-backdrop-blur:bg-background/60 sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur">
            <div class="container flex h-14 items-center">
                <!-- Mobile Menu Button -->
                <button class="inline-flex items-center justify-center rounded-md font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-9 w-9 md:hidden" onclick="toggleMobileMenu()">
                    <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none" class="h-5 w-5">
                        <path d="M8 2H13.5C13.7761 2 14 2.22386 14 2.5V12.5C14 12.7761 13.7761 13 13.5 13H8V2ZM7 2H1.5C1.22386 2 1 2.22386 1 2.5V12.5C1 12.7761 1.22386 13 1.5 13H7V2ZM0 2.5C0 1.67157 0.671573 1 1.5 1H13.5C14.3284 1 15 1.67157 15 2.5V12.5C15 13.3284 14.3284 14 13.5 14H1.5C0.671573 14 0 13.3284 0 12.5V2.5Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path>
                    </svg>
                    <span class="sr-only">Toggle Menu</span>
                </button>

                <!-- Desktop Navigation -->
                <div class="mr-4 hidden md:flex">
                    <nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex max-w-max flex-1 items-center justify-center">
                        <!-- PayCal Logo -->
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256" class="h-6 w-6 dark:text-white" aria-hidden="true">
                            <path d="M0 0h256v256H0z" fill="none"></path>
                            <rect width="256" height="256" fill="none" stroke="currentColor" stroke-width="16"></rect>
                            <rect x="32" y="32" width="192" height="64" fill="currentColor"></rect>
                            <circle cx="64" cy="128" r="16" fill="currentColor"></circle>
                            <circle cx="128" cy="128" r="16" fill="currentColor"></circle>
                            <circle cx="192" cy="128" r="16" fill="currentColor"></circle>
                            <circle cx="64" cy="176" r="16" fill="currentColor"></circle>
                            <circle cx="128" cy="176" r="16" fill="currentColor"></circle>
                            <circle cx="192" cy="176" r="16" fill="currentColor"></circle>
                            <circle cx="64" cy="224" r="16" fill="currentColor"></circle>
                            <circle cx="128" cy="224" r="16" fill="currentColor"></circle>
                            <circle cx="192" cy="224" r="16" fill="currentColor"></circle>
                        </svg>

                        <div style="position:relative">
                            <ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr">
                                <li>
                                    <a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="index.html">
                                        Paycal.com.au
                                    </a>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Pay Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden group-hover:block bg-background border rounded-md shadow-lg min-w-[250px] z-50">
                                            <div class="p-2">
                                                <a href="index.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Australian Pay Calculator</a>
                                                <a href="contractor-pay-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Contractor Pay Calculator</a>
                                                <a href="contract-rate-benchmarking.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Contract Rate Benchmarking</a>
                                                <a href="salary-rate-benchmarking.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Salary Rate Benchmarking</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Home & Property Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden group-hover:block bg-background border rounded-md shadow-lg min-w-[250px] z-50">
                                            <div class="p-2">
                                                <a href="mortgage-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Mortgage Calculator</a>
                                                <a href="home-loan-comparison.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Home Loan Comparison</a>
                                                <a href="rate-cut-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Rate Cut Calculator</a>
                                                <a href="mortgage-payoff-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md bg-accent/50">Mortgage Payoff Calculator</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Tax Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden group-hover:block bg-background border rounded-md shadow-lg min-w-[200px] z-50">
                                            <div class="p-2">
                                                <a href="tax-cut-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Tax Cut Calculator</a>
                                                <a href="tax-calendar.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Tax Calendar</a>
                                                <a href="gst-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">GST Calculator</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Financial Tools
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden group-hover:block bg-background border rounded-md shadow-lg min-w-[200px] z-50">
                                            <div class="p-2">
                                                <a href="invoice-generator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Invoice Generator</a>
                                                <a href="quote-generator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Quote Generator</a>
                                                <a href="payslip-generator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Payslip Generator</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Vehicle Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden group-hover:block bg-background border rounded-md shadow-lg min-w-[250px] z-50">
                                            <div class="p-2">
                                                <a href="novated-lease-calculator.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">EV & ICE Novated Lease</a>
                                                <a href="vehicle-registration.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Vehicle Registration</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="relative group">
                                        <button class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50 group submenu-trigger">
                                            Holidays Calculators
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180">
                                                <path d="m6 9 6 6 6-6"></path>
                                            </svg>
                                        </button>
                                        <div class="absolute left-0 top-full mt-1 hidden group-hover:block bg-background border rounded-md shadow-lg min-w-[200px] z-50">
                                            <div class="p-2">
                                                <a href="public-holidays.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">Public Holidays</a>
                                                <a href="school-holidays.html" class="block px-3 py-2 text-sm hover:bg-accent rounded-md">School Holidays</a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <a class="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50" href="#recommended">
                                        Recommended
                                    </a>
                                </li>
                            </ul>
                        </div>
                        <div class="absolute left-0 top-full flex justify-center"></div>
                    </nav>
                </div>

                <!-- Right Side Actions -->
                <div class="container flex h-16 items-center space-x-4 sm:justify-between sm:space-x-0">
                    <div class="flex flex-1 items-center justify-end space-x-4">
                        <nav class="flex items-center space-x-1">
                            <!-- Theme Toggle -->
                            <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 w-10" onclick="toggleTheme()">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-[1.5rem] w-[1.3rem] dark:hidden">
                                    <circle cx="12" cy="12" r="4"></circle>
                                    <path d="M12 2v2"></path>
                                    <path d="M12 20v2"></path>
                                    <path d="m4.93 4.93 1.41 1.41"></path>
                                    <path d="m17.66 17.66 1.41 1.41"></path>
                                    <path d="M2 12h2"></path>
                                    <path d="M20 12h2"></path>
                                    <path d="m6.34 17.66-1.41 1.41"></path>
                                    <path d="m19.07 4.93-1.41 1.41"></path>
                                </svg>
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="hidden h-5 w-5 dark:block dark:text-white">
                                    <path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"></path>
                                </svg>
                                <span class="sr-only">Toggle theme</span>
                            </button>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- Mobile Menu -->
            <div id="mobile-menu" class="hidden md:hidden border-t bg-background">
                <div class="container px-4 py-4">
                    <nav class="space-y-2">
                        <a href="index.html" class="block px-3 py-2 text-sm font-medium hover:bg-accent rounded-md">Paycal.com.au</a>
                        <div class="space-y-1">
                            <div class="px-3 py-2 text-sm font-medium text-muted-foreground">Pay Calculators</div>
                            <a href="index.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Australian Pay Calculator</a>
                            <a href="contractor-pay-calculator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Contractor Pay Calculator</a>
                            <a href="contract-rate-benchmarking.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Contract Rate Benchmarking</a>
                            <a href="salary-rate-benchmarking.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Salary Rate Benchmarking</a>
                        </div>
                        <div class="space-y-1">
                            <div class="px-3 py-2 text-sm font-medium text-muted-foreground">Home & Property Calculators</div>
                            <a href="mortgage-calculator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Mortgage Calculator</a>
                            <a href="home-loan-comparison.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Home Loan Comparison</a>
                            <a href="rate-cut-calculator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Rate Cut Calculator</a>
                            <a href="mortgage-payoff-calculator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md bg-accent/50">Mortgage Payoff Calculator</a>
                        </div>
                        <div class="space-y-1">
                            <div class="px-3 py-2 text-sm font-medium text-muted-foreground">Tax Calculators</div>
                            <a href="tax-cut-calculator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Tax Cut Calculator</a>
                            <a href="tax-calendar.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Tax Calendar</a>
                            <a href="gst-calculator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">GST Calculator</a>
                        </div>
                        <div class="space-y-1">
                            <div class="px-3 py-2 text-sm font-medium text-muted-foreground">Financial Tools</div>
                            <a href="invoice-generator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Invoice Generator</a>
                            <a href="quote-generator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Quote Generator</a>
                            <a href="payslip-generator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Payslip Generator</a>
                        </div>
                        <div class="space-y-1">
                            <div class="px-3 py-2 text-sm font-medium text-muted-foreground">Vehicle Calculators</div>
                            <a href="novated-lease-calculator.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">EV & ICE Novated Lease</a>
                            <a href="vehicle-registration.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Vehicle Registration</a>
                        </div>
                        <div class="space-y-1">
                            <div class="px-3 py-2 text-sm font-medium text-muted-foreground">Holidays Calculators</div>
                            <a href="public-holidays.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">Public Holidays</a>
                            <a href="school-holidays.html" class="block px-6 py-2 text-sm hover:bg-accent rounded-md">School Holidays</a>
                        </div>
                        <a href="#recommended" class="block px-3 py-2 text-sm font-medium hover:bg-accent rounded-md">Recommended</a>
                    </nav>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="flex-1">
            <div class="container mx-auto px-4 py-8">
                <!-- Hero Section -->
                <div class="text-center mb-12">
                    <h1 class="text-4xl font-bold tracking-tight text-foreground sm:text-6xl mb-4">
                        Mortgage Payoff Calculator - Australia
                    </h1>
                    <p class="text-xl text-muted-foreground max-w-3xl mx-auto">
                        Discover how extra payments can help you pay off your Australian home loan faster. Calculate time and interest savings with our free mortgage payoff calculator and see how much you could save.
                    </p>
                </div>

                <!-- Calculator Section -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12" id="calculator">
                    <!-- Calculator Form -->
                    <div class="calculator-container p-6">
                        <h2 class="text-2xl font-semibold mb-6">Mortgage Payoff Calculator</h2>

                        <form id="payoffForm" class="space-y-4">
                            <div class="input-group">
                                <label for="currentBalance">Current Loan Balance ($)</label>
                                <input type="number" id="currentBalance" name="currentBalance" value="400000" min="1000" max="10000000" step="1000" required>
                            </div>

                            <div class="input-group">
                                <label for="interestRate">Interest Rate (% per annum)</label>
                                <input type="number" id="interestRate" name="interestRate" value="6.5" min="0.1" max="20" step="0.01" required>
                            </div>

                            <div class="input-group">
                                <label for="remainingTerm">Remaining Term (years)</label>
                                <input type="number" id="remainingTerm" name="remainingTerm" value="25" min="1" max="50" step="1" required>
                            </div>

                            <div class="input-group">
                                <label for="paymentFrequency">Payment Frequency</label>
                                <select id="paymentFrequency" name="paymentFrequency">
                                    <option value="monthly">Monthly</option>
                                    <option value="fortnightly">Fortnightly</option>
                                    <option value="weekly">Weekly</option>
                                </select>
                            </div>

                            <div class="input-group">
                                <label for="extraPayments">Extra Payments ($)</label>
                                <input type="number" id="extraPayments" name="extraPayments" value="200" min="0" max="100000" step="10">
                                <small class="text-muted-foreground">Additional payments per period</small>
                            </div>

                            <button type="submit" class="btn-primary w-full">
                                Calculate Payoff Savings
                            </button>
                        </form>
                    </div>

                    <!-- Results Panel -->
                    <div class="calculator-container p-6">
                        <h2 class="text-2xl font-semibold mb-6">Your Payoff Results</h2>

                        <div id="results" class="results-container">
                            <div class="result-item">
                                <span class="result-label">Regular Payment:</span>
                                <span class="result-value" id="regularPayment">$2,654</span>
                            </div>
                            <div class="result-item">
                                <span class="result-label">With Extra Payments:</span>
                                <span class="result-value" id="totalPayment">$2,854</span>
                            </div>
                            <div class="result-item">
                                <span class="result-label">Time Saved:</span>
                                <span class="result-value savings-highlight" id="timeSaved">4 years 2 months</span>
                            </div>
                            <div class="result-item">
                                <span class="result-label">Interest Saved:</span>
                                <span class="result-value savings-highlight" id="interestSaved">$89,420</span>
                            </div>
                            <div class="result-item">
                                <span class="result-label">New Payoff Time:</span>
                                <span class="result-value" id="newPayoffTime">20 years 10 months</span>
                            </div>
                        </div>

                        <!-- Savings Visualization -->
                        <div class="mt-6">
                            <h3 class="text-lg font-semibold mb-4">Savings Breakdown</h3>
                            <div class="space-y-3">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm">Regular Loan</span>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-32 h-2 bg-muted rounded-full overflow-hidden">
                                            <div class="h-full bg-orange-500 rounded-full" style="width: 100%" id="regularBar"></div>
                                        </div>
                                        <span class="text-sm font-medium" id="regularTime">25 years</span>
                                    </div>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm">With Extra Payments</span>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-32 h-2 bg-muted rounded-full overflow-hidden">
                                            <div class="h-full bg-green-500 rounded-full" style="width: 83%" id="extraBar"></div>
                                        </div>
                                        <span class="text-sm font-medium" id="extraTime">20.8 years</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Information Section -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                    <div class="calculator-container p-6">
                        <h3 class="text-xl font-semibold mb-4">🚀 Pay Off Faster</h3>
                        <p class="text-muted-foreground">
                            Making extra payments towards your mortgage principal can dramatically reduce your loan term and save thousands in interest. Even small additional payments make a big difference over time.
                        </p>
                    </div>

                    <div class="calculator-container p-6">
                        <h3 class="text-xl font-semibold mb-4">💰 Save Interest</h3>
                        <p class="text-muted-foreground">
                            Every extra dollar you pay goes directly towards reducing your principal balance, which means less interest charged over the life of your loan. The earlier you start, the more you save.
                        </p>
                    </div>

                    <div class="calculator-container p-6">
                        <h3 class="text-xl font-semibold mb-4">📊 Smart Strategies</h3>
                        <p class="text-muted-foreground">
                            Consider increasing payment frequency, using tax refunds, or redirecting raises towards your mortgage. Our calculator shows you exactly how much time and money you'll save.
                        </p>
                    </div>
                </div>

                <!-- Mortgage Payoff Strategies Section -->
                <div class="calculator-container p-8 mb-12" id="strategies">
                    <h2 class="text-3xl font-bold mb-6">Mortgage Payoff Strategies</h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div>
                            <h3 class="text-xl font-semibold mb-4">Extra Payment Methods</h3>
                            <p class="text-muted-foreground mb-4">
                                There are several ways to make extra payments on your Australian home loan:
                            </p>
                            <ul class="list-disc list-inside text-muted-foreground space-y-2">
                                <li>Regular additional payments each period</li>
                                <li>Annual lump sum payments (tax refunds, bonuses)</li>
                                <li>Increase payment frequency (monthly to fortnightly)</li>
                                <li>Round up payments to the nearest $50 or $100</li>
                                <li>Use offset accounts to reduce interest</li>
                            </ul>
                        </div>

                        <div>
                            <h3 class="text-xl font-semibold mb-4">Benefits of Early Payoff</h3>
                            <p class="text-muted-foreground mb-4">
                                Paying off your mortgage early provides several advantages:
                            </p>
                            <ul class="list-disc list-inside text-muted-foreground space-y-2">
                                <li><strong>Interest Savings:</strong> Reduce total interest paid significantly</li>
                                <li><strong>Financial Freedom:</strong> Own your home outright sooner</li>
                                <li><strong>Peace of Mind:</strong> Eliminate monthly mortgage payments</li>
                                <li><strong>Equity Building:</strong> Build wealth through property ownership</li>
                                <li><strong>Flexibility:</strong> Free up income for other investments</li>
                            </ul>
                        </div>
                    </div>

                    <div class="mt-8 p-6 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                        <h4 class="text-lg font-semibold mb-3 text-green-800 dark:text-green-200">💡 Pro Tip</h4>
                        <p class="text-green-700 dark:text-green-300">
                            Even an extra $50 per month can save you years off your mortgage and thousands in interest. Start small and increase your extra payments as your income grows. The key is consistency!
                        </p>
                    </div>
                </div>
            </div>
        </main>

        <!-- PayCal Australia Footer Component -->
        <footer class="border-t mt-24">
            <div class="container mx-auto px-6 py-12">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
                    <!-- Brand Section -->
                    <div class="lg:col-span-2">
                        <a class="text-xl font-bold" href="index.html">PayCal.com.au</a>
                        <p class="mt-4 text-sm text-muted-foreground">
                            Australian calculators for tax, salary, and financial planning. Helping Aussies make informed financial decisions.
                        </p>
                        <div class="mt-6">
                            <a href="mailto:<EMAIL>" class="text-sm text-muted-foreground hover:text-primary">
                                <EMAIL>
                            </a>
                        </div>
                    </div>

                    <!-- Pay Calculators -->
                    <div>
                        <h3 class="text-sm font-semibold mb-4">Pay Calculators</h3>
                        <ul class="space-y-3">
                            <li><a class="text-sm text-muted-foreground hover:text-primary" href="index.html">Australian Pay Calculator</a></li>
                            <li><a class="text-sm text-muted-foreground hover:text-primary" href="contractor-pay-calculator.html">Contractor Pay Calculator</a></li>
                            <li><a class="text-sm text-muted-foreground hover:text-primary" href="#">Contract Rate Benchmarking</a></li>
                            <li><a class="text-sm text-muted-foreground hover:text-primary" href="#">Salary Rate Benchmarking</a></li>
                        </ul>
                    </div>

                    <!-- Property Calculators -->
                    <div>
                        <h3 class="text-sm font-semibold mb-4">Property Calculators</h3>
                        <ul class="space-y-3">
                            <li><a class="text-sm text-muted-foreground hover:text-primary" href="mortgage-calculator.html">Mortgage Calculator</a></li>
                            <li><a class="text-sm text-muted-foreground hover:text-primary" href="home-loan-comparison.html">Home Loan Comparison</a></li>
                            <li><a class="text-sm text-muted-foreground hover:text-primary" href="#">Rate Cut Calculator</a></li>
                            <li><a class="text-sm text-muted-foreground hover:text-primary" href="mortgage-payoff-calculator.html">Mortgage Payoff Calculator</a></li>
                        </ul>
                    </div>

                    <!-- Tax Tools -->
                    <div>
                        <h3 class="text-sm font-semibold mb-4">Tax Tools</h3>
                        <ul class="space-y-3">
                            <li><a class="text-sm text-muted-foreground hover:text-primary" href="tax-cut-calculator.html">Tax Cut Calculator</a></li>
                            <li><a class="text-sm text-muted-foreground hover:text-primary" href="#">Tax Calendar</a></li>
                            <li><a class="text-sm text-muted-foreground hover:text-primary" href="gst-calculator.html">GST Calculator</a></li>
                        </ul>
                    </div>
                </div>

                <!-- Second Row -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-8">
                    <!-- Financial Tools -->
                    <div>
                        <h3 class="text-sm font-semibold mb-4">Financial Tools</h3>
                        <ul class="space-y-3">
                            <li><a class="text-sm text-muted-foreground hover:text-primary" href="invoice-generator.html">Invoice Generator</a></li>
                            <li><a class="text-sm text-muted-foreground hover:text-primary" href="quote-generator.html">Quote Generator</a></li>
                            <li><a class="text-sm text-muted-foreground hover:text-primary" href="#">Payslip Generator</a></li>
                        </ul>
                    </div>

                    <!-- Vehicle Tools -->
                    <div>
                        <h3 class="text-sm font-semibold mb-4">Vehicle Tools</h3>
                        <ul class="space-y-3">
                            <li><a class="text-sm text-muted-foreground hover:text-primary" href="novated-lease-calculator.html">EV & ICE Novated Lease</a></li>
                            <li><a class="text-sm text-muted-foreground hover:text-primary" href="#">Vehicle Registration</a></li>
                            <li><a class="text-sm text-muted-foreground hover:text-primary" href="public-holidays.html">Public Holidays</a></li>
                            <li><a class="text-sm text-muted-foreground hover:text-primary" href="school-holidays.html">School Holidays</a></li>
                            <li><a class="text-sm text-muted-foreground hover:text-primary" href="#recommended">Recommended</a></li>
                        </ul>
                    </div>
                </div>

                <!-- Footer Bottom -->
                <div class="mt-12 pt-8 border-t">
                    <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                        <div class="flex items-center space-x-4">
                            <a class="text-sm text-muted-foreground hover:text-primary" href="#privacy">Privacy Policy</a>
                            <span class="text-muted-foreground">•</span>
                            <a class="text-sm text-muted-foreground hover:text-primary" href="#blog">Blog</a>
                            <span class="text-muted-foreground">•</span>
                            <a href="#feedback" target="_blank" rel="noopener noreferrer" class="text-sm text-muted-foreground hover:text-primary">Feedback</a>
                        </div>
                        <p class="text-sm text-muted-foreground">
                            Made for Aussies ❤️ © 2025 PayCal
                        </p>
                    </div>
                </div>
            </div>
        </footer>

        <!-- Scroll to Top Button -->
        <div class="fixed bottom-4 right-4 z-50">
            <button class="inline-flex items-center justify-center text-sm font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 bg-background/60 backdrop-blur-sm shadow-sm hover:shadow-md transition-all duration-200" onclick="scrollToTop()">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                    <path d="m18 15-6-6-6 6"></path>
                </svg>
                <span class="sr-only">Scroll to top</span>
            </button>
        </div>
    </div>

    <!-- JavaScript for Mortgage Payoff Calculator -->
    <script>
        // Mortgage Payoff Calculator Logic
        function calculatePayoff() {
            const currentBalance = parseFloat(document.getElementById('currentBalance').value);
            const annualRate = parseFloat(document.getElementById('interestRate').value) / 100;
            const remainingTermYears = parseInt(document.getElementById('remainingTerm').value);
            const paymentFrequency = document.getElementById('paymentFrequency').value;
            const extraPayments = parseFloat(document.getElementById('extraPayments').value) || 0;

            let paymentsPerYear, periodicRate, totalPayments;

            switch (paymentFrequency) {
                case 'weekly':
                    paymentsPerYear = 52;
                    break;
                case 'fortnightly':
                    paymentsPerYear = 26;
                    break;
                case 'monthly':
                default:
                    paymentsPerYear = 12;
                    break;
            }

            periodicRate = annualRate / paymentsPerYear;
            totalPayments = remainingTermYears * paymentsPerYear;

            // Calculate regular payment using mortgage formula
            const regularPayment = currentBalance * (periodicRate * Math.pow(1 + periodicRate, totalPayments)) /
                                  (Math.pow(1 + periodicRate, totalPayments) - 1);

            // Calculate regular loan scenario
            let regularBalance = currentBalance;
            let regularInterestPaid = 0;
            let regularPaymentCount = 0;

            while (regularBalance > 0.01 && regularPaymentCount < totalPayments) {
                const interestPayment = regularBalance * periodicRate;
                const principalPayment = Math.min(regularPayment - interestPayment, regularBalance);

                regularInterestPaid += interestPayment;
                regularBalance -= principalPayment;
                regularPaymentCount++;

                if (principalPayment <= 0) break;
            }

            // Calculate with extra payments scenario
            const totalPaymentWithExtra = regularPayment + extraPayments;
            let extraBalance = currentBalance;
            let extraInterestPaid = 0;
            let extraPaymentCount = 0;

            while (extraBalance > 0.01 && extraPaymentCount < totalPayments * 2) {
                const interestPayment = extraBalance * periodicRate;
                const principalPayment = Math.min(totalPaymentWithExtra - interestPayment, extraBalance);

                extraInterestPaid += interestPayment;
                extraBalance -= principalPayment;
                extraPaymentCount++;

                if (principalPayment <= 0) break;
            }

            // Calculate savings
            const interestSaved = regularInterestPaid - extraInterestPaid;
            const timeSaved = (regularPaymentCount - extraPaymentCount) / paymentsPerYear;
            const newPayoffTimeYears = extraPaymentCount / paymentsPerYear;

            // Update results
            document.getElementById('regularPayment').textContent = formatCurrency(regularPayment);
            document.getElementById('totalPayment').textContent = formatCurrency(totalPaymentWithExtra);
            document.getElementById('timeSaved').textContent = formatTime(timeSaved);
            document.getElementById('interestSaved').textContent = formatCurrency(interestSaved);
            document.getElementById('newPayoffTime').textContent = formatTime(newPayoffTimeYears);

            // Update visualization bars
            const regularTimePercent = 100;
            const extraTimePercent = (newPayoffTimeYears / remainingTermYears) * 100;

            document.getElementById('regularBar').style.width = regularTimePercent + '%';
            document.getElementById('extraBar').style.width = extraTimePercent + '%';
            document.getElementById('regularTime').textContent = remainingTermYears + ' years';
            document.getElementById('extraTime').textContent = newPayoffTimeYears.toFixed(1) + ' years';
        }

        function formatCurrency(amount) {
            return new Intl.NumberFormat('en-AU', {
                style: 'currency',
                currency: 'AUD',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(amount);
        }

        function formatTime(years) {
            const wholeYears = Math.floor(years);
            const months = Math.round((years - wholeYears) * 12);

            if (months === 0) {
                return `${wholeYears} year${wholeYears !== 1 ? 's' : ''}`;
            } else if (wholeYears === 0) {
                return `${months} month${months !== 1 ? 's' : ''}`;
            } else {
                return `${wholeYears} year${wholeYears !== 1 ? 's' : ''} ${months} month${months !== 1 ? 's' : ''}`;
            }
        }

        // Event Listeners
        document.getElementById('payoffForm').addEventListener('submit', function(e) {
            e.preventDefault();
            calculatePayoff();
        });

        // Real-time calculation on input change
        document.querySelectorAll('#payoffForm input, #payoffForm select').forEach(input => {
            input.addEventListener('input', calculatePayoff);
        });

        // PayCal Header/Footer Functions
        function toggleMobileMenu() {
            const mobileMenu = document.getElementById('mobile-menu');
            if (mobileMenu) {
                mobileMenu.classList.toggle('hidden');
            }
        }

        function dismissBanner() {
            const banner = document.querySelector('.bg-green-100\\/90');
            if (banner) {
                banner.style.display = 'none';
            }
        }

        function toggleTheme() {
            const html = document.documentElement;
            const currentTheme = html.classList.contains('dark') ? 'dark' : 'light';

            if (currentTheme === 'light') {
                html.classList.remove('light');
                html.classList.add('dark');
                html.style.colorScheme = 'dark';
                localStorage.setItem('theme', 'dark');
            } else {
                html.classList.remove('dark');
                html.classList.add('light');
                html.style.colorScheme = 'light';
                localStorage.setItem('theme', 'light');
            }
        }

        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            const html = document.documentElement;

            html.classList.remove('light', 'dark');
            html.classList.add(savedTheme);
            html.style.colorScheme = savedTheme;
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            initializeTheme();
            calculatePayoff(); // Initial calculation

            // Close mobile menu when clicking outside
            document.addEventListener('click', function(event) {
                const mobileMenu = document.getElementById('mobile-menu');
                const menuButton = document.querySelector('[onclick="toggleMobileMenu()"]');

                if (mobileMenu && !mobileMenu.classList.contains('hidden')) {
                    if (!mobileMenu.contains(event.target) && !menuButton.contains(event.target)) {
                        mobileMenu.classList.add('hidden');
                    }
                }
            });

            // Keyboard navigation
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    const mobileMenu = document.getElementById('mobile-menu');
                    if (mobileMenu && !mobileMenu.classList.contains('hidden')) {
                        mobileMenu.classList.add('hidden');
                    }
                }
            });

            // Show/hide scroll to top button
            window.addEventListener('scroll', function() {
                const scrollButton = document.querySelector('.fixed.bottom-4.right-4');
                if (scrollButton) {
                    if (window.pageYOffset > 300) {
                        scrollButton.style.opacity = '1';
                        scrollButton.style.visibility = 'visible';
                    } else {
                        scrollButton.style.opacity = '0';
                        scrollButton.style.visibility = 'hidden';
                    }
                }
            });
        });
    </script>
</body>
</html>
