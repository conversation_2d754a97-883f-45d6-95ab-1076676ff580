/* Tailwind CSS Reset and Base Styles */
*,
::before,
::after {
  box-sizing: border-box;
  border-width: 0;
  border-style: solid;
  border-color: #e5e7eb;
}

::before,
::after {
  --tw-content: '';
}

html {
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  tab-size: 4;
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-feature-settings: normal;
  font-variation-settings: normal;
}

body {
  margin: 0;
  line-height: inherit;
}

/* CSS Variables for Theme */
:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 221.2 83.2% 53.3%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96.1%;
  --secondary-foreground: 222.2 47.4% 11.2%;
  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96.1%;
  --accent-foreground: 222.2 47.4% 11.2%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 221.2 83.2% 53.3%;
  --radius: 0.5rem;
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 210 40% 98%;
  --primary-foreground: 222.2 84% 4.9%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 212.7 26.8% 83.9%;
}



/* Base Styles */
body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
  line-height: 1.5;
  margin: 0;
  padding: 0;
}

/* Tailwind Base Classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

/* Container */
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}

/* Display */
.block { display: block; }
.inline-block { display: inline-block; }
.inline { display: inline; }
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.table { display: table; }
.grid { display: grid; }
.hidden { display: none; }

/* Position */
.static { position: static; }
.fixed { position: fixed; }
.absolute { position: absolute; }
.relative { position: relative; }
.sticky { position: sticky; }

/* Top / Right / Bottom / Left */
.top-0 { top: 0px; }
.right-0 { right: 0px; }
.bottom-0 { bottom: 0px; }
.left-0 { left: 0px; }
.top-1\/2 { top: 50%; }
.right-4 { right: 1rem; }
.bottom-4 { bottom: 1rem; }
.left-3 { left: 0.75rem; }

/* Z-Index */
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }

/* Flex Direction */
.flex-row { flex-direction: row; }
.flex-row-reverse { flex-direction: row-reverse; }
.flex-col { flex-direction: column; }
.flex-col-reverse { flex-direction: column-reverse; }

/* Flex Wrap */
.flex-wrap { flex-wrap: wrap; }
.flex-wrap-reverse { flex-wrap: wrap-reverse; }
.flex-nowrap { flex-wrap: nowrap; }

/* Flex */
.flex-1 { flex: 1 1 0%; }
.flex-auto { flex: 1 1 auto; }
.flex-initial { flex: 0 1 auto; }
.flex-none { flex: none; }

/* Flex Grow */
.flex-grow { flex-grow: 1; }
.flex-grow-0 { flex-grow: 0; }

/* Flex Shrink */
.flex-shrink { flex-shrink: 1; }
.flex-shrink-0 { flex-shrink: 0; }

/* Justify Content */
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-evenly { justify-content: space-evenly; }

/* Align Items */
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.items-center { align-items: center; }
.items-baseline { align-items: baseline; }
.items-stretch { align-items: stretch; }

/* Width */
.w-1 { width: 0.25rem; }
.w-2 { width: 0.5rem; }
.w-3 { width: 0.75rem; }
.w-4 { width: 1rem; }
.w-5 { width: 1.25rem; }
.w-6 { width: 1.5rem; }
.w-7 { width: 1.75rem; }
.w-8 { width: 2rem; }
.w-9 { width: 2.25rem; }
.w-10 { width: 2.5rem; }
.w-11 { width: 2.75rem; }
.w-12 { width: 3rem; }
.w-14 { width: 3.5rem; }
.w-16 { width: 4rem; }
.w-20 { width: 5rem; }
.w-24 { width: 6rem; }
.w-28 { width: 7rem; }
.w-32 { width: 8rem; }
.w-36 { width: 9rem; }
.w-40 { width: 10rem; }
.w-44 { width: 11rem; }
.w-48 { width: 12rem; }
.w-52 { width: 13rem; }
.w-56 { width: 14rem; }
.w-60 { width: 15rem; }
.w-64 { width: 16rem; }
.w-72 { width: 18rem; }
.w-80 { width: 20rem; }
.w-96 { width: 24rem; }
.w-auto { width: auto; }
.w-px { width: 1px; }
.w-0\.5 { width: 0.125rem; }
.w-1\.5 { width: 0.375rem; }
.w-2\.5 { width: 0.625rem; }
.w-3\.5 { width: 0.875rem; }
.w-1\/2 { width: 50%; }
.w-1\/3 { width: 33.333333%; }
.w-2\/3 { width: 66.666667%; }
.w-1\/4 { width: 25%; }
.w-2\/4 { width: 50%; }
.w-3\/4 { width: 75%; }
.w-1\/5 { width: 20%; }
.w-2\/5 { width: 40%; }
.w-3\/5 { width: 60%; }
.w-4\/5 { width: 80%; }
.w-1\/6 { width: 16.666667%; }
.w-2\/6 { width: 33.333333%; }
.w-3\/6 { width: 50%; }
.w-4\/6 { width: 66.666667%; }
.w-5\/6 { width: 83.333333%; }
.w-1\/12 { width: 8.333333%; }
.w-2\/12 { width: 16.666667%; }
.w-3\/12 { width: 25%; }
.w-4\/12 { width: 33.333333%; }
.w-5\/12 { width: 41.666667%; }
.w-6\/12 { width: 50%; }
.w-7\/12 { width: 58.333333%; }
.w-8\/12 { width: 66.666667%; }
.w-9\/12 { width: 75%; }
.w-10\/12 { width: 83.333333%; }
.w-11\/12 { width: 91.666667%; }
.w-full { width: 100%; }
.w-screen { width: 100vw; }
.w-min { width: min-content; }
.w-max { width: max-content; }
.w-fit { width: fit-content; }

/* Height */
.h-1 { height: 0.25rem; }
.h-2 { height: 0.5rem; }
.h-3 { height: 0.75rem; }
.h-4 { height: 1rem; }
.h-5 { height: 1.25rem; }
.h-6 { height: 1.5rem; }
.h-7 { height: 1.75rem; }
.h-8 { height: 2rem; }
.h-9 { height: 2.25rem; }
.h-10 { height: 2.5rem; }
.h-11 { height: 2.75rem; }
.h-12 { height: 3rem; }
.h-14 { height: 3.5rem; }
.h-16 { height: 4rem; }
.h-20 { height: 5rem; }
.h-24 { height: 6rem; }
.h-28 { height: 7rem; }
.h-32 { height: 8rem; }
.h-36 { height: 9rem; }
.h-40 { height: 10rem; }
.h-44 { height: 11rem; }
.h-48 { height: 12rem; }
.h-52 { height: 13rem; }
.h-56 { height: 14rem; }
.h-60 { height: 15rem; }
.h-64 { height: 16rem; }
.h-72 { height: 18rem; }
.h-80 { height: 20rem; }
.h-96 { height: 24rem; }
.h-auto { height: auto; }
.h-px { height: 1px; }
.h-0\.5 { height: 0.125rem; }
.h-1\.5 { height: 0.375rem; }
.h-2\.5 { height: 0.625rem; }
.h-3\.5 { height: 0.875rem; }
.h-1\/2 { height: 50%; }
.h-1\/3 { height: 33.333333%; }
.h-2\/3 { height: 66.666667%; }
.h-1\/4 { height: 25%; }
.h-2\/4 { height: 50%; }
.h-3\/4 { height: 75%; }
.h-1\/5 { height: 20%; }
.h-2\/5 { height: 40%; }
.h-3\/5 { height: 60%; }
.h-4\/5 { height: 80%; }
.h-1\/6 { height: 16.666667%; }
.h-2\/6 { height: 33.333333%; }
.h-3\/6 { height: 50%; }
.h-4\/6 { height: 66.666667%; }
.h-5\/6 { height: 83.333333%; }
.h-full { height: 100%; }
.h-screen { height: 100vh; }
.h-min { height: min-content; }
.h-max { height: max-content; }
.h-fit { height: fit-content; }

/* Min Height */
.min-h-0 { min-height: 0px; }
.min-h-full { min-height: 100%; }
.min-h-screen { min-height: 100vh; }
.min-h-min { min-height: min-content; }
.min-h-max { min-height: max-content; }
.min-h-fit { min-height: fit-content; }

/* Max Width */
.max-w-0 { max-width: 0rem; }
.max-w-none { max-width: none; }
.max-w-xs { max-width: 20rem; }
.max-w-sm { max-width: 24rem; }
.max-w-md { max-width: 28rem; }
.max-w-lg { max-width: 32rem; }
.max-w-xl { max-width: 36rem; }
.max-w-2xl { max-width: 42rem; }
.max-w-3xl { max-width: 48rem; }
.max-w-4xl { max-width: 56rem; }
.max-w-5xl { max-width: 64rem; }
.max-w-6xl { max-width: 72rem; }
.max-w-7xl { max-width: 80rem; }
.max-w-full { max-width: 100%; }
.max-w-min { max-width: min-content; }
.max-w-max { max-width: max-content; }
.max-w-fit { max-width: fit-content; }
.max-w-prose { max-width: 65ch; }
.max-w-screen-sm { max-width: 640px; }
.max-w-screen-md { max-width: 768px; }
.max-w-screen-lg { max-width: 1024px; }
.max-w-screen-xl { max-width: 1280px; }
.max-w-screen-2xl { max-width: 1536px; }

/* Padding */
.p-0 { padding: 0px; }
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-5 { padding: 1.25rem; }
.p-6 { padding: 1.5rem; }
.p-7 { padding: 1.75rem; }
.p-8 { padding: 2rem; }
.p-9 { padding: 2.25rem; }
.p-10 { padding: 2.5rem; }
.p-11 { padding: 2.75rem; }
.p-12 { padding: 3rem; }
.px-0 { padding-left: 0px; padding-right: 0px; }
.px-1 { padding-left: 0.25rem; padding-right: 0.25rem; }
.px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-5 { padding-left: 1.25rem; padding-right: 1.25rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.px-7 { padding-left: 1.75rem; padding-right: 1.75rem; }
.px-8 { padding-left: 2rem; padding-right: 2rem; }
.py-0 { padding-top: 0px; padding-bottom: 0px; }
.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.py-5 { padding-top: 1.25rem; padding-bottom: 1.25rem; }
.py-6 { padding-top: 1.5rem; padding-bottom: 1.5rem; }
.py-7 { padding-top: 1.75rem; padding-bottom: 1.75rem; }
.py-8 { padding-top: 2rem; padding-bottom: 2rem; }
.py-12 { padding-top: 3rem; padding-bottom: 3rem; }
.pt-0 { padding-top: 0px; }
.pt-1 { padding-top: 0.25rem; }
.pt-2 { padding-top: 0.5rem; }
.pt-3 { padding-top: 0.75rem; }
.pt-4 { padding-top: 1rem; }
.pt-5 { padding-top: 1.25rem; }
.pt-6 { padding-top: 1.5rem; }
.pt-7 { padding-top: 1.75rem; }
.pt-8 { padding-top: 2rem; }
.pt-12 { padding-top: 3rem; }
.pr-3 { padding-right: 0.75rem; }
.pr-12 { padding-right: 3rem; }
.pb-3 { padding-bottom: 0.75rem; }
.pb-4 { padding-bottom: 1rem; }
.pb-8 { padding-bottom: 2rem; }
.pl-3 { padding-left: 0.75rem; }
.pl-5 { padding-left: 1.25rem; }
.pl-6 { padding-left: 1.5rem; }
.pl-8 { padding-left: 2rem; }

/* Margin */
.m-0 { margin: 0px; }
.m-1 { margin: 0.25rem; }
.m-2 { margin: 0.5rem; }
.m-3 { margin: 0.75rem; }
.m-4 { margin: 1rem; }
.m-5 { margin: 1.25rem; }
.m-6 { margin: 1.5rem; }
.m-auto { margin: auto; }
.mx-0 { margin-left: 0px; margin-right: 0px; }
.mx-1 { margin-left: 0.25rem; margin-right: 0.25rem; }
.mx-2 { margin-left: 0.5rem; margin-right: 0.5rem; }
.mx-3 { margin-left: 0.75rem; margin-right: 0.75rem; }
.mx-4 { margin-left: 1rem; margin-right: 1rem; }
.mx-5 { margin-left: 1.25rem; margin-right: 1.25rem; }
.mx-6 { margin-left: 1.5rem; margin-right: 1.5rem; }
.mx-auto { margin-left: auto; margin-right: auto; }
.my-0 { margin-top: 0px; margin-bottom: 0px; }
.my-1 { margin-top: 0.25rem; margin-bottom: 0.25rem; }
.my-2 { margin-top: 0.5rem; margin-bottom: 0.5rem; }
.my-3 { margin-top: 0.75rem; margin-bottom: 0.75rem; }
.my-4 { margin-top: 1rem; margin-bottom: 1rem; }
.my-5 { margin-top: 1.25rem; margin-bottom: 1.25rem; }
.my-6 { margin-top: 1.5rem; margin-bottom: 1.5rem; }
.mt-0 { margin-top: 0px; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }
.mt-5 { margin-top: 1.25rem; }
.mt-6 { margin-top: 1.5rem; }
.mt-8 { margin-top: 2rem; }
.mt-12 { margin-top: 3rem; }
.mr-1 { margin-right: 0.25rem; }
.mr-2 { margin-right: 0.5rem; }
.mr-3 { margin-right: 0.75rem; }
.mr-4 { margin-right: 1rem; }
.mr-5 { margin-right: 1.25rem; }
.mr-6 { margin-right: 1.5rem; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-5 { margin-bottom: 1.25rem; }
.mb-6 { margin-bottom: 1.5rem; }
.ml-1 { margin-left: 0.25rem; }
.ml-2 { margin-left: 0.5rem; }
.ml-3 { margin-left: 0.75rem; }
.ml-4 { margin-left: 1rem; }
.ml-5 { margin-left: 1.25rem; }
.ml-6 { margin-left: 1.5rem; }

/* Space Between */
.space-x-1 > :not([hidden]) ~ :not([hidden]) { --tw-space-x-reverse: 0; margin-right: calc(0.25rem * var(--tw-space-x-reverse)); margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse))); }
.space-x-2 > :not([hidden]) ~ :not([hidden]) { --tw-space-x-reverse: 0; margin-right: calc(0.5rem * var(--tw-space-x-reverse)); margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse))); }
.space-x-3 > :not([hidden]) ~ :not([hidden]) { --tw-space-x-reverse: 0; margin-right: calc(0.75rem * var(--tw-space-x-reverse)); margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse))); }
.space-x-4 > :not([hidden]) ~ :not([hidden]) { --tw-space-x-reverse: 0; margin-right: calc(1rem * var(--tw-space-x-reverse)); margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse))); }
.space-y-1 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: 0; margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse))); margin-bottom: calc(0.25rem * var(--tw-space-y-reverse)); }
.space-y-2 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: 0; margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse))); margin-bottom: calc(0.5rem * var(--tw-space-y-reverse)); }
.space-y-3 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: 0; margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse))); margin-bottom: calc(0.75rem * var(--tw-space-y-reverse)); }
.space-y-4 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: 0; margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse))); margin-bottom: calc(1rem * var(--tw-space-y-reverse)); }
.space-y-6 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: 0; margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse))); margin-bottom: calc(1.5rem * var(--tw-space-y-reverse)); }

/* Gap */
.gap-1 { gap: 0.25rem; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 0.75rem; }
.gap-4 { gap: 1rem; }
.gap-5 { gap: 1.25rem; }
.gap-6 { gap: 1.5rem; }
.gap-7 { gap: 1.75rem; }
.gap-8 { gap: 2rem; }
.gap-x-1 { column-gap: 0.25rem; }
.gap-x-2 { column-gap: 0.5rem; }
.gap-x-3 { column-gap: 0.75rem; }
.gap-x-4 { column-gap: 1rem; }
.gap-y-1 { row-gap: 0.25rem; }
.gap-y-2 { row-gap: 0.5rem; }
.gap-y-3 { row-gap: 0.75rem; }
.gap-y-4 { row-gap: 1rem; }

/* Borders */
.border-0 { border-width: 0px; }
.border { border-width: 1px; }
.border-2 { border-width: 2px; }
.border-4 { border-width: 4px; }
.border-8 { border-width: 8px; }
.border-x { border-left-width: 1px; border-right-width: 1px; }
.border-y { border-top-width: 1px; border-bottom-width: 1px; }
.border-t { border-top-width: 1px; }
.border-r { border-right-width: 1px; }
.border-b { border-bottom-width: 1px; }
.border-l { border-left-width: 1px; }
.border-t-0 { border-top-width: 0px; }
.border-r-0 { border-right-width: 0px; }
.border-b-0 { border-bottom-width: 0px; }
.border-l-0 { border-left-width: 0px; }
.border-t-2 { border-top-width: 2px; }
.border-r-2 { border-right-width: 2px; }
.border-b-2 { border-bottom-width: 2px; }
.border-l-2 { border-left-width: 2px; }

/* Border Radius */
.rounded-none { border-radius: 0px; }
.rounded-sm { border-radius: 0.125rem; }
.rounded { border-radius: 0.25rem; }
.rounded-md { border-radius: 0.375rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-2xl { border-radius: 1rem; }
.rounded-3xl { border-radius: 1.5rem; }
.rounded-full { border-radius: 9999px; }

/* Background Colors */
.bg-transparent { background-color: transparent; }
.bg-current { background-color: currentColor; }
.bg-black { background-color: rgb(0 0 0); }
.bg-white { background-color: rgb(255 255 255); }
.bg-gray-50 { background-color: rgb(249 250 251); }
.bg-gray-100 { background-color: rgb(243 244 246); }
.bg-gray-200 { background-color: rgb(229 231 235); }
.bg-gray-300 { background-color: rgb(209 213 219); }
.bg-gray-400 { background-color: rgb(156 163 175); }
.bg-gray-500 { background-color: rgb(107 114 128); }
.bg-gray-600 { background-color: rgb(75 85 99); }
.bg-gray-700 { background-color: rgb(55 65 81); }
.bg-gray-800 { background-color: rgb(31 41 55); }
.bg-gray-900 { background-color: rgb(17 24 39); }
.bg-red-50 { background-color: rgb(254 242 242); }
.bg-red-100 { background-color: rgb(254 226 226); }
.bg-red-500 { background-color: rgb(239 68 68); }
.bg-red-600 { background-color: rgb(220 38 38); }
.bg-yellow-50 { background-color: rgb(254 252 232); }
.bg-yellow-100 { background-color: rgb(254 249 195); }
.bg-green-50 { background-color: rgb(240 253 244); }
.bg-green-100 { background-color: rgb(220 252 231); }
.bg-green-600 { background-color: rgb(22 163 74); }
.bg-green-700 { background-color: rgb(21 128 61); }
.bg-blue-50 { background-color: rgb(239 246 255); }
.bg-blue-100 { background-color: rgb(219 234 254); }
.bg-blue-600 { background-color: rgb(37 99 235); }
.bg-blue-700 { background-color: rgb(29 78 216); }
.bg-orange-50 { background-color: rgb(255 247 237); }
.bg-orange-100 { background-color: rgb(254 215 170); }
.bg-orange-600 { background-color: rgb(234 88 12); }
.bg-orange-700 { background-color: rgb(194 65 12); }

/* Background Color with Opacity */
.bg-green-100\/90 { background-color: rgb(220 252 231 / 0.9); }
.bg-green-200\/70 { background-color: rgb(187 247 208 / 0.7); }
.bg-green-800\/30 { background-color: rgb(22 101 52 / 0.3); }
.bg-green-900\/20 { background-color: rgb(20 83 45 / 0.2); }
.bg-blue-900\/20 { background-color: rgb(30 58 138 / 0.2); }
.bg-orange-900\/20 { background-color: rgb(154 52 18 / 0.2); }
.bg-yellow-900\/20 { background-color: rgb(133 77 14 / 0.2); }
.bg-background\/60 { background-color: hsl(var(--background) / 0.6); }
.bg-background\/95 { background-color: hsl(var(--background) / 0.95); }

/* Text Colors */
.text-transparent { color: transparent; }
.text-current { color: currentColor; }
.text-black { color: rgb(0 0 0); }
.text-white { color: rgb(255 255 255); }
.text-gray-50 { color: rgb(249 250 251); }
.text-gray-100 { color: rgb(243 244 246); }
.text-gray-200 { color: rgb(229 231 235); }
.text-gray-300 { color: rgb(209 213 219); }
.text-gray-400 { color: rgb(156 163 175); }
.text-gray-500 { color: rgb(107 114 128); }
.text-gray-600 { color: rgb(75 85 99); }
.text-gray-700 { color: rgb(55 65 81); }
.text-gray-800 { color: rgb(31 41 55); }
.text-gray-900 { color: rgb(17 24 39); }
.text-red-600 { color: rgb(220 38 38); }
.text-yellow-700 { color: rgb(161 98 7); }
.text-yellow-800 { color: rgb(133 77 14); }
.text-green-600 { color: rgb(22 163 74); }
.text-green-700 { color: rgb(21 128 61); }
.text-green-800 { color: rgb(22 101 52); }
.text-green-900 { color: rgb(20 83 45); }
.text-green-100 { color: rgb(220 252 231); }
.text-green-200 { color: rgb(187 247 208); }
.text-green-300 { color: rgb(134 239 172); }
.text-green-400 { color: rgb(74 222 128); }
.text-blue-600 { color: rgb(37 99 235); }
.text-blue-700 { color: rgb(29 78 216); }
.text-blue-800 { color: rgb(30 64 175); }
.text-blue-900 { color: rgb(30 58 138); }
.text-blue-100 { color: rgb(219 234 254); }
.text-blue-200 { color: rgb(191 219 254); }
.text-orange-600 { color: rgb(234 88 12); }
.text-orange-700 { color: rgb(194 65 12); }

/* Border Colors */
.border-transparent { border-color: transparent; }
.border-current { border-color: currentColor; }
.border-black { border-color: rgb(0 0 0); }
.border-white { border-color: rgb(255 255 255); }
.border-gray-200 { border-color: rgb(229 231 235); }
.border-gray-300 { border-color: rgb(209 213 219); }
.border-gray-600 { border-color: rgb(75 85 99); }
.border-gray-700 { border-color: rgb(55 65 81); }
.border-green-200 { border-color: rgb(187 247 208); }
.border-green-300 { border-color: rgb(134 239 172); }
.border-green-700 { border-color: rgb(21 128 61); }
.border-green-800 { border-color: rgb(22 101 52); }
.border-blue-200 { border-color: rgb(191 219 254); }
.border-blue-800 { border-color: rgb(30 64 175); }
.border-orange-200 { border-color: rgb(254 215 170); }
.border-orange-800 { border-color: rgb(154 52 18); }

/* CSS Variable Colors */
.bg-background { background-color: hsl(var(--background)); }
.bg-foreground { background-color: hsl(var(--foreground)); }
.bg-card { background-color: hsl(var(--card)); }
.bg-card-foreground { background-color: hsl(var(--card-foreground)); }
.bg-popover { background-color: hsl(var(--popover)); }
.bg-popover-foreground { background-color: hsl(var(--popover-foreground)); }
.bg-primary { background-color: hsl(var(--primary)); }
.bg-primary-foreground { background-color: hsl(var(--primary-foreground)); }
.bg-secondary { background-color: hsl(var(--secondary)); }
.bg-secondary-foreground { background-color: hsl(var(--secondary-foreground)); }
.bg-muted { background-color: hsl(var(--muted)); }
.bg-muted-foreground { background-color: hsl(var(--muted-foreground)); }
.bg-accent { background-color: hsl(var(--accent)); }
.bg-accent-foreground { background-color: hsl(var(--accent-foreground)); }
.bg-destructive { background-color: hsl(var(--destructive)); }
.bg-destructive-foreground { background-color: hsl(var(--destructive-foreground)); }

.text-background { color: hsl(var(--background)); }
.text-foreground { color: hsl(var(--foreground)); }
.text-card { color: hsl(var(--card)); }
.text-card-foreground { color: hsl(var(--card-foreground)); }
.text-popover { color: hsl(var(--popover)); }
.text-popover-foreground { color: hsl(var(--popover-foreground)); }
.text-primary { color: hsl(var(--primary)); }
.text-primary-foreground { color: hsl(var(--primary-foreground)); }
.text-secondary { color: hsl(var(--secondary)); }
.text-secondary-foreground { color: hsl(var(--secondary-foreground)); }
.text-muted { color: hsl(var(--muted)); }
.text-muted-foreground { color: hsl(var(--muted-foreground)); }
.text-accent { color: hsl(var(--accent)); }
.text-accent-foreground { color: hsl(var(--accent-foreground)); }
.text-destructive { color: hsl(var(--destructive)); }
.text-destructive-foreground { color: hsl(var(--destructive-foreground)); }

.border-background { border-color: hsl(var(--background)); }
.border-foreground { border-color: hsl(var(--foreground)); }
.border-card { border-color: hsl(var(--card)); }
.border-card-foreground { border-color: hsl(var(--card-foreground)); }
.border-popover { border-color: hsl(var(--popover)); }
.border-popover-foreground { border-color: hsl(var(--popover-foreground)); }
.border-primary { border-color: hsl(var(--primary)); }
.border-primary-foreground { border-color: hsl(var(--primary-foreground)); }
.border-secondary { border-color: hsl(var(--secondary)); }
.border-secondary-foreground { border-color: hsl(var(--secondary-foreground)); }
.border-muted { border-color: hsl(var(--muted)); }
.border-muted-foreground { border-color: hsl(var(--muted-foreground)); }
.border-accent { border-color: hsl(var(--accent)); }
.border-accent-foreground { border-color: hsl(var(--accent-foreground)); }
.border-destructive { border-color: hsl(var(--destructive)); }
.border-destructive-foreground { border-color: hsl(var(--destructive-foreground)); }
.border-input { border-color: hsl(var(--input)); }
.border-ring { border-color: hsl(var(--ring)); }

/* Font Family */
.font-sans { font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; }
.font-serif { font-family: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif; }
.font-mono { font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace; }

/* Font Size */
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
.text-5xl { font-size: 3rem; line-height: 1; }
.text-6xl { font-size: 3.75rem; line-height: 1; }
.text-7xl { font-size: 4.5rem; line-height: 1; }
.text-8xl { font-size: 6rem; line-height: 1; }
.text-9xl { font-size: 8rem; line-height: 1; }

/* Font Weight */
.font-thin { font-weight: 100; }
.font-extralight { font-weight: 200; }
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }
.font-black { font-weight: 900; }

/* Line Height */
.leading-3 { line-height: .75rem; }
.leading-4 { line-height: 1rem; }
.leading-5 { line-height: 1.25rem; }
.leading-6 { line-height: 1.5rem; }
.leading-7 { line-height: 1.75rem; }
.leading-8 { line-height: 2rem; }
.leading-9 { line-height: 2.25rem; }
.leading-10 { line-height: 2.5rem; }
.leading-none { line-height: 1; }
.leading-tight { line-height: 1.25; }
.leading-snug { line-height: 1.375; }
.leading-normal { line-height: 1.5; }
.leading-relaxed { line-height: 1.625; }
.leading-loose { line-height: 2; }

/* Letter Spacing */
.tracking-tighter { letter-spacing: -0.05em; }
.tracking-tight { letter-spacing: -0.025em; }
.tracking-normal { letter-spacing: 0em; }
.tracking-wide { letter-spacing: 0.025em; }
.tracking-wider { letter-spacing: 0.05em; }
.tracking-widest { letter-spacing: 0.1em; }

/* Text Alignment */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

/* Text Transform */
.uppercase { text-transform: uppercase; }
.lowercase { text-transform: lowercase; }
.capitalize { text-transform: capitalize; }
.normal-case { text-transform: none; }

/* Text Decoration */
.underline { text-decoration-line: underline; }
.overline { text-decoration-line: overline; }
.line-through { text-decoration-line: line-through; }
.no-underline { text-decoration-line: none; }

/* Font Smoothing */
.antialiased { -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; }
.subpixel-antialiased { -webkit-font-smoothing: auto; -moz-osx-font-smoothing: auto; }

/* Box Shadow */
.shadow-sm { box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05); }
.shadow { box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1); }
.shadow-md { box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1); }
.shadow-lg { box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1); }
.shadow-xl { box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1); }
.shadow-2xl { box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25); }
.shadow-inner { box-shadow: inset 0 2px 4px 0 rgb(0 0 0 / 0.05); }
.shadow-none { box-shadow: 0 0 #0000; }

/* Opacity */
.opacity-0 { opacity: 0; }
.opacity-5 { opacity: 0.05; }
.opacity-10 { opacity: 0.1; }
.opacity-20 { opacity: 0.2; }
.opacity-25 { opacity: 0.25; }
.opacity-30 { opacity: 0.3; }
.opacity-40 { opacity: 0.4; }
.opacity-50 { opacity: 0.5; }
.opacity-60 { opacity: 0.6; }
.opacity-70 { opacity: 0.7; }
.opacity-75 { opacity: 0.75; }
.opacity-80 { opacity: 0.8; }
.opacity-90 { opacity: 0.9; }
.opacity-95 { opacity: 0.95; }
.opacity-100 { opacity: 1; }

/* Transition Property */
.transition-none { transition-property: none; }
.transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-opacity { transition-property: opacity; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-shadow { transition-property: box-shadow; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-transform { transition-property: transform; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }

/* Transition Duration */
.duration-75 { transition-duration: 75ms; }
.duration-100 { transition-duration: 100ms; }
.duration-150 { transition-duration: 150ms; }
.duration-200 { transition-duration: 200ms; }
.duration-300 { transition-duration: 300ms; }
.duration-500 { transition-duration: 500ms; }
.duration-700 { transition-duration: 700ms; }
.duration-1000 { transition-duration: 1000ms; }

/* Transform */
.transform { transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.transform-gpu { transform: translate3d(var(--tw-translate-x), var(--tw-translate-y), 0) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.transform-none { transform: none; }

/* Translate */
.-translate-y-1\/2 { --tw-translate-y: -50%; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

/* Rotate */
.rotate-0 { --tw-rotate: 0deg; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.rotate-90 { --tw-rotate: 90deg; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.rotate-180 { --tw-rotate: 180deg; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.-rotate-90 { --tw-rotate: -90deg; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

/* Scale */
.scale-0 { --tw-scale-x: 0; --tw-scale-y: 0; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.scale-50 { --tw-scale-x: .5; --tw-scale-y: .5; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.scale-75 { --tw-scale-x: .75; --tw-scale-y: .75; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.scale-90 { --tw-scale-x: .9; --tw-scale-y: .9; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.scale-95 { --tw-scale-x: .95; --tw-scale-y: .95; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.scale-100 { --tw-scale-x: 1; --tw-scale-y: 1; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.scale-105 { --tw-scale-x: 1.05; --tw-scale-y: 1.05; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.scale-110 { --tw-scale-x: 1.1; --tw-scale-y: 1.1; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.scale-125 { --tw-scale-x: 1.25; --tw-scale-y: 1.25; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.scale-150 { --tw-scale-x: 1.5; --tw-scale-y: 1.5; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

/* Cursor */
.cursor-auto { cursor: auto; }
.cursor-default { cursor: default; }
.cursor-pointer { cursor: pointer; }
.cursor-wait { cursor: wait; }
.cursor-text { cursor: text; }
.cursor-move { cursor: move; }
.cursor-help { cursor: help; }
.cursor-not-allowed { cursor: not-allowed; }

/* User Select */
.select-none { user-select: none; }
.select-text { user-select: text; }
.select-all { user-select: all; }
.select-auto { user-select: auto; }

/* Pointer Events */
.pointer-events-none { pointer-events: none; }
.pointer-events-auto { pointer-events: auto; }

/* Resize */
.resize-none { resize: none; }
.resize-y { resize: vertical; }
.resize-x { resize: horizontal; }
.resize { resize: both; }

/* Scroll Behavior */
.scroll-auto { scroll-behavior: auto; }
.scroll-smooth { scroll-behavior: smooth; }

/* Appearance */
.appearance-none { appearance: none; }

/* Outline */
.outline-none { outline: 2px solid transparent; outline-offset: 2px; }
.outline { outline-style: solid; }
.outline-dashed { outline-style: dashed; }
.outline-dotted { outline-style: dotted; }
.outline-double { outline-style: double; }

/* Ring Width */
.ring-0 { box-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color); }
.ring-1 { box-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color); }
.ring-2 { box-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color); }
.ring-4 { box-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color); }
.ring-8 { box-shadow: var(--tw-ring-inset) 0 0 0 calc(8px + var(--tw-ring-offset-width)) var(--tw-ring-color); }
.ring { box-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color); }
.ring-inset { --tw-ring-inset: inset; }

/* Ring Color */
.ring-slate-500 { --tw-ring-color: rgb(100 116 139); }
.ring-gray-500 { --tw-ring-color: rgb(107 114 128); }
.ring-zinc-500 { --tw-ring-color: rgb(113 113 122); }
.ring-neutral-500 { --tw-ring-color: rgb(115 115 115); }
.ring-stone-500 { --tw-ring-color: rgb(120 113 108); }
.ring-red-500 { --tw-ring-color: rgb(239 68 68); }
.ring-orange-500 { --tw-ring-color: rgb(249 115 22); }
.ring-amber-500 { --tw-ring-color: rgb(245 158 11); }
.ring-yellow-500 { --tw-ring-color: rgb(234 179 8); }
.ring-lime-500 { --tw-ring-color: rgb(132 204 22); }
.ring-green-500 { --tw-ring-color: rgb(34 197 94); }
.ring-emerald-500 { --tw-ring-color: rgb(16 185 129); }
.ring-teal-500 { --tw-ring-color: rgb(20 184 166); }
.ring-cyan-500 { --tw-ring-color: rgb(6 182 212); }
.ring-sky-500 { --tw-ring-color: rgb(14 165 233); }
.ring-blue-500 { --tw-ring-color: rgb(59 130 246); }
.ring-indigo-500 { --tw-ring-color: rgb(99 102 241); }
.ring-violet-500 { --tw-ring-color: rgb(139 92 246); }
.ring-purple-500 { --tw-ring-color: rgb(168 85 247); }
.ring-fuchsia-500 { --tw-ring-color: rgb(217 70 239); }
.ring-pink-500 { --tw-ring-color: rgb(236 72 153); }
.ring-rose-500 { --tw-ring-color: rgb(244 63 94); }

/* Ring Offset Width */
.ring-offset-0 { --tw-ring-offset-width: 0px; }
.ring-offset-1 { --tw-ring-offset-width: 1px; }
.ring-offset-2 { --tw-ring-offset-width: 2px; }
.ring-offset-4 { --tw-ring-offset-width: 4px; }
.ring-offset-8 { --tw-ring-offset-width: 8px; }

/* Ring Offset Color */
.ring-offset-background { --tw-ring-offset-color: hsl(var(--background)); }
.ring-offset-white { --tw-ring-offset-color: #fff; }
.ring-offset-black { --tw-ring-offset-color: #000; }

/* Focus Ring */
.focus\:ring-2:focus { box-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color); }
.focus\:ring-orange-500:focus { --tw-ring-color: rgb(249 115 22); }
.focus\:border-orange-500:focus { border-color: rgb(249 115 22); }

/* Hover States */
.hover\:bg-accent:hover { background-color: hsl(var(--accent)); }
.hover\:bg-primary\/90:hover { background-color: hsl(var(--primary) / 0.9); }
.hover\:bg-green-700:hover { background-color: rgb(21 128 61); }
.hover\:bg-orange-700:hover { background-color: rgb(194 65 12); }
.hover\:bg-gray-100:hover { background-color: rgb(243 244 246); }
.hover\:bg-gray-700:hover { background-color: rgb(55 65 81); }
.hover\:text-accent-foreground:hover { color: hsl(var(--accent-foreground)); }
.hover\:text-primary:hover { color: hsl(var(--primary)); }
.hover\:text-orange-600:hover { color: rgb(234 88 12); }
.hover\:text-orange-700:hover { color: rgb(194 65 12); }
.hover\:no-underline:hover { text-decoration-line: none; }
.hover\:shadow-md:hover { box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1); }

/* Focus States */
.focus\:bg-accent:focus { background-color: hsl(var(--accent)); }
.focus\:text-accent-foreground:focus { color: hsl(var(--accent-foreground)); }
.focus\:outline-none:focus { outline: 2px solid transparent; outline-offset: 2px; }
.focus\:ring-2:focus { box-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color); }
.focus\:ring-ring:focus { --tw-ring-color: hsl(var(--ring)); }
.focus\:ring-offset-2:focus { --tw-ring-offset-width: 2px; }

/* Focus Visible States */
.focus-visible\:outline-none:focus-visible { outline: 2px solid transparent; outline-offset: 2px; }
.focus-visible\:ring-2:focus-visible { box-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color); }
.focus-visible\:ring-ring:focus-visible { --tw-ring-color: hsl(var(--ring)); }
.focus-visible\:ring-offset-2:focus-visible { --tw-ring-offset-width: 2px; }

/* Disabled States */
.disabled\:pointer-events-none:disabled { pointer-events: none; }
.disabled\:opacity-50:disabled { opacity: 0.5; }
.disabled\:cursor-not-allowed:disabled { cursor: not-allowed; }

/* Grid */
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
.grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
.grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }

/* Column Span */
.col-span-1 { grid-column: span 1 / span 1; }
.col-span-2 { grid-column: span 2 / span 2; }
.col-span-3 { grid-column: span 3 / span 3; }
.col-span-4 { grid-column: span 4 / span 4; }
.col-span-5 { grid-column: span 5 / span 5; }
.col-span-6 { grid-column: span 6 / span 6; }
.col-span-full { grid-column: 1 / -1; }

/* List Style */
.list-none { list-style-type: none; }
.list-disc { list-style-type: disc; }
.list-decimal { list-style-type: decimal; }

/* Overflow */
.overflow-auto { overflow: auto; }
.overflow-hidden { overflow: hidden; }
.overflow-visible { overflow: visible; }
.overflow-scroll { overflow: scroll; }
.overflow-x-auto { overflow-x: auto; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-x-visible { overflow-x: visible; }
.overflow-x-scroll { overflow-x: scroll; }
.overflow-y-auto { overflow-y: auto; }
.overflow-y-hidden { overflow-y: hidden; }
.overflow-y-visible { overflow-y: visible; }
.overflow-y-scroll { overflow-y: scroll; }

/* Object Fit */
.object-contain { object-fit: contain; }
.object-cover { object-fit: cover; }
.object-fill { object-fit: fill; }
.object-none { object-fit: none; }
.object-scale-down { object-fit: scale-down; }

/* Object Position */
.object-bottom { object-position: bottom; }
.object-center { object-position: center; }
.object-left { object-position: left; }
.object-left-bottom { object-position: left bottom; }
.object-left-top { object-position: left top; }
.object-right { object-position: right; }
.object-right-bottom { object-position: right bottom; }
.object-right-top { object-position: right top; }
.object-top { object-position: top; }

/* Visibility */
.visible { visibility: visible; }
.invisible { visibility: hidden; }

/* Whitespace */
.whitespace-normal { white-space: normal; }
.whitespace-nowrap { white-space: nowrap; }
.whitespace-pre { white-space: pre; }
.whitespace-pre-line { white-space: pre-line; }
.whitespace-pre-wrap { white-space: pre-wrap; }

/* Word Break */
.break-normal { overflow-wrap: normal; word-break: normal; }
.break-words { overflow-wrap: break-word; }
.break-all { word-break: break-all; }

/* Content */
.content-none { content: none; }

/* Backdrop Filter */
.backdrop-blur-none { backdrop-filter: blur(0); }
.backdrop-blur-sm { backdrop-filter: blur(4px); }
.backdrop-blur { backdrop-filter: blur(8px); }
.backdrop-blur-md { backdrop-filter: blur(12px); }
.backdrop-blur-lg { backdrop-filter: blur(16px); }
.backdrop-blur-xl { backdrop-filter: blur(24px); }
.backdrop-blur-2xl { backdrop-filter: blur(40px); }
.backdrop-blur-3xl { backdrop-filter: blur(64px); }

/* Supports */
.supports-backdrop-blur\:bg-background\/60 { background-color: hsl(var(--background) / 0.6); }

/* Table */
.table { display: table; }
.table-caption { display: table-caption; }
.table-cell { display: table-cell; }
.table-column { display: table-column; }
.table-column-group { display: table-column-group; }
.table-footer-group { display: table-footer-group; }
.table-header-group { display: table-header-group; }
.table-row-group { display: table-row-group; }
.table-row { display: table-row; }

/* Border Collapse */
.border-collapse { border-collapse: collapse; }
.border-separate { border-collapse: separate; }

/* Divide */
.divide-y > :not([hidden]) ~ :not([hidden]) { --tw-divide-y-reverse: 0; border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse))); border-bottom-width: calc(1px * var(--tw-divide-y-reverse)); }
.divide-gray-200 > :not([hidden]) ~ :not([hidden]) { border-color: rgb(229 231 235); }
.divide-gray-700 > :not([hidden]) ~ :not([hidden]) { border-color: rgb(55 65 81); }

/* Interactive Elements */
button, input, select {
  font-family: inherit;
  font-size: 100%;
  font-weight: inherit;
  line-height: inherit;
  color: inherit;
  margin: 0;
  padding: 0;
}

button {
  cursor: pointer;
}

button:disabled {
  cursor: default;
}

/* Form Elements */
input, select {
  background-color: hsl(var(--background));
  border: 1px solid hsl(var(--input));
  border-radius: calc(var(--radius) - 2px);
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  transition: border-color 150ms ease-in-out, box-shadow 150ms ease-in-out;
}

input:focus, select:focus {
  outline: none;
  border-color: hsl(var(--ring));
  box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
}

input::placeholder {
  color: hsl(var(--muted-foreground));
}

/* Button Styles */
.inline-flex {
  display: inline-flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

/* Responsive Design */

/* Small screens (sm) - 640px and up */
@media (min-width: 640px) {
  .sm\:block { display: block; }
  .sm\:inline { display: inline; }
  .sm\:inline-block { display: inline-block; }
  .sm\:flex { display: flex; }
  .sm\:inline-flex { display: inline-flex; }
  .sm\:hidden { display: none; }
  .sm\:w-auto { width: auto; }
  .sm\:flex-row { flex-direction: row; }
  .sm\:flex-col { flex-direction: column; }
  .sm\:justify-start { justify-content: flex-start; }
  .sm\:justify-end { justify-content: flex-end; }
  .sm\:justify-center { justify-content: center; }
  .sm\:justify-between { justify-content: space-between; }
  .sm\:gap-x-2 { column-gap: 0.5rem; }
  .sm\:space-x-0 > :not([hidden]) ~ :not([hidden]) { --tw-space-x-reverse: 0; margin-right: calc(0px * var(--tw-space-x-reverse)); margin-left: calc(0px * calc(1 - var(--tw-space-x-reverse))); }
  .sm\:space-y-0 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: 0; margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse))); margin-bottom: calc(0px * var(--tw-space-y-reverse)); }
  .sm\:p-8 { padding: 2rem; }
  .sm\:px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
  .sm\:py-4 { padding-top: 1rem; padding-bottom: 1rem; }
  .sm\:pr-12 { padding-right: 3rem; }
  .sm\:text-sm { font-size: 0.875rem; line-height: 1.25rem; }
  .sm\:text-base { font-size: 1rem; line-height: 1.5rem; }
  .sm\:text-lg { font-size: 1.125rem; line-height: 1.75rem; }
  .sm\:text-xl { font-size: 1.25rem; line-height: 1.75rem; }
  .sm\:inline-block { display: inline-block; }
}

/* Medium screens (md) - 768px and up */
@media (min-width: 768px) {
  .md\:block { display: block; }
  .md\:inline { display: inline; }
  .md\:inline-block { display: inline-block; }
  .md\:flex { display: flex; }
  .md\:inline-flex { display: inline-flex; }
  .md\:grid { display: grid; }
  .md\:hidden { display: none; }
  .md\:w-auto { width: auto; }
  .md\:w-1\/2 { width: 50%; }
  .md\:w-1\/3 { width: 33.333333%; }
  .md\:w-2\/3 { width: 66.666667%; }
  .md\:w-40 { width: 10rem; }
  .md\:flex-row { flex-direction: row; }
  .md\:flex-col { flex-direction: column; }
  .md\:justify-start { justify-content: flex-start; }
  .md\:justify-end { justify-content: flex-end; }
  .md\:justify-center { justify-content: center; }
  .md\:justify-between { justify-content: space-between; }
  .md\:gap-8 { gap: 2rem; }
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\:space-y-0 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: 0; margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse))); margin-bottom: calc(0px * var(--tw-space-y-reverse)); }
  .md\:space-y-4 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: 0; margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse))); margin-bottom: calc(1rem * var(--tw-space-y-reverse)); }
  .md\:pt-12 { padding-top: 3rem; }
  .md\:text-5xl { font-size: 3rem; line-height: 1; }
  .md\:flex-none { flex: none; }
}

/* Large screens (lg) - 1024px and up */
@media (min-width: 1024px) {
  .lg\:block { display: block; }
  .lg\:inline { display: inline; }
  .lg\:inline-block { display: inline-block; }
  .lg\:flex { display: flex; }
  .lg\:inline-flex { display: inline-flex; }
  .lg\:grid { display: grid; }
  .lg\:hidden { display: none; }
  .lg\:w-1\/2 { width: 50%; }
  .lg\:w-64 { width: 16rem; }
  .lg\:flex-row { flex-direction: row; }
  .lg\:flex-col { flex-direction: column; }
  .lg\:grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
  .lg\:col-span-1 { grid-column: span 1 / span 1; }
  .lg\:leading-\[1\.1\] { line-height: 1.1; }
}

/* Extra large screens (xl) - 1280px and up */
@media (min-width: 1280px) {
  .xl\:block { display: block; }
  .xl\:inline { display: inline; }
  .xl\:flex { display: flex; }
  .xl\:hidden { display: none; }
}

/* 2X large screens (2xl) - 1536px and up */
@media (min-width: 1536px) {
  .\32xl\:block { display: block; }
  .\32xl\:inline { display: inline; }
  .\32xl\:flex { display: flex; }
  .\32xl\:hidden { display: none; }
}

/* Shadcn/UI Specific Classes */
.text-muted-foreground { color: hsl(var(--muted-foreground)); }
.hover\:text-primary:hover { color: hsl(var(--primary)); }
.data-\[active\]\:bg-accent\/50[data-active] { background-color: hsl(var(--accent) / 0.5); }
.data-\[state\=open\]\:bg-accent\/50[data-state="open"] { background-color: hsl(var(--accent) / 0.5); }
.data-\[state\=open\]\:rotate-180[data-state="open"] { --tw-rotate: 180deg; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.group-data-\[state\=open\]\:rotate-180 .group[data-state="open"] { --tw-rotate: 180deg; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.submenu-trigger { position: relative; }
.ring-offset-background { --tw-ring-offset-color: hsl(var(--background)); }
.focus-visible\:ring-ring:focus-visible { --tw-ring-color: hsl(var(--ring)); }
.peer-disabled\:cursor-not-allowed:disabled ~ * { cursor: not-allowed; }
.peer-disabled\:opacity-70:disabled ~ * { opacity: 0.7; }
.file\:border-0::file-selector-button { border-width: 0px; }
.file\:bg-transparent::file-selector-button { background-color: transparent; }
.file\:text-sm::file-selector-button { font-size: 0.875rem; line-height: 1.25rem; }
.file\:font-medium::file-selector-button { font-weight: 500; }
.placeholder\:text-muted-foreground::placeholder { color: hsl(var(--muted-foreground)); }
.rounded-l-none { border-top-left-radius: 0px; border-bottom-left-radius: 0px; }
.m-\[-1\] { margin: -1px; }
.top-\[1px\] { top: 1px; }
.h-\[1\.5rem\] { height: 1.5rem; }
.w-\[1\.3rem\] { width: 1.3rem; }
.h-\[1\.2rem\] { height: 1.2rem; }
.w-\[1\.2rem\] { width: 1.2rem; }
.dark\:hidden { display: none; }
.dark\:block { display: block; }
.dark\:text-white { color: rgb(255 255 255); }
.max-w-\[750px\] { max-width: 750px; }
.data-br { display: inline-block; vertical-align: top; text-decoration: inherit; }
.data-brr { text-wrap: balance; }
.text-wrap-balance { text-wrap: balance; }
.no-underline { text-decoration-line: none; }
.group-hover\:shadow-lg:hover .group { box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1); }
.hover\:shadow-orange-100:hover { box-shadow: 0 1px 3px 0 rgb(255 237 213 / 0.1), 0 1px 2px -1px rgb(255 237 213 / 0.1); }
.border-orange-100 { border-color: rgb(255 237 213); }
.hover\:border-orange-200:hover { border-color: rgb(254 215 170); }
.from-orange-100 { --tw-gradient-from: rgb(255 237 213); --tw-gradient-to: rgb(255 237 213 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.to-yellow-100 { --tw-gradient-to: rgb(254 249 195); }
.group-hover\:scale-110:hover .group { --tw-scale-x: 1.1; --tw-scale-y: 1.1; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.duration-300 { transition-duration: 300ms; }
.pr-8 { padding-right: 2rem; }
.space-y-1 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: 0; margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse))); margin-bottom: calc(0.25rem * var(--tw-space-y-reverse)); }
.space-y-1\.5 > :not([hidden]) ~ :not([hidden]) { --tw-space-y-reverse: 0; margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse))); margin-bottom: calc(0.375rem * var(--tw-space-y-reverse)); }
.rounded-full { border-radius: 9999px; }
.px-2\.5 { padding-left: 0.625rem; padding-right: 0.625rem; }
.py-0\.5 { padding-top: 0.125rem; padding-bottom: 0.125rem; }
.focus\:ring-2:focus { box-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color); }
.focus\:ring-ring:focus { --tw-ring-color: hsl(var(--ring)); }
.focus\:ring-offset-2:focus { --tw-ring-offset-width: 2px; }
.hover\:bg-secondary\/80:hover { background-color: hsl(var(--secondary) / 0.8); }
.bg-orange-50 { background-color: rgb(255 247 237); }
.text-orange-700 { color: rgb(194 65 12); }
.border-orange-200 { border-color: rgb(254 215 170); }
.leading-relaxed { line-height: 1.625; }
.from-orange-50 { --tw-gradient-from: rgb(255 247 237); --tw-gradient-to: rgb(255 247 237 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.to-yellow-50 { --tw-gradient-to: rgb(254 252 232); }
.text-orange-600 { color: rgb(234 88 12); }
.from-orange-500 { --tw-gradient-from: rgb(249 115 22); --tw-gradient-to: rgb(249 115 22 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.to-yellow-500 { --tw-gradient-to: rgb(234 179 8); }
.hover\:from-orange-600:hover { --tw-gradient-from: rgb(234 88 12); --tw-gradient-to: rgb(234 88 12 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.hover\:to-yellow-600:hover { --tw-gradient-to: rgb(202 138 4); }
.hover\:scale-105:hover { --tw-scale-x: 1.05; --tw-scale-y: 1.05; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.active\:scale-95:active { --tw-scale-x: 0.95; --tw-scale-y: 0.95; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.group-hover\:animate-pulse:hover .group { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
.inset-x-0 { left: 0px; right: 0px; }
.-bottom-px { bottom: -1px; }
.h-px { height: 1px; }
.from-transparent { --tw-gradient-from: transparent; --tw-gradient-to: rgb(0 0 0 / 0); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); }
.via-orange-300\/50 { --tw-gradient-to: rgb(253 186 116 / 0); --tw-gradient-stops: var(--tw-gradient-from), rgb(253 186 116 / 0.5), var(--tw-gradient-to); }
.to-transparent { --tw-gradient-to: transparent; }
.group-hover\:via-orange-400\/70:hover .group { --tw-gradient-to: rgb(251 146 60 / 0); --tw-gradient-stops: var(--tw-gradient-from), rgb(251 146 60 / 0.7), var(--tw-gradient-to); }
.flex-wrap { flex-wrap: wrap; }
.flex-grow { flex-grow: 1; }
.overflow-hidden { overflow: hidden; }
.rounded-\[0\.5rem\] { border-radius: 0.5rem; }
.max-w-screen-xl { max-width: 1280px; }
.\[&\>\>svg\~\*\]\:pl-7 > svg ~ * { padding-left: 1.75rem; }
.\[&\>\>svg\+div\]\:translate-y-\[-3px\] > svg + div { --tw-translate-y: -3px; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.\[&\>\>svg\]\:absolute > svg { position: absolute; }
.\[&\>\>svg\]\:left-4 > svg { left: 1rem; }
.\[&\>\>svg\]\:top-4 > svg { top: 1rem; }
.\[&\>\>svg\]\:text-foreground > svg { color: hsl(var(--foreground)); }
.\[&_p\]\:leading-relaxed p { line-height: 1.625; }
.underline-offset-4 { text-underline-offset: 4px; }
.shrink-0 { flex-shrink: 0; }
.data-\[state\=checked\]\:bg-primary[data-state="checked"] { background-color: hsl(var(--primary)); }
.data-\[state\=unchecked\]\:bg-input[data-state="unchecked"] { background-color: hsl(var(--input)); }
.data-\[state\=checked\]\:translate-x-5[data-state="checked"] { --tw-translate-x: 1.25rem; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.data-\[state\=unchecked\]\:translate-x-0[data-state="unchecked"] { --tw-translate-x: 0px; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.ring-0 { box-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color); }
.shadow-lg { box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1); }

/* Dark Mode */
.dark {
  color-scheme: dark;
}

.dark .bg-background { background-color: hsl(var(--background)); }
.dark .bg-foreground { background-color: hsl(var(--foreground)); }
.dark .bg-card { background-color: hsl(var(--card)); }
.dark .bg-card-foreground { background-color: hsl(var(--card-foreground)); }
.dark .bg-popover { background-color: hsl(var(--popover)); }
.dark .bg-popover-foreground { background-color: hsl(var(--popover-foreground)); }
.dark .bg-primary { background-color: hsl(var(--primary)); }
.dark .bg-primary-foreground { background-color: hsl(var(--primary-foreground)); }
.dark .bg-secondary { background-color: hsl(var(--secondary)); }
.dark .bg-secondary-foreground { background-color: hsl(var(--secondary-foreground)); }
.dark .bg-muted { background-color: hsl(var(--muted)); }
.dark .bg-muted-foreground { background-color: hsl(var(--muted-foreground)); }
.dark .bg-accent { background-color: hsl(var(--accent)); }
.dark .bg-accent-foreground { background-color: hsl(var(--accent-foreground)); }
.dark .bg-destructive { background-color: hsl(var(--destructive)); }
.dark .bg-destructive-foreground { background-color: hsl(var(--destructive-foreground)); }

.dark .text-background { color: hsl(var(--background)); }
.dark .text-foreground { color: hsl(var(--foreground)); }
.dark .text-card { color: hsl(var(--card)); }
.dark .text-card-foreground { color: hsl(var(--card-foreground)); }
.dark .text-popover { color: hsl(var(--popover)); }
.dark .text-popover-foreground { color: hsl(var(--popover-foreground)); }
.dark .text-primary { color: hsl(var(--primary)); }
.dark .text-primary-foreground { color: hsl(var(--primary-foreground)); }
.dark .text-secondary { color: hsl(var(--secondary)); }
.dark .text-secondary-foreground { color: hsl(var(--secondary-foreground)); }
.dark .text-muted { color: hsl(var(--muted)); }
.dark .text-muted-foreground { color: hsl(var(--muted-foreground)); }
.dark .text-accent { color: hsl(var(--accent)); }
.dark .text-accent-foreground { color: hsl(var(--accent-foreground)); }
.dark .text-destructive { color: hsl(var(--destructive)); }
.dark .text-destructive-foreground { color: hsl(var(--destructive-foreground)); }

.dark .border-background { border-color: hsl(var(--background)); }
.dark .border-foreground { border-color: hsl(var(--foreground)); }
.dark .border-card { border-color: hsl(var(--card)); }
.dark .border-card-foreground { border-color: hsl(var(--card-foreground)); }
.dark .border-popover { border-color: hsl(var(--popover)); }
.dark .border-popover-foreground { border-color: hsl(var(--popover-foreground)); }
.dark .border-primary { border-color: hsl(var(--primary)); }
.dark .border-primary-foreground { border-color: hsl(var(--primary-foreground)); }
.dark .border-secondary { border-color: hsl(var(--secondary)); }
.dark .border-secondary-foreground { border-color: hsl(var(--secondary-foreground)); }
.dark .border-muted { border-color: hsl(var(--muted)); }
.dark .border-muted-foreground { border-color: hsl(var(--muted-foreground)); }
.dark .border-accent { border-color: hsl(var(--accent)); }
.dark .border-accent-foreground { border-color: hsl(var(--accent-foreground)); }
.dark .border-destructive { border-color: hsl(var(--destructive)); }
.dark .border-destructive-foreground { border-color: hsl(var(--destructive-foreground)); }
.dark .border-input { border-color: hsl(var(--input)); }
.dark .border-ring { border-color: hsl(var(--ring)); }

.dark .bg-gray-800 { background-color: rgb(31 41 55); }
.dark .bg-gray-900 { background-color: rgb(17 24 39); }
.dark .bg-gray-700 { background-color: rgb(55 65 81); }
.dark .text-white { color: rgb(255 255 255); }
.dark .text-gray-300 { color: rgb(209 213 219); }
.dark .text-gray-400 { color: rgb(156 163 175); }
.dark .border-gray-600 { border-color: rgb(75 85 99); }
.dark .border-gray-700 { border-color: rgb(55 65 81); }

.dark .bg-green-900\/30 { background-color: rgb(20 83 45 / 0.3); }
.dark .border-green-800\/30 { border-color: rgb(22 101 52 / 0.3); }
.dark .text-green-400 { color: rgb(74 222 128); }
.dark .hover\:bg-green-800\/30:hover { background-color: rgb(22 101 52 / 0.3); }

.dark .bg-blue-900\/20 { background-color: rgb(30 58 138 / 0.2); }
.dark .border-blue-800 { border-color: rgb(30 64 175); }
.dark .text-blue-100 { color: rgb(219 234 254); }
.dark .text-blue-200 { color: rgb(191 219 254); }

.dark .bg-green-900\/20 { background-color: rgb(20 83 45 / 0.2); }
.dark .border-green-800 { border-color: rgb(22 101 52); }
.dark .text-green-100 { color: rgb(220 252 231); }
.dark .text-green-200 { color: rgb(187 247 208); }
.dark .text-green-300 { color: rgb(134 239 172); }

.dark .bg-orange-900\/20 { background-color: rgb(154 52 18 / 0.2); }
.dark .border-orange-800 { border-color: rgb(154 52 18); }

.dark .bg-yellow-900\/20 { background-color: rgb(133 77 14 / 0.2); }

.dark .divide-gray-700 > :not([hidden]) ~ :not([hidden]) { border-color: rgb(55 65 81); }

.dark .-rotate-90 { --tw-rotate: -90deg; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.dark .scale-0 { --tw-scale-x: 0; --tw-scale-y: 0; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.dark .rotate-0 { --tw-rotate: 0deg; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.dark .scale-100 { --tw-scale-x: 1; --tw-scale-y: 1; transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }

/* Prose (for content) */
.prose { color: var(--tw-prose-body); max-width: 65ch; }
.prose-gray { --tw-prose-body: rgb(55 65 81); --tw-prose-headings: rgb(17 24 39); --tw-prose-lead: rgb(75 85 99); --tw-prose-links: rgb(17 24 39); --tw-prose-bold: rgb(17 24 39); --tw-prose-counters: rgb(107 114 128); --tw-prose-bullets: rgb(209 213 219); --tw-prose-hr: rgb(229 231 235); --tw-prose-quotes: rgb(17 24 39); --tw-prose-quote-borders: rgb(229 231 235); --tw-prose-captions: rgb(107 114 128); --tw-prose-code: rgb(17 24 39); --tw-prose-pre-code: rgb(229 231 235); --tw-prose-pre-bg: rgb(17 24 39); --tw-prose-th-borders: rgb(209 213 219); --tw-prose-td-borders: rgb(229 231 235); --tw-prose-invert-body: rgb(209 213 219); --tw-prose-invert-headings: rgb(255 255 255); --tw-prose-invert-lead: rgb(156 163 175); --tw-prose-invert-links: rgb(255 255 255); --tw-prose-invert-bold: rgb(255 255 255); --tw-prose-invert-counters: rgb(156 163 175); --tw-prose-invert-bullets: rgb(75 85 99); --tw-prose-invert-hr: rgb(55 65 81); --tw-prose-invert-quotes: rgb(243 244 246); --tw-prose-invert-quote-borders: rgb(55 65 81); --tw-prose-invert-captions: rgb(156 163 175); --tw-prose-invert-code: rgb(255 255 255); --tw-prose-invert-pre-code: rgb(209 213 219); --tw-prose-invert-pre-bg: rgb(0 0 0 / 50%); --tw-prose-invert-th-borders: rgb(75 85 99); --tw-prose-invert-td-borders: rgb(55 65 81); }
.dark .prose-invert { --tw-prose-body: var(--tw-prose-invert-body); --tw-prose-headings: var(--tw-prose-invert-headings); --tw-prose-lead: var(--tw-prose-invert-lead); --tw-prose-links: var(--tw-prose-invert-links); --tw-prose-bold: var(--tw-prose-invert-bold); --tw-prose-counters: var(--tw-prose-invert-counters); --tw-prose-bullets: var(--tw-prose-invert-bullets); --tw-prose-hr: var(--tw-prose-invert-hr); --tw-prose-quotes: var(--tw-prose-invert-quotes); --tw-prose-quote-borders: var(--tw-prose-invert-quote-borders); --tw-prose-captions: var(--tw-prose-invert-captions); --tw-prose-code: var(--tw-prose-invert-code); --tw-prose-pre-code: var(--tw-prose-invert-pre-code); --tw-prose-pre-bg: var(--tw-prose-invert-pre-bg); --tw-prose-th-borders: var(--tw-prose-invert-th-borders); --tw-prose-td-borders: var(--tw-prose-invert-td-borders); }

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Additional Utility Classes */
.supports-backdrop-blur\:bg-background\/60 {
  background-color: hsl(var(--background) / 0.6);
}

@supports (backdrop-filter: blur(1px)) {
  .supports-backdrop-blur\:bg-background\/60 {
    background-color: hsl(var(--background) / 0.6);
  }
}

/* Custom Gradient Classes */
.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

/* Transform Variables */
:root {
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x: ;
  --tw-pan-y: ;
  --tw-pinch-zoom: ;
  --tw-scroll-snap-strictness: proximity;
  --tw-ordinal: ;
  --tw-slashed-zero: ;
  --tw-numeric-figure: ;
  --tw-numeric-spacing: ;
  --tw-numeric-fraction: ;
  --tw-ring-inset: ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur: ;
  --tw-brightness: ;
  --tw-contrast: ;
  --tw-grayscale: ;
  --tw-hue-rotate: ;
  --tw-invert: ;
  --tw-saturate: ;
  --tw-sepia: ;
  --tw-drop-shadow: ;
  --tw-backdrop-blur: ;
  --tw-backdrop-brightness: ;
  --tw-backdrop-contrast: ;
  --tw-backdrop-grayscale: ;
  --tw-backdrop-hue-rotate: ;
  --tw-backdrop-invert: ;
  --tw-backdrop-opacity: ;
  --tw-backdrop-saturate: ;
  --tw-backdrop-sepia: ;
}

/* Additional Missing Classes */
.group-hover\:translate-x-0\.5:hover .group {
  --tw-translate-x: 0.125rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.h-\[24px\] { height: 24px; }
.w-\[44px\] { width: 44px; }

/* Fix for dark mode in specific contexts */
.dark .dark\:hidden {
  display: none;
}

.dark .dark\:block {
  display: block;
}

/* Calculator Specific Styles */
.calculator-fields {
  display: block;
}

.calculator-fields.hidden {
  display: none;
}

/* Table Styles */
table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

th, td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid hsl(var(--border));
}

th {
  font-weight: 500;
  color: hsl(var(--muted-foreground));
  background-color: hsl(var(--muted));
}

tr:hover {
  background-color: hsl(var(--muted) / 0.5);
}

/* Dark Mode */
.dark .bg-green-100\/90 { background-color: rgb(20 83 45 / 0.3); }
.dark .border-green-200 { border-color: rgb(20 83 45 / 0.3); }
.dark .text-green-400 { color: rgb(74 222 128); }
.dark .hover\:bg-green-800\/30:hover { background-color: rgb(22 101 52 / 0.3); }

/* Backdrop Blur */
.backdrop-blur {
  backdrop-filter: blur(8px);
}

.supports-backdrop-blur\:bg-background\/60 {
  background-color: hsl(var(--background) / 0.6);
}

/* Overflow */
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }

/* Text Alignment */
.text-center { text-align: center; }

/* Max Width */
.max-w-\[750px\] { max-width: 750px; }
.max-w-screen-xl { max-width: 1280px; }

/* Flex Wrap */
.flex-wrap { flex-wrap: wrap; }

/* Flex Grow */
.flex-grow { flex-grow: 1; }

/* List Styles */
.list-none { list-style: none; }

/* Ring Offset */
.ring-offset-background { --tw-ring-offset-color: hsl(var(--background)); }

/* Data Attributes */
[data-active] { background-color: hsl(var(--accent) / 0.5); }
[data-state="open"] { background-color: hsl(var(--accent) / 0.5); }

/* Navigation dropdown styles */
.dropdown-content {
  animation: fadeIn 0.15s ease-out;
  z-index: 50;
  transition: opacity 0.15s ease-out, transform 0.15s ease-out;
}

.dropdown-content.hidden {
  opacity: 0;
  transform: translateY(-10px);
  pointer-events: none;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Submenu trigger arrow rotation */
.submenu-trigger[data-state="open"] svg {
  transform: rotate(180deg);
}

.submenu-trigger svg {
  transition: transform 0.15s ease;
}

/* Navigation hover area */
.submenu-trigger:hover {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

/* Ensure dropdown stays visible when hovering */
nav li {
  position: relative;
}

nav li:hover .dropdown-content {
  display: block;
}

/* Dark mode dropdown styles */
.dark .dropdown-content {
  background-color: rgb(31 41 55);
  border-color: rgb(55 65 81);
}

.dark .dropdown-content a {
  color: rgb(209 213 219);
}

.dark .dropdown-content a:hover {
  color: hsl(var(--primary));
}
