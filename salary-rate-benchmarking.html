<!DOCTYPE html>
<html lang="en" class="light" style="color-scheme: light;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Salary Rate Benchmarking | Employee Salary Calculator | PayCal Australia</title>
    <meta name="description" content="Compare your salary against Australian market standards. Find out what employees in your industry, role, and experience level are earning across Australia.">
    <meta name="keywords" content="salary benchmarking,salary calculator,market salary,employee salary,salary comparison,australian salaries,pay scale,salary survey">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        border: "hsl(var(--border))",
                        input: "hsl(var(--input))",
                        ring: "hsl(var(--ring))",
                        background: "hsl(var(--background))",
                        foreground: "hsl(var(--foreground))",
                        primary: {
                            DEFAULT: "hsl(var(--primary))",
                            foreground: "hsl(var(--primary-foreground))",
                        },
                        secondary: {
                            DEFAULT: "hsl(var(--secondary))",
                            foreground: "hsl(var(--secondary-foreground))",
                        },
                        muted: {
                            DEFAULT: "hsl(var(--muted))",
                            foreground: "hsl(var(--muted-foreground))",
                        },
                    },
                }
            }
        }
    </script>
    <style>
        :root {
            --background: 0 0% 100%;
            --foreground: 222.2 84% 4.9%;
            --card: 0 0% 100%;
            --card-foreground: 222.2 84% 4.9%;
            --primary: 222.2 47.4% 11.2%;
            --primary-foreground: 210 40% 98%;
            --secondary: 210 40% 96%;
            --secondary-foreground: 222.2 84% 4.9%;
            --muted: 210 40% 96%;
            --muted-foreground: 215.4 16.3% 46.9%;
            --border: 214.3 31.8% 91.4%;
            --input: 214.3 31.8% 91.4%;
            --ring: 222.2 84% 4.9%;
            --radius: 0.5rem;
        }

        * {
            border-color: hsl(var(--border));
        }

        body {
            background-color: hsl(var(--background));
            color: hsl(var(--foreground));
        }

        .calculator-container {
            background: hsl(var(--card));
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
        }

        .input-group {
            margin-bottom: 1rem;
        }

        .input-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: hsl(var(--foreground));
        }

        .input-group input, .input-group select {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
            background: hsl(var(--background));
            color: hsl(var(--foreground));
        }

        .input-group input:focus, .input-group select:focus {
            outline: none;
            border-color: hsl(var(--ring));
            box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
        }

        .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid hsl(var(--border));
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .result-label {
            font-weight: 500;
            color: hsl(var(--foreground));
        }

        .result-value {
            font-weight: 600;
            color: hsl(var(--primary));
            font-size: 1.1rem;
        }

        .salary-range {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin: 1rem 0;
        }

        .salary-bar {
            flex: 1;
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            position: relative;
        }

        .salary-indicator {
            height: 100%;
            background: linear-gradient(90deg, #ef4444, #f59e0b, #16a34a);
            border-radius: 4px;
            position: relative;
        }

        .salary-marker {
            position: absolute;
            top: -4px;
            width: 16px;
            height: 16px;
            background: #1f2937;
            border-radius: 50%;
            transform: translateX(-50%);
        }

        .percentile-info {
            background: hsl(var(--muted));
            padding: 1rem;
            border-radius: var(--radius);
            margin-top: 1rem;
        }

        @media (max-width: 768px) {
            .calculator-container {
                padding: 1rem;
            }
        }
    </style>
</head>

<body class="min-h-screen bg-background font-sans antialiased">
    <div class="relative flex min-h-screen flex-col">
        <!-- PayCal Australia Header -->
        <div class="bg-orange-100/90 border-b border-orange-200 transition-colors duration-500">
            <div class="container mx-auto px-4 py-3">
                <div class="flex items-center justify-between gap-x-4">
                    <div class="flex items-center gap-x-2 flex-1">
                        <span class="hidden sm:inline">💰 Compare!</span>
                        <span class="text-sm sm:text-base">Check <a class="font-semibold text-orange-600 hover:underline" href="#calculator">market salary rates</a><span class="hidden sm:inline"> - Know your worth!</span></span>
                    </div>
                    <div class="flex items-center gap-x-1 sm:gap-x-2">
                        <a class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-9 rounded-md px-3 hidden sm:inline-flex border-orange-300 hover:bg-orange-200/70" href="#calculator">Check Salary</a>
                        <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-8 w-8 hover:bg-orange-200/70" onclick="dismissBanner()">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                                <path d="M18 6 6 18"></path>
                                <path d="m6 6 12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <main class="flex-1">
            <div class="container mx-auto px-4 py-8">
                <!-- Hero Section -->
                <div class="text-center mb-8">
                    <h1 class="text-4xl font-bold tracking-tight text-foreground mb-4">
                        Salary Rate Benchmarking
                    </h1>
                    <p class="text-xl text-muted-foreground max-w-2xl mx-auto">
                        Compare your salary against Australian market standards. Find out what employees in your industry, role, and experience level are earning.
                    </p>
                </div>

                <!-- Calculator Section -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto" id="calculator">
                    <!-- Input Form -->
                    <div class="calculator-container p-6">
                        <h2 class="text-2xl font-semibold mb-4">Your Details</h2>
                        
                        <form class="grid gap-4" id="salary-form">
                            <!-- Industry -->
                            <div class="input-group">
                                <label for="industry">Industry</label>
                                <select 
                                    id="industry"
                                    class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                    onchange="calculateSalaryBenchmark()"
                                >
                                    <option value="it">Information Technology</option>
                                    <option value="finance">Finance & Banking</option>
                                    <option value="healthcare">Healthcare</option>
                                    <option value="education">Education</option>
                                    <option value="engineering">Engineering</option>
                                    <option value="marketing">Marketing & Sales</option>
                                    <option value="legal">Legal Services</option>
                                    <option value="consulting">Consulting</option>
                                    <option value="government">Government</option>
                                    <option value="retail">Retail & Hospitality</option>
                                </select>
                            </div>

                            <!-- Job Level -->
                            <div class="input-group">
                                <label for="jobLevel">Job Level</label>
                                <select 
                                    id="jobLevel"
                                    class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                    onchange="calculateSalaryBenchmark()"
                                >
                                    <option value="entry">Entry Level (0-2 years)</option>
                                    <option value="junior">Junior (2-4 years)</option>
                                    <option value="mid" selected>Mid-level (4-7 years)</option>
                                    <option value="senior">Senior (7-12 years)</option>
                                    <option value="lead">Lead/Manager (12+ years)</option>
                                    <option value="executive">Executive/Director</option>
                                </select>
                            </div>

                            <!-- Location -->
                            <div class="input-group">
                                <label for="location">Location</label>
                                <select 
                                    id="location"
                                    class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                    onchange="calculateSalaryBenchmark()"
                                >
                                    <option value="sydney">Sydney</option>
                                    <option value="melbourne">Melbourne</option>
                                    <option value="brisbane">Brisbane</option>
                                    <option value="perth">Perth</option>
                                    <option value="adelaide">Adelaide</option>
                                    <option value="canberra">Canberra</option>
                                    <option value="regional">Regional Australia</option>
                                </select>
                            </div>

                            <!-- Company Size -->
                            <div class="input-group">
                                <label for="companySize">Company Size</label>
                                <select 
                                    id="companySize"
                                    class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                    onchange="calculateSalaryBenchmark()"
                                >
                                    <option value="startup">Startup (< 50 employees)</option>
                                    <option value="small">Small (50-200 employees)</option>
                                    <option value="medium" selected>Medium (200-1000 employees)</option>
                                    <option value="large">Large (1000+ employees)</option>
                                    <option value="enterprise">Enterprise (5000+ employees)</option>
                                </select>
                            </div>

                            <!-- Education Level -->
                            <div class="input-group">
                                <label for="education">Education Level</label>
                                <select 
                                    id="education"
                                    class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                    onchange="calculateSalaryBenchmark()"
                                >
                                    <option value="highschool">High School</option>
                                    <option value="certificate">Certificate/Diploma</option>
                                    <option value="bachelor" selected>Bachelor's Degree</option>
                                    <option value="master">Master's Degree</option>
                                    <option value="phd">PhD/Doctorate</option>
                                </select>
                            </div>

                            <!-- Current Salary (Optional) -->
                            <div class="input-group">
                                <label for="currentSalary">Your Current Salary (Optional)</label>
                                <div class="flex border h-10 w-full rounded-md border-input bg-background">
                                    <span class="flex items-center px-3 h-10">$</span>
                                    <input 
                                        type="number" 
                                        id="currentSalary"
                                        class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-l-none m-[-1]"
                                        min="0"
                                        placeholder="Enter your current annual salary"
                                        oninput="calculateSalaryBenchmark()"
                                    />
                                    <span class="flex items-center px-3 h-10">/year</span>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Results Section -->
                    <div class="calculator-container p-6">
                        <h2 class="text-2xl font-semibold mb-4">Market Benchmark</h2>
                        
                        <!-- Salary Range Visualization -->
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold mb-3">Salary Range Distribution</h3>
                            <div class="salary-range">
                                <span class="text-sm font-medium">$50k</span>
                                <div class="salary-bar">
                                    <div class="salary-indicator" style="width: 100%;">
                                        <div class="salary-marker" id="salary-marker" style="left: 50%;"></div>
                                    </div>
                                </div>
                                <span class="text-sm font-medium">$150k</span>
                            </div>
                            <div class="flex justify-between text-xs text-muted-foreground mt-1">
                                <span>25th Percentile</span>
                                <span>Median</span>
                                <span>75th Percentile</span>
                            </div>
                        </div>

                        <div class="results-container" id="salary-results">
                            <div class="result-item">
                                <span class="result-label">25th Percentile</span>
                                <span class="result-value" id="percentile-25">$65,000</span>
                            </div>
                            <div class="result-item">
                                <span class="result-label">Median (50th)</span>
                                <span class="result-value" id="median-salary">$85,000</span>
                            </div>
                            <div class="result-item">
                                <span class="result-label">75th Percentile</span>
                                <span class="result-value" id="percentile-75">$110,000</span>
                            </div>
                            <div class="result-item border-t-2 border-primary pt-4">
                                <span class="result-label font-semibold">Market Average</span>
                                <span class="result-value text-xl font-bold" id="market-average">$88,000</span>
                            </div>
                            <div class="result-item">
                                <span class="result-label">Total Package (incl. Super)</span>
                                <span class="result-value" id="total-package">$98,120</span>
                            </div>
                        </div>

                        <!-- Salary Comparison -->
                        <div class="mt-6" id="salary-comparison" style="display: none;">
                            <h4 class="font-semibold mb-2">Your Salary Analysis</h4>
                            <div class="p-4 rounded-lg" id="comparison-result">
                                <p class="text-sm" id="comparison-text"></p>
                                <p class="text-xs mt-2 text-muted-foreground" id="percentile-rank"></p>
                            </div>
                        </div>

                        <!-- Market Insights -->
                        <div class="percentile-info">
                            <h4 class="font-semibold mb-2">Market Insights</h4>
                            <div class="text-sm text-muted-foreground" id="market-insights">
                                <p>• IT professionals in Sydney typically earn 15% above national average</p>
                                <p>• Mid-level positions show strong growth potential</p>
                                <p>• Large companies offer 10-20% higher base salaries</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="border-t bg-muted/50">
            <div class="container mx-auto px-4 py-8">
                <div class="text-center">
                    <p class="text-sm text-muted-foreground">
                        © 2024 PayCal.com.au. Made for Aussies ❤️
                    </p>
                </div>
            </div>
        </footer>
    </div>

    <script>
        // Salary data by industry and job level (annual salaries in AUD)
        const SALARY_DATA = {
            it: {
                entry: [55000, 65000, 75000],
                junior: [65000, 80000, 95000],
                mid: [80000, 100000, 120000],
                senior: [100000, 130000, 160000],
                lead: [130000, 160000, 200000],
                executive: [180000, 250000, 350000]
            },
            finance: {
                entry: [50000, 60000, 70000],
                junior: [60000, 75000, 90000],
                mid: [75000, 95000, 115000],
                senior: [95000, 125000, 155000],
                lead: [125000, 155000, 195000],
                executive: [170000, 240000, 320000]
            },
            healthcare: {
                entry: [45000, 55000, 65000],
                junior: [55000, 70000, 85000],
                mid: [70000, 90000, 110000],
                senior: [90000, 115000, 140000],
                lead: [115000, 145000, 180000],
                executive: [160000, 220000, 300000]
            },
            engineering: {
                entry: [60000, 70000, 80000],
                junior: [70000, 85000, 100000],
                mid: [85000, 105000, 125000],
                senior: [105000, 135000, 165000],
                lead: [135000, 170000, 210000],
                executive: [190000, 260000, 350000]
            }
        };

        // Multipliers
        const LOCATION_MULTIPLIERS = {
            sydney: 1.15, melbourne: 1.10, brisbane: 0.95, perth: 0.90,
            adelaide: 0.85, canberra: 1.05, regional: 0.80
        };

        const COMPANY_MULTIPLIERS = {
            startup: 0.85, small: 0.95, medium: 1.00, large: 1.15, enterprise: 1.25
        };

        const EDUCATION_MULTIPLIERS = {
            highschool: 0.90, certificate: 0.95, bachelor: 1.00, master: 1.10, phd: 1.20
        };

        // Initialize calculator
        document.addEventListener('DOMContentLoaded', function() {
            calculateSalaryBenchmark();
        });

        // Calculate salary benchmark
        function calculateSalaryBenchmark() {
            const industry = document.getElementById('industry').value;
            const jobLevel = document.getElementById('jobLevel').value;
            const location = document.getElementById('location').value;
            const companySize = document.getElementById('companySize').value;
            const education = document.getElementById('education').value;
            const currentSalary = parseFloat(document.getElementById('currentSalary').value) || 0;

            // Get base salary data
            let baseSalaries = [65000, 85000, 110000]; // Default

            if (SALARY_DATA[industry] && SALARY_DATA[industry][jobLevel]) {
                baseSalaries = SALARY_DATA[industry][jobLevel];
            }

            // Apply multipliers
            const locationMultiplier = LOCATION_MULTIPLIERS[location] || 1.0;
            const companyMultiplier = COMPANY_MULTIPLIERS[companySize] || 1.0;
            const educationMultiplier = EDUCATION_MULTIPLIERS[education] || 1.0;

            const totalMultiplier = locationMultiplier * companyMultiplier * educationMultiplier;

            // Calculate adjusted salaries
            const percentile25 = Math.round(baseSalaries[0] * totalMultiplier);
            const medianSalary = Math.round(baseSalaries[1] * totalMultiplier);
            const percentile75 = Math.round(baseSalaries[2] * totalMultiplier);
            const marketAverage = Math.round((percentile25 + medianSalary + percentile75) / 3);
            const totalPackage = Math.round(marketAverage * 1.115); // Including 11.5% super

            // Update display
            document.getElementById('percentile-25').textContent = formatSalary(percentile25);
            document.getElementById('median-salary').textContent = formatSalary(medianSalary);
            document.getElementById('percentile-75').textContent = formatSalary(percentile75);
            document.getElementById('market-average').textContent = formatSalary(marketAverage);
            document.getElementById('total-package').textContent = formatSalary(totalPackage);

            // Update visualization
            updateSalaryVisualization(percentile25, medianSalary, percentile75);

            // Update market insights
            updateMarketInsights(industry, location, companySize);

            // Show salary comparison if current salary is provided
            if (currentSalary > 0) {
                showSalaryComparison(currentSalary, percentile25, medianSalary, percentile75);
            } else {
                document.getElementById('salary-comparison').style.display = 'none';
            }
        }

        // Update salary visualization
        function updateSalaryVisualization(p25, median, p75) {
            const marker = document.getElementById('salary-marker');
            const range = p75 - p25;
            const medianPosition = ((median - p25) / range) * 100;

            marker.style.left = `${medianPosition}%`;

            // Update range labels
            const rangeContainer = document.querySelector('.salary-range');
            const labels = rangeContainer.querySelectorAll('span');
            labels[0].textContent = formatSalary(p25, true);
            labels[1].textContent = formatSalary(p75, true);
        }

        // Show salary comparison
        function showSalaryComparison(currentSalary, p25, median, p75) {
            const comparisonDiv = document.getElementById('salary-comparison');
            const resultDiv = document.getElementById('comparison-result');
            const textElement = document.getElementById('comparison-text');
            const percentileElement = document.getElementById('percentile-rank');

            let message = '';
            let className = '';
            let percentileRank = '';

            if (currentSalary < p25) {
                message = `Your salary of ${formatSalary(currentSalary)} is below the 25th percentile. Consider negotiating for an increase.`;
                className = 'bg-red-50 border border-red-200 text-red-800';
                percentileRank = 'You earn less than 75% of professionals in similar roles.';
            } else if (currentSalary >= p25 && currentSalary < median) {
                message = `Your salary of ${formatSalary(currentSalary)} is between the 25th and 50th percentile.`;
                className = 'bg-yellow-50 border border-yellow-200 text-yellow-800';
                percentileRank = 'You earn more than 25-50% of professionals in similar roles.';
            } else if (currentSalary >= median && currentSalary <= p75) {
                message = `Your salary of ${formatSalary(currentSalary)} is competitive, between median and 75th percentile.`;
                className = 'bg-green-50 border border-green-200 text-green-800';
                percentileRank = 'You earn more than 50-75% of professionals in similar roles.';
            } else {
                message = `Your salary of ${formatSalary(currentSalary)} is above the 75th percentile. Excellent!`;
                className = 'bg-blue-50 border border-blue-200 text-blue-800';
                percentileRank = 'You earn more than 75% of professionals in similar roles.';
            }

            textElement.textContent = message;
            percentileElement.textContent = percentileRank;
            resultDiv.className = `p-4 rounded-lg ${className}`;
            comparisonDiv.style.display = 'block';
        }

        // Update market insights
        function updateMarketInsights(industry, location, companySize) {
            const insights = document.getElementById('market-insights');
            const industryName = document.getElementById('industry').selectedOptions[0].text;
            const locationName = document.getElementById('location').selectedOptions[0].text;
            const companySizeName = document.getElementById('companySize').selectedOptions[0].text;

            const insightsList = [
                `• ${industryName} professionals in ${locationName} show strong market demand`,
                `• ${companySizeName} companies typically offer competitive packages`,
                `• Consider skills development for higher salary brackets`,
                `• Annual salary reviews are common in this industry`
            ];

            insights.innerHTML = insightsList.map(insight => `<p>${insight}</p>`).join('');
        }

        // Format salary
        function formatSalary(amount, short = false) {
            if (short && amount >= 1000) {
                return `$${Math.round(amount / 1000)}k`;
            }
            return new Intl.NumberFormat('en-AU', {
                style: 'currency',
                currency: 'AUD',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(amount);
        }

        // Dismiss banner
        function dismissBanner() {
            const banner = document.querySelector('.bg-orange-100\\/90');
            if (banner) banner.style.display = 'none';
        }
    </script>
</body>
</html>
