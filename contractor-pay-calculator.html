<!DOCTYPE html>
<html lang="en" class="light" style="color-scheme: light;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contractor Pay Calculator | ABN Tax Calculator | PayCal Australia</title>
    <meta name="description" content="Free contractor pay calculator for Australian contractors and freelancers. Calculate your take-home pay, tax obligations, GST, and superannuation as a sole trader or company contractor.">
    <meta name="keywords" content="contractor pay calculator,abn calculator,contractor tax calculator,sole trader calculator,freelancer calculator,contractor income calculator,gst calculator,contractor superannuation,australian contractor tax">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        border: "hsl(var(--border))",
                        input: "hsl(var(--input))",
                        ring: "hsl(var(--ring))",
                        background: "hsl(var(--background))",
                        foreground: "hsl(var(--foreground))",
                        primary: {
                            DEFAULT: "hsl(var(--primary))",
                            foreground: "hsl(var(--primary-foreground))",
                        },
                        secondary: {
                            DEFAULT: "hsl(var(--secondary))",
                            foreground: "hsl(var(--secondary-foreground))",
                        },
                        destructive: {
                            DEFAULT: "hsl(var(--destructive))",
                            foreground: "hsl(var(--destructive-foreground))",
                        },
                        muted: {
                            DEFAULT: "hsl(var(--muted))",
                            foreground: "hsl(var(--muted-foreground))",
                        },
                        accent: {
                            DEFAULT: "hsl(var(--accent))",
                            foreground: "hsl(var(--accent-foreground))",
                        },
                        popover: {
                            DEFAULT: "hsl(var(--popover))",
                            foreground: "hsl(var(--popover-foreground))",
                        },
                        card: {
                            DEFAULT: "hsl(var(--card))",
                            foreground: "hsl(var(--card-foreground))",
                        },
                    },
                }
            }
        }
    </script>
    <style>
        :root {
            --background: 0 0% 100%;
            --foreground: 222.2 84% 4.9%;
            --card: 0 0% 100%;
            --card-foreground: 222.2 84% 4.9%;
            --popover: 0 0% 100%;
            --popover-foreground: 222.2 84% 4.9%;
            --primary: 222.2 47.4% 11.2%;
            --primary-foreground: 210 40% 98%;
            --secondary: 210 40% 96%;
            --secondary-foreground: 222.2 84% 4.9%;
            --muted: 210 40% 96%;
            --muted-foreground: 215.4 16.3% 46.9%;
            --accent: 210 40% 96%;
            --accent-foreground: 222.2 84% 4.9%;
            --destructive: 0 84.2% 60.2%;
            --destructive-foreground: 210 40% 98%;
            --border: 214.3 31.8% 91.4%;
            --input: 214.3 31.8% 91.4%;
            --ring: 222.2 84% 4.9%;
            --radius: 0.5rem;
        }

        .dark {
            --background: 222.2 84% 4.9%;
            --foreground: 210 40% 98%;
            --card: 222.2 84% 4.9%;
            --card-foreground: 210 40% 98%;
            --popover: 222.2 84% 4.9%;
            --popover-foreground: 210 40% 98%;
            --primary: 210 40% 98%;
            --primary-foreground: 222.2 47.4% 11.2%;
            --secondary: 217.2 32.6% 17.5%;
            --secondary-foreground: 210 40% 98%;
            --muted: 217.2 32.6% 17.5%;
            --muted-foreground: 215 20.2% 65.1%;
            --accent: 217.2 32.6% 17.5%;
            --accent-foreground: 210 40% 98%;
            --destructive: 0 62.8% 30.6%;
            --destructive-foreground: 210 40% 98%;
            --border: 217.2 32.6% 17.5%;
            --input: 217.2 32.6% 17.5%;
            --ring: 212.7 26.8% 83.9%;
        }

        * {
            border-color: hsl(var(--border));
        }

        body {
            background-color: hsl(var(--background));
            color: hsl(var(--foreground));
        }

        .calculator-container {
            background: hsl(var(--card));
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
        }

        .input-group {
            margin-bottom: 1rem;
        }

        .input-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: hsl(var(--foreground));
        }

        .input-group input, .input-group select {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
            background: hsl(var(--background));
            color: hsl(var(--foreground));
        }

        .input-group input:focus, .input-group select:focus {
            outline: none;
            border-color: hsl(var(--ring));
            box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
        }

        .btn-primary {
            background: hsl(var(--primary));
            color: hsl(var(--primary-foreground));
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--radius);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-primary:hover {
            background: hsl(var(--primary) / 0.9);
        }

        .results-container {
            background: hsl(var(--muted));
            padding: 1.5rem;
            border-radius: var(--radius);
            margin-top: 1rem;
        }

        .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid hsl(var(--border));
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .result-label {
            font-weight: 500;
            color: hsl(var(--foreground));
        }

        .result-value {
            font-weight: 600;
            color: hsl(var(--primary));
            font-size: 1.1rem;
        }

        .result-value.positive {
            color: #16a34a;
        }

        .result-value.negative {
            color: #dc2626;
        }

        .warning-box {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: var(--radius);
            padding: 1rem;
            margin: 1rem 0;
            color: #92400e;
        }

        .info-box {
            background: #dbeafe;
            border: 1px solid #3b82f6;
            border-radius: var(--radius);
            padding: 1rem;
            margin: 1rem 0;
            color: #1e40af;
        }

        @media (max-width: 768px) {
            .calculator-container {
                padding: 1rem;
            }
            .input-group {
                flex-direction: column;
            }
        }
    </style>
</head>

<body class="min-h-screen bg-background font-sans antialiased">
    <div class="relative flex min-h-screen flex-col">
        <!-- PayCal Australia Header Component -->
        <div class="bg-orange-100/90 dark:bg-orange-900/30 border-b border-orange-200 dark:border-orange-800/30 transition-colors duration-500">
            <div class="container mx-auto px-4 py-3">
                <div class="flex items-center justify-between gap-x-4">
                    <div class="flex items-center gap-x-2 flex-1">
                        <span class="hidden sm:inline">💼 New!</span>
                        <span class="text-sm sm:text-base">Calculate your <a class="font-semibold text-orange-600 dark:text-orange-400 hover:underline" href="#calculator">contractor income</a><span class="hidden sm:inline"> - ABN, GST & tax calculations!</span></span>
                    </div>
                    <div class="flex items-center gap-x-1 sm:gap-x-2">
                        <div class="hidden sm:flex items-center gap-x-1 mr-2">
                            <button class="w-2 h-2 rounded-full transition-colors bg-orange-600 dark:text-orange-400" aria-label="Show promotion 1"></button>
                            <button class="w-2 h-2 rounded-full transition-colors bg-muted-foreground/30 hover:bg-orange-600/50" aria-label="Show promotion 2"></button>
                            <button class="w-2 h-2 rounded-full transition-colors bg-muted-foreground/30 hover:bg-orange-600/50" aria-label="Show promotion 3"></button>
                        </div>
                        <a class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-9 rounded-md px-3 hidden sm:inline-flex border-orange-300 dark:border-orange-700 hover:bg-orange-200/70 dark:hover:bg-orange-800/30" href="#calculator">Try Now</a>
                        <a class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-8 w-8 sm:hidden border-orange-300 dark:border-orange-700 hover:bg-orange-200/70 dark:hover:bg-orange-800/30" href="#calculator">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                                <path d="M5 12h14"></path>
                                <path d="m12 5 7 7-7 7"></path>
                            </svg>
                            <span class="sr-only">Try Calculator</span>
                        </a>
                        <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:text-accent-foreground h-8 w-8 hover:bg-orange-200/70 dark:hover:bg-orange-800/30" onclick="dismissBanner()">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4">
                                <path d="M18 6 6 18"></path>
                                <path d="m6 6 12 12"></path>
                            </svg>
                            <span class="sr-only">Dismiss</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <main class="flex-1">
            <div class="container mx-auto px-4 py-8">
                <!-- Hero Section -->
                <div class="text-center mb-8">
                    <h1 class="text-4xl font-bold tracking-tight text-foreground mb-4">
                        Contractor Pay Calculator
                    </h1>
                    <p class="text-xl text-muted-foreground max-w-2xl mx-auto">
                        Calculate your contractor income, tax obligations, GST, and superannuation as an Australian sole trader or company contractor.
                    </p>
                </div>

                <!-- Calculator Section -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
                    <!-- Input Form -->
                    <div class="calculator-container p-6">
                        <h2 class="text-2xl font-semibold mb-4">Contractor Details</h2>

                        <form class="grid gap-4" id="contractor-form">
                            <!-- Income Input -->
                            <div class="input-group">
                                <label for="income">Gross Income</label>
                                <div class="flex border h-10 w-full rounded-md border-input bg-background">
                                    <span class="flex items-center px-3 h-10">$</span>
                                    <input
                                        type="number"
                                        id="income"
                                        class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-l-none m-[-1]"
                                        min="0"
                                        placeholder="Enter your gross income"
                                        value="80000"
                                        oninput="calculateContractorPay()"
                                    />
                                </div>
                            </div>

                            <!-- Time Period -->
                            <div class="input-group">
                                <label for="timePeriod">Time Period</label>
                                <select
                                    id="timePeriod"
                                    class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                    onchange="calculateContractorPay()"
                                >
                                    <option value="Annually" selected>Annually</option>
                                    <option value="Monthly">Monthly</option>
                                    <option value="Weekly">Weekly</option>
                                    <option value="Daily">Daily</option>
                                    <option value="Hourly">Hourly</option>
                                </select>
                            </div>

                            <!-- Business Structure -->
                            <div class="input-group">
                                <label for="businessStructure">Business Structure</label>
                                <select
                                    id="businessStructure"
                                    class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                    onchange="calculateContractorPay()"
                                >
                                    <option value="sole-trader" selected>Sole Trader</option>
                                    <option value="company">Company</option>
                                    <option value="partnership">Partnership</option>
                                </select>
                            </div>

                            <!-- Business Expenses -->
                            <div class="input-group">
                                <label for="businessExpenses">Business Expenses (Annual)</label>
                                <div class="flex border h-10 w-full rounded-md border-input bg-background">
                                    <span class="flex items-center px-3 h-10">$</span>
                                    <input
                                        type="number"
                                        id="businessExpenses"
                                        class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-l-none m-[-1]"
                                        min="0"
                                        placeholder="Enter business expenses"
                                        value="10000"
                                        oninput="calculateContractorPay()"
                                    />
                                </div>
                            </div>

                            <!-- Options -->
                            <div class="border-t pt-4">
                                <h3 class="text-lg font-semibold mb-4">Options</h3>

                                <!-- GST Registered -->
                                <div class="flex items-center justify-between space-x-2 mb-4">
                                    <label class="text-sm font-medium" for="gstRegistered">
                                        GST Registered (10%)
                                    </label>
                                    <button
                                        type="button"
                                        role="switch"
                                        aria-checked="true"
                                        data-state="checked"
                                        id="gstRegistered"
                                        class="peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 bg-primary"
                                        onclick="toggleContractorSwitch(this); calculateContractorPay();"
                                    >
                                        <span class="pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform translate-x-5"></span>
                                    </button>
                                </div>

                                <!-- Income Includes GST -->
                                <div class="flex items-center justify-between space-x-2 mb-4">
                                    <label class="text-sm font-medium" for="incomeIncludesGST">
                                        Income Includes GST
                                    </label>
                                    <button
                                        type="button"
                                        role="switch"
                                        aria-checked="false"
                                        data-state="unchecked"
                                        id="incomeIncludesGST"
                                        class="peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 bg-input"
                                        onclick="toggleContractorSwitch(this); calculateContractorPay();"
                                    >
                                        <span class="pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform translate-x-0"></span>
                                    </button>
                                </div>

                                <!-- Pay Yourself Super -->
                                <div class="flex items-center justify-between space-x-2">
                                    <label class="text-sm font-medium" for="payYourselfSuper">
                                        Pay Yourself Super (11.5%)
                                    </label>
                                    <button
                                        type="button"
                                        role="switch"
                                        aria-checked="true"
                                        data-state="checked"
                                        id="payYourselfSuper"
                                        class="peer inline-flex h-[24px] w-[44px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 bg-primary"
                                        onclick="toggleContractorSwitch(this); calculateContractorPay();"
                                    >
                                        <span class="pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform translate-x-5"></span>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Results Section -->
                    <div class="calculator-container p-6">
                        <h2 class="text-2xl font-semibold mb-4">Your Results</h2>

                        <div class="results-container" id="contractor-results">
                            <div class="result-item">
                                <span class="result-label">Gross Income</span>
                                <span class="result-value" id="contractor-gross">$80,000</span>
                            </div>
                            <div class="result-item">
                                <span class="result-label">GST Collected</span>
                                <span class="result-value positive" id="contractor-gst">+$7,273</span>
                            </div>
                            <div class="result-item">
                                <span class="result-label">Business Expenses</span>
                                <span class="result-value negative" id="contractor-expenses">-$10,000</span>
                            </div>
                            <div class="result-item">
                                <span class="result-label">Taxable Income</span>
                                <span class="result-value" id="contractor-taxable">$70,000</span>
                            </div>
                            <div class="result-item">
                                <span class="result-label">Income Tax</span>
                                <span class="result-value negative" id="contractor-tax">-$14,617</span>
                            </div>
                            <div class="result-item">
                                <span class="result-label">Medicare Levy</span>
                                <span class="result-value negative" id="contractor-medicare">-$1,400</span>
                            </div>
                            <div class="result-item">
                                <span class="result-label">Superannuation</span>
                                <span class="result-value positive" id="contractor-super">+$8,050</span>
                            </div>
                            <div class="result-item border-t-2 border-primary pt-4">
                                <span class="result-label font-semibold">Net Income</span>
                                <span class="result-value positive text-xl font-bold" id="contractor-net">$53,983</span>
                            </div>
                        </div>

                        <!-- Chart Container -->
                        <div class="mt-6">
                            <canvas id="contractorChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="border-t bg-muted/50">
            <div class="container mx-auto px-4 py-8">
                <div class="text-center">
                    <p class="text-sm text-muted-foreground">
                        © 2024 PayCal.com.au. Made for Aussies ❤️
                    </p>
                </div>
            </div>
        </footer>
    </div>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
        let contractorChart = null;

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            calculateContractorPay();
            initializeContractorChart();
        });

        // Calculate contractor pay
        function calculateContractorPay() {
            const income = parseFloat(document.getElementById('income').value || 80000);
            const timePeriod = document.getElementById('timePeriod').value;
            const businessStructure = document.getElementById('businessStructure').value;
            const businessExpenses = parseFloat(document.getElementById('businessExpenses').value || 10000);

            const gstRegistered = getSwitchValue('gstRegistered');
            const incomeIncludesGST = getSwitchValue('incomeIncludesGST');
            const payYourselfSuper = getSwitchValue('payYourselfSuper');

            // Convert to annual
            let annualIncome = income;
            switch(timePeriod) {
                case 'Monthly': annualIncome = income * 12; break;
                case 'Weekly': annualIncome = income * 52; break;
                case 'Daily': annualIncome = income * 250; break;
                case 'Hourly': annualIncome = income * 2000; break;
            }

            // Calculate GST
            let grossIncome = annualIncome;
            let gstCollected = 0;

            if (gstRegistered) {
                if (incomeIncludesGST) {
                    gstCollected = grossIncome / 11;
                    grossIncome = grossIncome - gstCollected;
                } else {
                    gstCollected = grossIncome * 0.1;
                }
            }

            // Calculate taxable income
            const taxableIncome = grossIncome - businessExpenses;

            // Calculate tax (simplified)
            let incomeTax = 0;
            if (taxableIncome > 18200) {
                if (taxableIncome <= 45000) {
                    incomeTax = (taxableIncome - 18200) * 0.19;
                } else if (taxableIncome <= 120000) {
                    incomeTax = 5092 + (taxableIncome - 45000) * 0.325;
                } else if (taxableIncome <= 180000) {
                    incomeTax = 29467 + (taxableIncome - 120000) * 0.37;
                } else {
                    incomeTax = 51667 + (taxableIncome - 180000) * 0.45;
                }
            }

            // Medicare levy
            const medicareLevy = taxableIncome > 24276 ? taxableIncome * 0.02 : 0;

            // Superannuation
            const superannuation = payYourselfSuper ? grossIncome * 0.115 : 0;

            // Net income
            const netIncome = grossIncome - incomeTax - medicareLevy - businessExpenses + superannuation;

            // Convert back to selected period
            const results = convertToTimePeriod({
                grossIncome,
                gstCollected,
                businessExpenses,
                taxableIncome,
                incomeTax,
                medicareLevy,
                superannuation,
                netIncome
            }, timePeriod);

            updateContractorResults(results);
            updateContractorChart(results);
        }

        // Convert to time period
        function convertToTimePeriod(amounts, timePeriod) {
            const divisor = {
                'Annually': 1,
                'Monthly': 12,
                'Weekly': 52,
                'Daily': 250,
                'Hourly': 2000
            }[timePeriod];

            return {
                grossIncome: amounts.grossIncome / divisor,
                gstCollected: amounts.gstCollected / divisor,
                businessExpenses: amounts.businessExpenses / divisor,
                taxableIncome: amounts.taxableIncome / divisor,
                incomeTax: amounts.incomeTax / divisor,
                medicareLevy: amounts.medicareLevy / divisor,
                superannuation: amounts.superannuation / divisor,
                netIncome: amounts.netIncome / divisor
            };
        }

        // Update results
        function updateContractorResults(results) {
            document.getElementById('contractor-gross').textContent = formatCurrency(results.grossIncome);
            document.getElementById('contractor-gst').textContent = '+' + formatCurrency(results.gstCollected);
            document.getElementById('contractor-expenses').textContent = '-' + formatCurrency(results.businessExpenses);
            document.getElementById('contractor-taxable').textContent = formatCurrency(results.taxableIncome);
            document.getElementById('contractor-tax').textContent = '-' + formatCurrency(results.incomeTax);
            document.getElementById('contractor-medicare').textContent = '-' + formatCurrency(results.medicareLevy);
            document.getElementById('contractor-super').textContent = '+' + formatCurrency(results.superannuation);
            document.getElementById('contractor-net').textContent = formatCurrency(results.netIncome);
        }

        // Format currency
        function formatCurrency(amount) {
            return new Intl.NumberFormat('en-AU', {
                style: 'currency',
                currency: 'AUD',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(amount);
        }

        // Get switch value
        function getSwitchValue(switchId) {
            const switchElement = document.getElementById(switchId);
            return switchElement ? switchElement.getAttribute('aria-checked') === 'true' : false;
        }

        // Toggle switch
        function toggleContractorSwitch(element) {
            const isChecked = element.getAttribute('aria-checked') === 'true';
            const newState = !isChecked;

            element.setAttribute('aria-checked', newState);
            element.setAttribute('data-state', newState ? 'checked' : 'unchecked');

            const thumb = element.querySelector('span');
            if (newState) {
                element.classList.add('bg-primary');
                element.classList.remove('bg-input');
                thumb.style.transform = 'translateX(20px)';
            } else {
                element.classList.remove('bg-primary');
                element.classList.add('bg-input');
                thumb.style.transform = 'translateX(0px)';
            }
        }

        // Initialize chart
        function initializeContractorChart() {
            const ctx = document.getElementById('contractorChart').getContext('2d');
            contractorChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Net Income', 'Income Tax', 'Medicare Levy', 'Business Expenses'],
                    datasets: [{
                        data: [53983, 14617, 1400, 10000],
                        backgroundColor: ['#16a34a', '#dc2626', '#f59e0b', '#6b7280'],
                        borderWidth: 2,
                        borderColor: '#ffffff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: { padding: 20, usePointStyle: true }
                        }
                    }
                }
            });
        }

        // Update chart
        function updateContractorChart(results) {
            if (contractorChart) {
                contractorChart.data.datasets[0].data = [
                    results.netIncome,
                    results.incomeTax,
                    results.medicareLevy,
                    results.businessExpenses
                ];
                contractorChart.update();
            }
        }

        // Dismiss banner
        function dismissBanner() {
            const banner = document.querySelector('.bg-orange-100\\/90');
            if (banner) banner.style.display = 'none';
        }
    </script>